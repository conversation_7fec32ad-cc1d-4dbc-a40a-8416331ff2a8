package main

import (
	"log"
	"real-estate-backend/internal/config"
	"real-estate-backend/internal/database"
	"real-estate-backend/internal/handlers"
	"real-estate-backend/internal/middleware"
	"real-estate-backend/internal/repositories"
	"real-estate-backend/internal/services"

	"github.com/gin-gonic/gin"
)

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatal("Failed to load configuration:", err)
	}

	// Initialize database
	db, err := database.Initialize(cfg.Database)
	if err != nil {
		log.Fatal("Failed to initialize database:", err)
	}

	// Run migrations
	if err := database.RunMigrations(db); err != nil {
		log.Fatal("Failed to run migrations:", err)
	}

	// Initialize repositories
	repos := repositories.NewRepositories(db)

	// Initialize services with strategy pattern
	services := services.NewServices(repos, cfg)

	// Initialize handlers
	handlers := handlers.NewHandlers(services)

	// Setup router
	router := setupRouter(handlers, cfg)

	// Start server
	log.Printf("Server starting on port %s", cfg.Server.Port)
	if err := router.Run(":" + cfg.Server.Port); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}

func setupRouter(h *handlers.Handlers, cfg *config.Config) *gin.Engine {
	if cfg.Server.Mode == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()

	// Global middleware
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(middleware.CORS())
	router.Use(middleware.RateLimit())

	// API routes
	api := router.Group("/api/v1")
	{
		// Public routes
		auth := api.Group("/auth")
		{
			auth.POST("/register", h.Auth.Register)
			auth.POST("/login", h.Auth.Login)
		}

		// Public property routes
		api.GET("/properties", h.Property.GetPublicProperties)
		api.GET("/properties/:id", h.Property.GetPropertyByID)
		api.GET("/categories", h.Category.GetCategories)
		api.GET("/memberships", h.Membership.GetMemberships)

		// Protected routes
		protected := api.Group("")
		protected.Use(middleware.AuthRequired())
		{
			// User dashboard
			protected.GET("/dashboard", h.Dashboard.GetUserDashboard)

			// Property management
			protected.POST("/properties", h.Property.CreateProperty)
			protected.PUT("/properties/:id", h.Property.UpdateProperty)
			protected.DELETE("/properties/:id", h.Property.DeleteProperty)
			protected.POST("/properties/:id/upload-images", h.Property.UploadImages)
			protected.GET("/my-properties", h.Property.GetUserProperties)

			// Payment routes
			payment := protected.Group("/payments")
			{
				payment.POST("/create-checkout-session", h.Payment.CreateCheckoutSession)
			}

			// Chatbot
			protected.POST("/chatbot/chat", h.Chatbot.Chat)
		}

		// Admin routes
		admin := api.Group("/admin")
		admin.Use(middleware.AuthRequired())
		admin.Use(middleware.AdminRequired())
		{
			admin.GET("/stats/dashboard", h.Admin.GetDashboardStats)
			admin.GET("/properties/pending", h.Admin.GetPendingProperties)
			admin.POST("/properties/:id/approve", h.Admin.ApproveProperty)
			admin.POST("/properties/:id/reject", h.Admin.RejectProperty)
			admin.GET("/users", h.Admin.GetUsers)
			admin.POST("/users/:id/ban", h.Admin.BanUser)
			admin.POST("/users/:id/unban", h.Admin.UnbanUser)
		}

		// Webhook routes (no auth required)
		api.POST("/payments/webhook", h.Payment.StripeWebhook)
	}

	// Health check
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "ok"})
	})

	return router
}
