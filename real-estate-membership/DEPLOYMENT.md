# 🚀 EC2 Deployment Guide - Java 21 LTS

## ☕ Java 21 LTS Requirements

This project now uses **Java 21 LTS** with Spring Boot 3.4.1.

### Install Java 21 LTS:
```bash
# Run the installation script
chmod +x install-java21.sh
./install-java21.sh

# Test Java 21 compatibility
chmod +x test-java21.sh
./test-java21.sh
```

## Quick Start Commands

```bash
# 🚀 Initialize and start all services
make init

# 🛑 Stop all services  
make stop

# 🧹 Clean up everything
make clean

# 📊 Check status
make ps

# 📝 View logs
make logs
```

## After Pull/Update

```bash
# 1. Pull latest changes
git pull origin main

# 2. Restart services
make restart

# 3. Test deployment
./test-deployment.sh
```

## Environment Variables Fixed

✅ **APP_JWT_SECRET** - Added to docker-compose.yml
✅ **JWT_EXPIRATION** - Added to .env
✅ **GOOGLE_CLIENT_ID/SECRET** - Added to docker-compose.yml  
✅ **AWS_S3_ENDPOINT/CDN_DOMAIN** - Added to docker-compose.yml

## Services

- **🌐 API Server:** `http://YOUR_EC2_IP:8080/api/v1`
- **📚 Swagger UI:** `http://YOUR_EC2_IP:8080/api/v1/swagger-ui/index.html`
- **🔧 Health Check:** `http://YOUR_EC2_IP:8080/api/v1/actuator/health`
- **🌐 Nginx Proxy:** `http://YOUR_EC2_IP/`

## Security Group Requirements

Make sure these ports are open in AWS Security Group:
- **Port 80** (HTTP)
- **Port 443** (HTTPS)
- **Port 8080** (Spring Boot)

## Troubleshooting

```bash
# Check container status
docker ps -a

# View application logs
docker logs realestate-app --tail 50

# Test internal connectivity
docker exec realestate-app curl http://localhost:8080/api/v1/actuator/health

# Restart specific service
docker-compose restart app

# Full cleanup and restart
make clean && make init
```

## Test Script

Run the comprehensive test script:
```bash
./test-deployment.sh
```

This will test:
- ✅ Container health
- ✅ API endpoints
- ✅ Database connectivity
- ✅ Network connectivity
- ✅ Resource usage
