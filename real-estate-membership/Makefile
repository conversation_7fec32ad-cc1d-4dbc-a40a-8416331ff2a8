# =====================================================
# MAKEFILE FOR REAL ESTATE MEMBERSHIP SYSTEM
# Docker Compose Management with AWS S3 Support + RDS
# =====================================================

.PHONY: help init init-rds build build-rds up up-rds down down-rds logs logs-rds clean ps ps-rds restart restart-rds rebuild rebuild-rds

# Default target
help: ## Show this help message
	@echo "🏠 Real Estate Membership System - Docker Commands"
	@echo "=================================================="
	@awk 'BEGIN {FS = ":.*##"; printf "\nUsage:\n  make \033[36m<target>\033[0m\n"} /^[a-zA-Z_-]+:.*?##/ { printf "  \033[36m%-20s\033[0m %s\n", $$1, $$2 } /^##@/ { printf "\n\033[1m%s\033[0m\n", substr($$0, 5) } ' $(MAKEFILE_LIST)

##@ 🚀 Quick Start Commands

init: ## Initialize project (local PostgreSQL)
	@echo "🔧 Initializing Real Estate Membership System..."
	@if [ ! -f .env ]; then \
		echo "📋 Creating .env file..."; \
		cp .env.example .env 2>/dev/null || echo "# Add your environment variables here" > .env; \
		echo "✅ .env file created. Please edit it with your values."; \
	else \
		echo "✅ .env file already exists."; \
	fi
	@$(MAKE) build
	@$(MAKE) up
	@$(MAKE) info

build: ## Build Docker images (local DB)
	@echo "🔨 Building Docker images..."
	@mvn clean package -DskipTests
	@docker-compose build --no-cache
	@echo "✅ Build completed!"

build-rds: ## Build Docker images (AWS RDS)
	@echo "🔨 Building Docker images for RDS..."
	@mvn clean package -DskipTests
	@docker-compose -f docker-compose-rds.yml build --no-cache
	@echo "✅ RDS Build completed!"

up: ## Start services (local PostgreSQL)
	@echo "🚀 Starting all services..."
	@docker-compose up -d
	@echo "✅ All services started!"
	@$(MAKE) ps

up-rds: ## Start services (AWS RDS)
	@echo "🚀 Starting services with AWS RDS..."
	@docker-compose -f docker-compose-rds.yml up -d
	@echo "✅ RDS services started!"
	@$(MAKE) ps-rds

stop: ## Stop services (alias for down)
	@$(MAKE) down

down: ## Stop services (local)
	@echo "🛑 Stopping all services..."
	@docker-compose down
	@echo "✅ All services stopped!"

down-rds: ## Stop services (RDS)
	@echo "🛑 Stopping RDS services..."
	@docker-compose -f docker-compose-rds.yml down
	@echo "✅ RDS services stopped!"

##@ 📊 Monitoring Commands

ps: ## Show running containers (local)
	@echo "📊 Container Status:"
	@docker-compose ps

ps-rds: ## Show running containers (RDS)
	@echo "📊 RDS Container Status:"
	@docker-compose -f docker-compose-rds.yml ps

logs: ## Show logs (local)
	@docker-compose logs -f

logs-rds: ## Show logs (RDS)
	@docker-compose -f docker-compose-rds.yml logs -f

logs-app: ## Show application logs only
	@docker-compose logs -f app

logs-app-rds: ## Show RDS application logs
	@docker-compose -f docker-compose-rds.yml logs -f app

##@ 🔧 Development Commands

restart: ## Restart services (local)
	@echo "🔄 Restarting all services..."
	@docker-compose restart
	@echo "✅ Services restarted!"

restart-rds: ## Restart services (RDS)
	@echo "🔄 Restarting RDS services..."
	@docker-compose -f docker-compose-rds.yml restart
	@echo "✅ RDS Services restarted!"

restart-app: ## Restart app (local)
	@echo "🔄 Restarting application..."
	@docker-compose restart app
	@echo "✅ Application restarted!"

restart-app-rds: ## Restart app (RDS)
	@echo "🔄 Restarting RDS application..."
	@docker-compose -f docker-compose-rds.yml restart app
	@echo "✅ RDS Application restarted!"

rebuild: ## Rebuild and restart (local)
	@echo "🔨 Rebuilding and restarting..."
	@$(MAKE) down
	@$(MAKE) build
	@$(MAKE) up

rebuild-rds: ## Rebuild and restart (RDS)
	@echo "🔨 Rebuilding and restarting RDS..."
	@$(MAKE) down-rds
	@$(MAKE) build-rds
	@$(MAKE) up-rds

##@ 🗄️ Database Commands

db-shell: ## Access PostgreSQL shell (local)
	@echo "🗄️ Connecting to PostgreSQL..."
	@docker-compose exec postgres psql -U postgres -d realestate_membership

redis-cli: ## Access Redis CLI
	@echo "📦 Connecting to Redis..."
	@docker-compose exec redis redis-cli || docker-compose -f docker-compose-rds.yml exec redis redis-cli

redis-flush: ## Clear Redis cache
	@echo "🧹 Clearing Redis cache..."
	@docker-compose exec redis redis-cli FLUSHALL || docker-compose -f docker-compose-rds.yml exec redis redis-cli FLUSHALL
	@echo "✅ Redis cache cleared!"

##@ 🧹 Cleanup Commands

clean: ## Stop and remove everything (local)
	@echo "🧹 Cleaning up everything..."
	@docker-compose down --volumes --remove-orphans
	@docker system prune -f
	@echo "✅ Cleanup completed!"

clean-rds: ## Stop and remove everything (RDS)
	@echo "🧹 Cleaning up RDS setup..."
	@docker-compose -f docker-compose-rds.yml down --volumes --remove-orphans
	@docker system prune -f
	@echo "✅ RDS Cleanup completed!"

##@ ℹ️ Information Commands

info: ## Show system info (local PostgreSQL)
	@echo ""
	@echo "🏠 Real Estate Membership System - Local Environment"
	@echo "====================================================="
	@echo "📱 Services Available:"
	@echo "   🌐 API Server:      http://localhost:8080/api/v1"
	@echo "   📚 API Docs:        http://localhost:8080/api/v1/swagger-ui/index.html"
	@echo "   🔧 Health Check:    http://localhost:8080/api/v1/actuator/health"
	@echo "   🗄️ PostgreSQL:      localhost:5432"
	@echo "   📦 Redis:           localhost:6379"
	@echo "   🌐 Nginx Proxy:     http://localhost:80"
	@echo ""
	@echo "🔧 Management Commands:"
	@echo "   📊 View logs:       make logs"
	@echo "   🔄 Restart:         make restart"
	@echo "   🛑 Stop:            make down"
	@echo ""
	@echo "✅ System ready for development!"

info-rds: ## Show system info (AWS RDS)
	@echo ""
	@echo "🏠 Real Estate Membership System - AWS RDS Environment"
	@echo "======================================================="
	@echo "📱 Services Available:"
	@echo "   🌐 API Server:      http://localhost:8080/api/v1"
	@echo "   📚 API Docs:        http://localhost:8080/api/v1/swagger-ui/index.html"
	@echo "   🔧 Health Check:    http://localhost:8080/api/v1/actuator/health"
	@echo "   🗄️ PostgreSQL:      AWS RDS (realestate-membership.cvq0uqo4wdkq...)"
	@echo "   📦 Redis:           localhost:6379"
	@echo "   🌐 Nginx Proxy:     http://localhost:80"
	@echo "   ☁️ S3 Bucket:       realestate-s3-do-an-4"
	@echo ""
	@echo "🔧 Management Commands:"
	@echo "   📊 View logs:       make logs-rds"
	@echo "   🔄 Restart:         make restart-rds"
	@echo "   🛑 Stop:            make down-rds"
	@echo ""
	@echo "✅ AWS RDS System ready!"

health: ## Check health of services
	@echo "🏥 Checking service health..."
	@echo "📊 Container Status:"
	@docker-compose ps 2>/dev/null || docker-compose -f docker-compose-rds.yml ps
	@echo ""
	@echo "🔧 Application Health:"
	@curl -s http://localhost:8080/api/v1/actuator/health | jq . 2>/dev/null || curl -s http://localhost:8080/api/v1/actuator/health || echo "❌ Application not responding"
	@echo ""
	@echo "📦 Redis Health:"
	@docker-compose exec redis redis-cli ping 2>/dev/null || docker-compose -f docker-compose-rds.yml exec redis redis-cli ping 2>/dev/null || echo "❌ Redis not responding"

##@ 🚀 Production Commands

prod-build: ## Build for production
	@echo "🏭 Building for production..."
	@mvn clean package -Pprod -DskipTests
	@docker build -t realestate-membership:prod .
	@echo "✅ Production build completed!"

##@ 📈 Development Tools

dev: ## Start development environment
	@echo "🔥 Starting development environment..."
	@docker-compose --profile development up -d
	@echo "✅ Development environment started!"

test: ## Run tests
	@echo "🧪 Running tests..."
	@mvn test
	@echo "✅ Tests completed!"

##@ 🆘 Common Issues

fix-port: ## Kill process using port 5432
	@echo "🔧 Killing processes on port 5432..."
	@lsof -ti:5432 | xargs kill -9 2>/dev/null || echo "No processes found on port 5432"
	@echo "✅ Port 5432 cleared!"

fix-permissions: ## Fix Docker permissions
	@echo "🔧 Fixing Docker permissions..."
	@sudo chown -R $(USER):$(USER) .
	@echo "✅ Permissions fixed!"

##@ 📖 Documentation

docs: ## Generate documentation
	@echo "📖 Generating documentation..."
	@mvn javadoc:javadoc
	@echo "✅ Documentation generated in target/site/apidocs/"

##@ Quick Commands for RDS

rds: ## Quick start with RDS (shortcut for init-rds)
	@$(MAKE) init-rds

local: ## Quick start with local DB (shortcut for init)
	@$(MAKE) init
