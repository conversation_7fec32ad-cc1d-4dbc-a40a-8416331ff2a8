# Minimal test properties for CI/CD
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driver-class-name=org.h2.Driver
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect

# Mock JWT
app.jwt.secret=testSecretKeyForCIEnvironmentWithAtLeast256BitsLength12345678901234567890
app.jwt.expiration=86400000

# Mock external services
openai.api.key=mock-key
aws.s3.enabled=false

# Disable problematic features
management.endpoints.enabled-by-default=false
springdoc.api-docs.enabled=false

# Silent logging
logging.level.root=ERROR
logging.level.com.realestate.membership=WARN
