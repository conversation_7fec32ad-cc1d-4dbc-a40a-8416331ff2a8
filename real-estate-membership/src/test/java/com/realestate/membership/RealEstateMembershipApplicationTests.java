package com.realestate.membership;

import com.realestate.membership.service.OpenAIService;
import com.realestate.membership.service.tools.PropertySearchTool;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
@ActiveProfiles("test")
@TestPropertySource(properties = {
    "openai.api.key=mock-key",
    "spring.datasource.url=jdbc:h2:mem:testdb",
    "spring.jpa.hibernate.ddl-auto=create-drop",
    "app.jwt.secret=testSecretKeyForCIEnvironmentWithAtLeast256BitsLength12345678901234567890",
    "logging.level.root=WARN"
})
class RealEstateMembershipApplicationTests {

    @MockBean
    private OpenAIService openAIService;
    
    @MockBean
    private PropertySearchTool propertySearchTool;

    @Test
    void applicationContextLoads() {
        // Simple smoke test to verify Spring context loads
        // MockBean automatically replaces problematic services
    }

}
