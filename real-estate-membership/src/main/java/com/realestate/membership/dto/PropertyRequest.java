package com.realestate.membership.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.realestate.membership.entity.Property;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class PropertyRequest {
    
    @NotNull(message = "Category ID is required")
    @JsonProperty("categoryId")
    private Long categoryId;
    
    @NotBlank(message = "Title is required")
    @JsonProperty("title")
    private String title;
    
    @JsonProperty("description")
    private String description;
    
    @NotNull(message = "Price is required")
    @DecimalMin(value = "0.0", inclusive = false, message = "Price must be greater than 0")
    @JsonProperty("price")
    private BigDecimal price;
    
    @NotBlank(message = "Address is required")
    @JsonProperty("address")
    private String address;
    
    @NotBlank(message = "City is required")
    @JsonProperty("city")
    private String city;
    
    @NotBlank(message = "District is required")
    @JsonProperty("district")
    private String district;
    
    @NotBlank(message = "Ward is required")
    @JsonProperty("ward")
    private String ward;
    
    @JsonProperty("latitude")
    private BigDecimal latitude;
    
    @JsonProperty("longitude")
    private BigDecimal longitude;
    
    @JsonProperty("propertyArea")
    private BigDecimal propertyArea;
    
    @JsonProperty("landArea")
    private BigDecimal landArea;
    
    @JsonProperty("bedrooms")
    private Integer bedrooms;
    
    @JsonProperty("bathrooms")
    private Integer bathrooms;
    
    @JsonProperty("floors")
    private Integer floors;
    
    @NotNull(message = "Property type is required")
    @JsonProperty("propertyType")
    private Property.PropertyType propertyType;
    
    @NotNull(message = "Listing type is required")
    @JsonProperty("listingType")
    private Property.ListingType listingType;
}
