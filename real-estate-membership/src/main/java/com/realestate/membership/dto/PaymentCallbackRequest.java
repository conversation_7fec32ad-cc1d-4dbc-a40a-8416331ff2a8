package com.realestate.membership.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PaymentCallbackRequest {
    
    // Common fields
    private String orderId;
    private String transactionId;
    private BigDecimal amount;
    private String resultCode;
    private String message;
    private String signature;
    private String requestId;
    
    // VNPay specific fields
    private String vnp_TmnCode;
    private String vnp_Amount;
    private String vnp_BankCode;
    private String vnp_BankTranNo;
    private String vnp_CardType;
    private String vnp_OrderInfo;
    private String vnp_PayDate;
    private String vnp_ResponseCode;
    private String vnp_TxnRef;
    private String vnp_SecureHashType;
    private String vnp_SecureHash;
    private String vnp_TransactionNo;
    private String vnp_TransactionStatus;
    
    // MoMo specific fields
    private String partnerCode;
    private String accessKey;
    private String orderType;
    private String extraData;
    private String payType;
    private String responseTime;
    private Long transId;
    private String orderInfo;
    
    // Additional metadata
    private Map<String, String> additionalData;
    private String rawData;
    private String ipAddress;
    private String userAgent;
    private Long timestamp;
    
    // Helper methods for validation
    public boolean isVNPayCallback() {
        return vnp_TmnCode != null && !vnp_TmnCode.trim().isEmpty();
    }
    
    public boolean isMoMoCallback() {
        return partnerCode != null && !partnerCode.trim().isEmpty();
    }
    
    public boolean isSuccessfulVNPay() {
        return "00".equals(vnp_ResponseCode);
    }
    
    public boolean isSuccessfulMoMo() {
        return "0".equals(resultCode) || "00".equals(resultCode);
    }
    
    public String getGatewayTransactionId() {
        if (isVNPayCallback()) {
            return vnp_TransactionNo;
        } else if (isMoMoCallback()) {
            return String.valueOf(transId);
        }
        return transactionId;
    }
    
    public String getGatewayOrderId() {
        if (isVNPayCallback()) {
            return vnp_TxnRef;
        } else if (isMoMoCallback()) {
            return orderId;
        }
        return orderId;
    }
    
    public BigDecimal getPaymentAmount() {
        if (isVNPayCallback() && vnp_Amount != null) {
            try {
                // VNPay amount is in cents (VND * 100)
                return new BigDecimal(vnp_Amount).divide(new BigDecimal(100));
            } catch (NumberFormatException e) {
                return BigDecimal.ZERO;
            }
        }
        return amount != null ? amount : BigDecimal.ZERO;
    }
    
    public String getPaymentMessage() {
        if (isVNPayCallback()) {
            return getVNPayMessage(vnp_ResponseCode);
        } else if (isMoMoCallback()) {
            return message;
        }
        return message;
    }
    
    private String getVNPayMessage(String responseCode) {
        switch (responseCode) {
            case "00": return "Giao dịch thành công";
            case "07": return "Trừ tiền thành công. Giao dịch bị nghi ngờ (liên quan tới lừa đảo, giao dịch bất thường)";
            case "09": return "Giao dịch không thành công do: Thẻ/Tài khoản của khách hàng chưa đăng ký dịch vụ InternetBanking tại ngân hàng";
            case "10": return "Giao dịch không thành công do: Khách hàng xác thực thông tin thẻ/tài khoản không đúng quá 3 lần";
            case "11": return "Giao dịch không thành công do: Đã hết hạn chờ thanh toán";
            case "12": return "Giao dịch không thành công do: Thẻ/Tài khoản của khách hàng bị khóa";
            case "13": return "Giao dịch không thành công do Quý khách nhập sai mật khẩu xác thực giao dịch (OTP)";
            case "24": return "Giao dịch không thành công do: Khách hàng hủy giao dịch";
            case "51": return "Giao dịch không thành công do: Tài khoản của quý khách không đủ số dư để thực hiện giao dịch";
            case "65": return "Giao dịch không thành công do: Tài khoản của Quý khách đã vượt quá hạn mức giao dịch trong ngày";
            case "75": return "Ngân hàng thanh toán đang bảo trì";
            case "79": return "Giao dịch không thành công do: KH nhập sai mật khẩu thanh toán quá số lần quy định";
            default: return "Giao dịch thất bại";
        }
    }
} 