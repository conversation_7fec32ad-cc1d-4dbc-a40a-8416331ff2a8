package com.realestate.membership.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class LoginRequest {
    
    @JsonProperty("usernameOrEmail")
    private String usernameOrEmail;
    
    @JsonProperty("username") 
    private String username;
    
    @JsonProperty("email")
    private String email;
    
    @NotBlank(message = "Password is required")
    private String password;
    
    // Helper method to get the login identifier
    public String getUsernameOrEmail() {
        if (usernameOrEmail != null && !usernameOrEmail.trim().isEmpty()) {
            return usernameOrEmail.trim();
        }
        if (username != null && !username.trim().isEmpty()) {
            return username.trim();
        }
        if (email != null && !email.trim().isEmpty()) {
            return email.trim();
        }
        return null;
    }
    
    // Custom validation
    public boolean isValid() {
        return getUsernameOrEmail() != null && password != null && !password.trim().isEmpty();
    }
}
