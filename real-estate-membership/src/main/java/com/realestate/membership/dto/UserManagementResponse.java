package com.realestate.membership.dto;

import com.realestate.membership.entity.User;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserManagementResponse {
    
    // =====================================================
    // BASIC USER INFORMATION
    // =====================================================
    private Long id;
    private String username;
    private String email;
    private String firstName;
    private String lastName;
    private String phoneNumber;
    private String avatarUrl;
    
    // =====================================================
    // STATUS & ROLE
    // =====================================================
    private User.Role role;
    private User.UserStatus status;
    private Boolean emailVerified;
    private User.OAuthProvider oauthProvider;
    private String googleId;
    
    // =====================================================
    // TIMESTAMPS
    // =====================================================
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private LocalDateTime lastLoginAt;
    private LocalDateTime emailVerifiedAt;
    
    // =====================================================
    // ACTIVITY STATISTICS
    // =====================================================
    private UserActivityStats activityStats;
    
    // =====================================================
    // MEMBERSHIP INFORMATION
    // =====================================================
    private UserMembershipInfo membershipInfo;
    
    // =====================================================
    // PROPERTY STATISTICS
    // =====================================================
    private UserPropertyStats propertyStats;
    
    // =====================================================
    // ADMIN ACTIONS HISTORY
    // =====================================================
    private List<AdminAction> adminActions;
    
    // =====================================================
    // SYSTEM FLAGS
    // =====================================================
    private Boolean isOnline;
    private String lastIpAddress;
    private String userAgent;
    private Integer loginAttempts;
    private LocalDateTime lastFailedLogin;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserActivityStats {
        private Long totalLogins;
        private Long totalProperties;
        private Long totalChatSessions;
        private Long totalPayments;
        private LocalDateTime lastActivity;
        private Integer daysActive;
        private Double averageSessionDuration;
        private Long totalViews;
        private Long totalContacts;
        private String mostActiveDay;
        private String preferredDevice;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserMembershipInfo {
        private Boolean hasActiveMembership;
        private String currentMembershipType;
        private String currentMembershipName;
        private LocalDateTime membershipStartDate;
        private LocalDateTime membershipEndDate;
        private Integer propertiesUsed;
        private Integer maxProperties;
        private Integer membershipHistoryCount;
        private Boolean autoRenewal;
        private LocalDateTime nextRenewalDate;
        private String membershipStatus;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserPropertyStats {
        private Long totalProperties;
        private Long approvedProperties;
        private Long pendingProperties;
        private Long rejectedProperties;
        private Long featuredProperties;
        private Long totalViews;
        private Long totalContacts;
        private Double averagePropertyValue;
        private String mostPopularPropertyType;
        private String preferredCity;
        private LocalDateTime lastPropertySubmission;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AdminAction {
        private Long id;
        private String actionType; // BAN, UNBAN, ROLE_CHANGE, DELETE_PROPERTY, etc.
        private String description;
        private String reason;
        private String adminUsername;
        private Long adminId;
        private LocalDateTime timestamp;
        private String severity; // INFO, WARNING, CRITICAL
        private Boolean isReversible;
        private LocalDateTime expiresAt; // For temporary bans
        private String metadata;
    }

    // =====================================================
    // HELPER METHODS
    // =====================================================
    
    public String getFullName() {
        if (firstName != null && lastName != null) {
            return firstName + " " + lastName;
        } else if (firstName != null) {
            return firstName;
        } else if (lastName != null) {
            return lastName;
        }
        return username;
    }
    
    public Boolean isBanned() {
        return status == User.UserStatus.BANNED;
    }
    
    public Boolean isActive() {
        return status == User.UserStatus.ACTIVE;
    }
    
    public Boolean isOAuthUser() {
        return oauthProvider != null && oauthProvider != User.OAuthProvider.LOCAL;
    }
    
    public Integer getDaysSinceRegistration() {
        if (createdAt == null) return 0;
        return (int) java.time.temporal.ChronoUnit.DAYS.between(createdAt, LocalDateTime.now());
    }
    
    public Integer getDaysSinceLastLogin() {
        if (lastLoginAt == null) return -1;
        return (int) java.time.temporal.ChronoUnit.DAYS.between(lastLoginAt, LocalDateTime.now());
    }
    
    public String getAccountAge() {
        int days = getDaysSinceRegistration();
        if (days < 7) return days + " ngày";
        else if (days < 30) return (days / 7) + " tuần";
        else if (days < 365) return (days / 30) + " tháng";
        else return (days / 365) + " năm";
    }
    
    public String getRiskLevel() {
        if (isBanned()) return "BANNED";
        if (loginAttempts != null && loginAttempts > 5) return "HIGH";
        if (getDaysSinceLastLogin() > 30) return "INACTIVE";
        if (propertyStats != null && propertyStats.getRejectedProperties() > propertyStats.getApprovedProperties()) return "MEDIUM";
        return "LOW";
    }
} 