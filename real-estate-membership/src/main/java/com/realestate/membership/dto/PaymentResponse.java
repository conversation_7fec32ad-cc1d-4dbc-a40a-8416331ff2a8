package com.realestate.membership.dto;

import com.realestate.membership.entity.Payment;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PaymentResponse {
    
    private Long id;
    private Long userId;
    private String userName;
    private Long membershipId;
    private String membershipName;
    private String membershipType;
    
    private BigDecimal amount;
    private Payment.PaymentMethod paymentMethod;
    private Payment.PaymentStatus status;
    private String description;
    
    // Transaction identifiers
    private String orderId;
    private String transactionId;
    private String gatewayTransactionId;
    
    // Gateway response details
    private String gatewayResponseCode;
    private String gatewayMessage;
    private String bankCode;
    
    // Payment processing details
    private BigDecimal processingFee;
    private String currency = "VND";
    private String paymentUrl;
    private String qrCodeUrl;
    
    // Timestamps
    private LocalDateTime paymentDate;
    private LocalDateTime completedAt;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Error handling
    private String failureReason;
    private String errorCode;
    
    // Additional metadata
    private Boolean isRefundable;
    private Boolean isCancellable;
    private String displayStatus;
    private String statusColor;
    
    // Helper methods
    public String getDisplayStatus() {
        if (displayStatus != null) return displayStatus;
        
        return switch (status) {
            case PENDING -> "Đang xử lý";
            case COMPLETED -> "Thành công";
            case FAILED -> "Thất bại";
            case CANCELLED -> "Đã hủy";
            case REFUNDED -> "Đã hoàn tiền";
        };
    }
    
    public String getStatusColor() {
        if (statusColor != null) return statusColor;
        
        return switch (status) {
            case PENDING -> "warning";
            case COMPLETED -> "success";
            case FAILED -> "danger";
            case CANCELLED -> "secondary";
            case REFUNDED -> "info";
        };
    }
    
    public Boolean getIsRefundable() {
        if (isRefundable != null) return isRefundable;
        
        return status == Payment.PaymentStatus.COMPLETED && 
               amount != null && 
               amount.compareTo(BigDecimal.ZERO) > 0;
    }
    
    public Boolean getIsCancellable() {
        if (isCancellable != null) return isCancellable;
        
        return status == Payment.PaymentStatus.PENDING;
    }
    
    public String getPaymentMethodDisplayName() {
        return switch (paymentMethod) {
            case VNPAY -> "VNPay";
            case MOMO -> "MoMo";
            case STRIPE -> "Stripe";
            case BANK_TRANSFER -> "Chuyển khoản ngân hàng";
            case CASH -> "Tiền mặt";
            case WALLET -> "Ví điện tử";
        };
    }
    
    public String getFormattedAmount() {
        if (amount == null) return "0 VND";
        
        return String.format("%,.0f VND", amount);
    }
    
    public String getFormattedProcessingFee() {
        if (processingFee == null) return "0 VND";
        
        return String.format("%,.0f VND", processingFee);
    }
    
    public Boolean isSuccessful() {
        return status == Payment.PaymentStatus.COMPLETED;
    }
    
    public Boolean isPending() {
        return status == Payment.PaymentStatus.PENDING;
    }
    
    public Boolean isFailed() {
        return status == Payment.PaymentStatus.FAILED;
    }
} 