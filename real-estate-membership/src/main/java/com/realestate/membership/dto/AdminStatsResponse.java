package com.realestate.membership.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AdminStatsResponse {
    
    // =====================================================
    // OVERVIEW STATISTICS
    // =====================================================
    private OverviewStats overview;
    
    // =====================================================
    // REVENUE STATISTICS
    // =====================================================
    private RevenueStats revenue;
    
    // =====================================================
    // USER STATISTICS
    // =====================================================
    private UserStats users;
    
    // =====================================================
    // PROPERTY STATISTICS
    // =====================================================
    private PropertyStats properties;
    
    // =====================================================
    // MEMBERSHIP STATISTICS
    // =====================================================
    private MembershipStats memberships;
    
    // =====================================================
    // SYSTEM HEALTH
    // =====================================================
    private SystemHealth systemHealth;
    
    // =====================================================
    // RECENT ACTIVITIES
    // =====================================================
    private List<RecentActivity> recentActivities;
    
    // =====================================================
    // CHART DATA
    // =====================================================
    private Map<String, Object> chartData;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OverviewStats {
        private Long totalUsers;
        private Long totalProperties;
        private Long totalActiveMemberships;
        private BigDecimal totalRevenue;
        private Long pendingApprovals;
        private Long todayRegistrations;
        private Long todayProperties;
        private BigDecimal todayRevenue;
        private Double systemUptime;
        private String lastUpdateTime;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RevenueStats {
        private BigDecimal totalRevenue;
        private BigDecimal monthlyRevenue;
        private BigDecimal weeklyRevenue;
        private BigDecimal dailyRevenue;
        private BigDecimal previousMonthRevenue;
        private Double monthlyGrowthRate;
        private List<RevenueByPeriod> revenueByMonth;
        private List<RevenueByMembership> revenueByMembership;
        private BigDecimal averageOrderValue;
        private Long totalTransactions;
        private Long successfulTransactions;
        private Double successRate;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserStats {
        private Long totalUsers;
        private Long activeUsers;
        private Long bannedUsers;
        private Long newUsersToday;
        private Long newUsersThisWeek;
        private Long newUsersThisMonth;
        private Double userGrowthRate;
        private List<UsersByPeriod> userGrowthChart;
        private Map<String, Long> usersByStatus;
        private Map<String, Long> usersByRole;
        private Double averageUserLifetime;
        private Long usersWithActiveSubscriptions;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PropertyStats {
        private Long totalProperties;
        private Long approvedProperties;
        private Long pendingProperties;
        private Long rejectedProperties;
        private Long featuredProperties;
        private Long propertiesThisMonth;
        private Double approvalRate;
        private List<PropertiesByPeriod> propertyGrowthChart;
        private Map<String, Long> propertiesByType;
        private Map<String, Long> propertiesByCity;
        private Map<String, Long> propertiesByStatus;
        private Double averagePropertyValue;
        private Long totalViews;
        private Long totalContacts;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MembershipStats {
        private Long totalActiveMemberships;
        private Long totalExpiredMemberships;
        private Map<String, Long> membershipsByType;
        private Map<String, BigDecimal> revenueByMembershipType;
        private List<MembershipTrend> membershipTrends;
        private Double renewalRate;
        private Double churnRate;
        private BigDecimal averageSubscriptionValue;
        private Long membershipsPendingRenewal;
        private Long membershipsCancelledThisMonth;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SystemHealth {
        private String status; // HEALTHY, WARNING, CRITICAL
        private Double cpuUsage;
        private Double memoryUsage;
        private Double diskUsage;
        private Long activeConnections;
        private Double responseTime;
        private String lastBackup;
        private List<String> alerts;
        private Boolean maintenanceMode;
        private LocalDateTime lastHealthCheck;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RecentActivity {
        private String type; // USER_REGISTERED, PROPERTY_SUBMITTED, PAYMENT_COMPLETED, etc.
        private String description;
        private String userName;
        private Long userId;
        private Long entityId;
        private LocalDateTime timestamp;
        private String severity; // INFO, WARNING, ERROR
        private Map<String, Object> metadata;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RevenueByPeriod {
        private String period; // "2024-01", "2024-02", etc.
        private BigDecimal amount;
        private Long transactionCount;
        private BigDecimal averageOrderValue;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RevenueByMembership {
        private String membershipType;
        private String membershipName;
        private BigDecimal totalRevenue;
        private Long subscriptionCount;
        private BigDecimal averagePrice;
        private Double percentage;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UsersByPeriod {
        private String period; // "2024-01-01", "2024-01-02", etc.
        private Long newUsers;
        private Long totalUsers;
        private Long activeUsers;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PropertiesByPeriod {
        private String period;
        private Long newProperties;
        private Long approvedProperties;
        private Long rejectedProperties;
        private Long totalProperties;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MembershipTrend {
        private String period;
        private String membershipType;
        private Long newSubscriptions;
        private Long renewals;
        private Long cancellations;
        private Long activeSubscriptions;
        private BigDecimal revenue;
    }
} 