package com.realestate.membership.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "ai_agents")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AIAgent {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(unique = true, nullable = false, length = 50)
    private String name;
    
    @Column(name = "display_name", nullable = false, length = 100)
    private String displayName;
    
    @Column(columnDefinition = "TEXT")
    private String description;
    
    @Column(name = "prompt_template", columnDefinition = "TEXT", nullable = false)
    private String promptTemplate;
    
    @Column(name = "system_prompt", columnDefinition = "TEXT")
    private String systemPrompt;
    
    @Column(name = "is_active")
    private Boolean isActive = true;
    
    private Integer priority = 1;
    
    @Column(name = "api_provider", length = 30)
    private String apiProvider = "openai";
    
    @Column(name = "model_name", length = 50)
    private String modelName = "gpt-3.5-turbo";
    
    @Column(name = "max_tokens")
    private Integer maxTokens = 1000;
    
    @Column(precision = 2, scale = 1)
    private BigDecimal temperature = BigDecimal.valueOf(0.7);
    
    @Column(name = "response_format", length = 20)
    private String responseFormat = "text";
    
    @Column(name = "created_at")
    private LocalDateTime createdAt = LocalDateTime.now();
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt = LocalDateTime.now();
    
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
}