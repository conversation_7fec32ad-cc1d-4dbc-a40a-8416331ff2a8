package com.realestate.membership.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Entity
@Table(name = "property_boosts")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PropertyBoost {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "property_id", nullable = false)
    private Property property;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    
    @Column(name = "boost_type")
    @Enumerated(EnumType.STRING)
    private BoostType boostType = BoostType.PUSH_TOP;
    
    @Column(name = "boost_start")
    private LocalDateTime boostStart;
    
    @Column(name = "boost_end")
    private LocalDateTime boostEnd;
    
    @Column(name = "is_active")
    private Boolean isActive = true;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
        if (boostStart == null) {
            boostStart = LocalDateTime.now();
        }
        if (boostEnd == null) {
            boostEnd = LocalDateTime.now().plusDays(7); // Default 7 days
        }
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    public boolean isCurrentlyActive() {
        LocalDateTime now = LocalDateTime.now();
        return isActive && 
               boostStart != null && 
               boostEnd != null &&
               now.isAfter(boostStart) && 
               now.isBefore(boostEnd);
    }
    
    public enum BoostType {
        PUSH_TOP, FEATURED, PREMIUM_LISTING
    }
}
