package com.realestate.membership.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.time.YearMonth;

@Entity
@Table(name = "user_monthly_usage", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"user_id", "year_month"}))
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserMonthlyUsage {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    
    @Column(name = "year_month", nullable = false)
    private String yearMonth; // Format: "2024-12"
    
    @Column(name = "push_top_used")
    private Integer pushTopUsed = 0;
    
    @Column(name = "ai_content_used")
    private Integer aiContentUsed = 0;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
        if (yearMonth == null) {
            yearMonth = YearMonth.now().toString();
        }
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    public static String getCurrentYearMonth() {
        return YearMonth.now().toString();
    }
    
    public boolean canUsePushTop(Integer monthlyLimit) {
        return pushTopUsed < monthlyLimit;
    }
    
    public void incrementPushTopUsage() {
        this.pushTopUsed++;
        this.updatedAt = LocalDateTime.now();
    }
    
    public void incrementAiContentUsage() {
        this.aiContentUsed++;
        this.updatedAt = LocalDateTime.now();
    }
}
