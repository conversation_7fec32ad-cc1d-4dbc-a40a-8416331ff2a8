package com.realestate.membership.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Entity
@Table(name = "admin_actions")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AdminAction {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "admin_id", nullable = false)
    private User admin;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "target_user_id")
    private User targetUser;
    
    @Column(name = "action_type", nullable = false, length = 50)
    private String actionType;
    
    @Column(name = "entity_type", length = 50)
    private String entityType; // USER, PROPERTY, MEMBERSHIP, SYSTEM
    
    @Column(name = "entity_id")
    private Long entityId;
    
    @Column(columnDefinition = "TEXT")
    private String description;
    
    @Column(columnDefinition = "TEXT")
    private String reason;
    
    @Column(name = "old_value", columnDefinition = "TEXT")
    private String oldValue;
    
    @Column(name = "new_value", columnDefinition = "TEXT")
    private String newValue;
    
    @Enumerated(EnumType.STRING)
    private ActionSeverity severity = ActionSeverity.INFO;
    
    @Column(name = "is_reversible")
    private Boolean isReversible = true;
    
    @Column(name = "expires_at")
    private LocalDateTime expiresAt;
    
    @Column(name = "metadata", columnDefinition = "TEXT")
    private String metadata;
    
    @Column(name = "ip_address", length = 45)
    private String ipAddress;
    
    @Column(name = "user_agent", length = 500)
    private String userAgent;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
    }
    
    public enum ActionSeverity {
        INFO, WARNING, CRITICAL
    }
    
    // Static factory methods for common actions
    public static AdminAction createPropertyApproval(User admin, Property property, String reason) {
        AdminAction action = new AdminAction();
        action.setAdmin(admin);
        action.setTargetUser(property.getUser());
        action.setActionType("PROPERTY_APPROVED");
        action.setEntityType("PROPERTY");
        action.setEntityId(property.getId());
        action.setDescription("Property '" + property.getTitle() + "' has been approved");
        action.setReason(reason);
        action.setOldValue("PENDING");
        action.setNewValue("APPROVED");
        action.setSeverity(ActionSeverity.INFO);
        return action;
    }
    
    public static AdminAction createPropertyRejection(User admin, Property property, String reason) {
        AdminAction action = new AdminAction();
        action.setAdmin(admin);
        action.setTargetUser(property.getUser());
        action.setActionType("PROPERTY_REJECTED");
        action.setEntityType("PROPERTY");
        action.setEntityId(property.getId());
        action.setDescription("Property '" + property.getTitle() + "' has been rejected");
        action.setReason(reason);
        action.setOldValue("PENDING");
        action.setNewValue("REJECTED");
        action.setSeverity(ActionSeverity.WARNING);
        return action;
    }
    
    public static AdminAction createUserBan(User admin, User targetUser, String reason, Integer durationDays) {
        AdminAction action = new AdminAction();
        action.setAdmin(admin);
        action.setTargetUser(targetUser);
        action.setActionType("USER_BANNED");
        action.setEntityType("USER");
        action.setEntityId(targetUser.getId());
        action.setDescription("User '" + targetUser.getUsername() + "' has been banned");
        action.setReason(reason);
        action.setOldValue("ACTIVE");
        action.setNewValue("BANNED");
        action.setSeverity(ActionSeverity.CRITICAL);
        action.setIsReversible(true);
        if (durationDays != null) {
            action.setExpiresAt(LocalDateTime.now().plusDays(durationDays));
        }
        return action;
    }
    
    public static AdminAction createUserUnban(User admin, User targetUser, String reason) {
        AdminAction action = new AdminAction();
        action.setAdmin(admin);
        action.setTargetUser(targetUser);
        action.setActionType("USER_UNBANNED");
        action.setEntityType("USER");
        action.setEntityId(targetUser.getId());
        action.setDescription("User '" + targetUser.getUsername() + "' has been unbanned");
        action.setReason(reason);
        action.setOldValue("BANNED");
        action.setNewValue("ACTIVE");
        action.setSeverity(ActionSeverity.INFO);
        return action;
    }
    
    public static AdminAction createRoleChange(User admin, User targetUser, User.Role oldRole, User.Role newRole, String reason) {
        AdminAction action = new AdminAction();
        action.setAdmin(admin);
        action.setTargetUser(targetUser);
        action.setActionType("ROLE_CHANGED");
        action.setEntityType("USER");
        action.setEntityId(targetUser.getId());
        action.setDescription("User '" + targetUser.getUsername() + "' role changed from " + oldRole + " to " + newRole);
        action.setReason(reason);
        action.setOldValue(oldRole.toString());
        action.setNewValue(newRole.toString());
        action.setSeverity(ActionSeverity.WARNING);
        return action;
    }
    
    public static AdminAction createPropertyDeletion(User admin, Property property, String reason) {
        AdminAction action = new AdminAction();
        action.setAdmin(admin);
        action.setTargetUser(property.getUser());
        action.setActionType("PROPERTY_DELETED");
        action.setEntityType("PROPERTY");
        action.setEntityId(property.getId());
        action.setDescription("Property '" + property.getTitle() + "' has been deleted by admin");
        action.setReason(reason);
        action.setOldValue(property.getStatus().toString());
        action.setNewValue("DELETED");
        action.setSeverity(ActionSeverity.CRITICAL);
        action.setIsReversible(false);
        return action;
    }
} 