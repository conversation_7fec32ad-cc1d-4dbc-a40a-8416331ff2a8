package com.realestate.membership.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Entity
@Table(name = "memberships")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Membership {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(unique = true, nullable = false)
    private String name;
    
    @Column(columnDefinition = "TEXT")
    private String description;
    
    @Column(nullable = false, precision = 10, scale = 2)
    private BigDecimal price;
    
    @Column(name = "duration_months", nullable = false)
    private Integer durationMonths;
    
    @Column(name = "max_properties", nullable = false)
    private Integer maxProperties;
    
    @Column(name = "featured_properties")
    private Integer featuredProperties = 0;

    @Column(name = "priority_support")
    private Boolean prioritySupport = false;

    @Column(name = "analytics_access")
    private Boolean analyticsAccess = false;

    @Column(name = "multiple_images")
    private Boolean multipleImages = false;

    @Column(name = "contact_info_display")
    private Boolean contactInfoDisplay = true;

    @Column(name = "ai_content_generation")
    private Boolean aiContentGeneration = false;

    @Column(name = "push_top_limit")
    private Integer pushTopLimit = 0;
    
    @Enumerated(EnumType.STRING)
    private MembershipType type;
    
    @Column(name = "is_active")
    private Boolean isActive = true;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @OneToMany(mappedBy = "membership", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<UserMembership> userMemberships;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    public enum MembershipType {
        BASIC, ADVANCED
    }
}
