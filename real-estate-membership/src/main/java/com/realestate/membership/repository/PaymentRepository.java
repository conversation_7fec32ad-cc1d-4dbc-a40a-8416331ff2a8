package com.realestate.membership.repository;

import com.realestate.membership.entity.Payment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Optional;

@Repository
public interface PaymentRepository extends JpaRepository<Payment, Long> {
    
    Optional<Payment> findByTransactionId(String transactionId);
    
    Optional<Payment> findByOrderId(String orderId);
    
    Page<Payment> findByUserIdOrderByCreatedAtDesc(Long userId, Pageable pageable);
    
    Page<Payment> findByUserIdAndStatusOrderByCreatedAtDesc(Long userId, Payment.PaymentStatus status, Pageable pageable);
    
    Page<Payment> findByMembershipIdOrderByCreatedAtDesc(Long membershipId, Pageable pageable);
    
    Page<Payment> findByStatusOrderByCreatedAtDesc(Payment.PaymentStatus status, Pageable pageable);
    
    Page<Payment> findByPaymentMethodOrderByCreatedAtDesc(Payment.PaymentMethod method, Pageable pageable);
    
    Page<Payment> findByStatusAndPaymentMethodOrderByCreatedAtDesc(Payment.PaymentStatus status, Payment.PaymentMethod method, Pageable pageable);
    
    @Query("SELECT SUM(p.amount) FROM Payment p WHERE p.status = 'COMPLETED' AND p.paymentDate BETWEEN :startDate AND :endDate")
    BigDecimal getTotalRevenueByDateRange(@Param("startDate") LocalDateTime startDate, 
                                         @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT COUNT(p) FROM Payment p WHERE p.membershipId = :membershipId AND p.status = 'COMPLETED'")
    Long countCompletedPaymentsByMembershipId(@Param("membershipId") Long membershipId);
    
    // Admin service queries
    Long countByStatus(Payment.PaymentStatus status);
    
    @Query("SELECT DATE(p.paymentDate), COUNT(p) FROM Payment p " +
           "WHERE p.paymentDate >= :startDate AND p.status = 'COMPLETED' " +
           "GROUP BY DATE(p.paymentDate) " +
           "ORDER BY DATE(p.paymentDate)")
    java.util.List<Object[]> getRevenueChart(@Param("startDate") LocalDateTime startDate);
}
