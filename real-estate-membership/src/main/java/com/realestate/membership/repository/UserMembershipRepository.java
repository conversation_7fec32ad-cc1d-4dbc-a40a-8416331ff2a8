package com.realestate.membership.repository;

import com.realestate.membership.entity.UserMembership;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface UserMembershipRepository extends JpaRepository<UserMembership, Long> {
    
    @Query("SELECT um FROM UserMembership um WHERE um.user.id = :userId AND um.status = 'ACTIVE' AND um.endDate > :now ORDER BY um.endDate DESC")
    Optional<UserMembership> findActiveByUserId(@Param("userId") Long userId, @Param("now") LocalDateTime now);
    
    List<UserMembership> findByUserIdOrderByCreatedAtDesc(Long userId);
    
    @Query("SELECT um FROM UserMembership um WHERE um.endDate < :now AND um.status = 'ACTIVE'")
    List<UserMembership> findExpiredMemberships(@Param("now") LocalDateTime now);
    
    @Query("SELECT COUNT(um) FROM UserMembership um WHERE um.membership.id = :membershipId AND um.status = 'ACTIVE'")
    Long countActiveSubscriptionsByMembershipId(@Param("membershipId") Long membershipId);
}
