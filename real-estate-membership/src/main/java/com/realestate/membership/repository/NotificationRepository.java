package com.realestate.membership.repository;

import com.realestate.membership.entity.Notification;
import com.realestate.membership.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface NotificationRepository extends JpaRepository<Notification, Long> {

    // Find notifications by user
    Page<Notification> findByUserAndStatusNot(User user, Notification.NotificationStatus status, Pageable pageable);
    
    Page<Notification> findByUserOrderByCreatedAtDesc(User user, Pageable pageable);
    
    List<Notification> findByUserAndStatus(User user, Notification.NotificationStatus status);

    // Count unread notifications
    @Query("SELECT COUNT(n) FROM Notification n WHERE n.user = :user AND n.status = 'UNREAD'")
    Long countUnreadByUser(@Param("user") User user);

    // Find notifications by type
    List<Notification> findByUserAndType(User user, Notification.NotificationType type);
    
    List<Notification> findByUserAndTypeAndStatus(User user, Notification.NotificationType type, Notification.NotificationStatus status);

    // Find notifications to send
    @Query("SELECT n FROM Notification n WHERE " +
           "(n.sendEmail = true AND n.emailSent = false) OR " +
           "(n.sendPush = true AND n.pushSent = false) OR " +
           "(n.sendSms = true AND n.smsSent = false)")
    List<Notification> findPendingNotifications();

    // Find scheduled notifications ready to send
    @Query("SELECT n FROM Notification n WHERE " +
           "n.scheduledAt IS NOT NULL AND n.scheduledAt <= :now AND " +
           "((n.sendEmail = true AND n.emailSent = false) OR " +
           "(n.sendPush = true AND n.pushSent = false) OR " +
           "(n.sendSms = true AND n.smsSent = false))")
    List<Notification> findScheduledNotificationsReadyToSend(@Param("now") LocalDateTime now);

    // Find expired notifications
    @Query("SELECT n FROM Notification n WHERE n.expiresAt IS NOT NULL AND n.expiresAt < :now")
    List<Notification> findExpiredNotifications(@Param("now") LocalDateTime now);

    // Find failed notifications that can be retried
    @Query("SELECT n FROM Notification n WHERE " +
           "n.retryCount < n.maxRetries AND " +
           "n.lastError IS NOT NULL AND " +
           "((n.sendEmail = true AND n.emailSent = false) OR " +
           "(n.sendPush = true AND n.pushSent = false) OR " +
           "(n.sendSms = true AND n.smsSent = false))")
    List<Notification> findRetryableNotifications();

    // Find notifications by priority
    List<Notification> findByUserAndPriorityOrderByCreatedAtDesc(User user, Notification.NotificationPriority priority);

    // Find notifications by related entities
    List<Notification> findByPropertyIdAndUser(Long propertyId, User user);
    
    List<Notification> findByMembershipIdAndUser(Long membershipId, User user);
    
    List<Notification> findByPaymentIdAndUser(Long paymentId, User user);

    // Mark notifications as read
    @Modifying
    @Query("UPDATE Notification n SET n.status = 'READ', n.readAt = :readAt WHERE n.user = :user AND n.status = 'UNREAD'")
    int markAllAsReadByUser(@Param("user") User user, @Param("readAt") LocalDateTime readAt);

    @Modifying
    @Query("UPDATE Notification n SET n.status = 'READ', n.readAt = :readAt WHERE n.id IN :ids")
    int markAsReadByIds(@Param("ids") List<Long> ids, @Param("readAt") LocalDateTime readAt);

    // Delete old notifications
    @Modifying
    @Query("DELETE FROM Notification n WHERE n.createdAt < :cutoffDate AND n.status IN ('READ', 'ARCHIVED')")
    int deleteOldNotifications(@Param("cutoffDate") LocalDateTime cutoffDate);

    // Statistics queries
    @Query("SELECT n.type, COUNT(n) FROM Notification n WHERE n.user = :user GROUP BY n.type")
    List<Object[]> getNotificationStatsByUser(@Param("user") User user);

    @Query("SELECT n.status, COUNT(n) FROM Notification n WHERE n.user = :user GROUP BY n.status")
    List<Object[]> getNotificationStatusStatsByUser(@Param("user") User user);

    // Find recent notifications
    @Query("SELECT n FROM Notification n WHERE n.user = :user AND n.createdAt >= :since ORDER BY n.createdAt DESC")
    List<Notification> findRecentNotificationsByUser(@Param("user") User user, @Param("since") LocalDateTime since);

    // Check for duplicate notifications
    @Query("SELECT COUNT(n) FROM Notification n WHERE " +
           "n.user = :user AND n.type = :type AND n.createdAt >= :since")
    Long countRecentNotificationsByType(@Param("user") User user, 
                                      @Param("type") Notification.NotificationType type, 
                                      @Param("since") LocalDateTime since);

    // Admin queries
    @Query("SELECT COUNT(n) FROM Notification n WHERE n.createdAt >= :startDate AND n.createdAt <= :endDate")
    Long countNotificationsBetweenDates(@Param("startDate") LocalDateTime startDate, 
                                       @Param("endDate") LocalDateTime endDate);

    @Query("SELECT n.type, COUNT(n) FROM Notification n WHERE n.createdAt >= :startDate GROUP BY n.type")
    List<Object[]> getNotificationTypeStats(@Param("startDate") LocalDateTime startDate);
} 