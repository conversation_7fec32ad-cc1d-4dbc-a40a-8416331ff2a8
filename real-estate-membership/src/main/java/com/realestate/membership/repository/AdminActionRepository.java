package com.realestate.membership.repository;

import com.realestate.membership.entity.AdminAction;
import com.realestate.membership.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface AdminActionRepository extends JpaRepository<AdminAction, Long> {
    
    // Find actions by admin
    Page<AdminAction> findByAdminOrderByCreatedAtDesc(User admin, Pageable pageable);
    
    // Find actions by target user
    Page<AdminAction> findByTargetUserOrderByCreatedAtDesc(User targetUser, Pageable pageable);
    
    // Find actions by action type
    Page<AdminAction> findByActionTypeOrderByCreatedAtDesc(String actionType, Pageable pageable);
    
    // Find actions by severity
    Page<AdminAction> findBySeverityOrderByCreatedAtDesc(AdminAction.ActionSeverity severity, Pageable pageable);
    
    // Find actions within date range
    @Query("SELECT aa FROM AdminAction aa WHERE aa.createdAt BETWEEN :startDate AND :endDate ORDER BY aa.createdAt DESC")
    Page<AdminAction> findByDateRange(@Param("startDate") LocalDateTime startDate, 
                                     @Param("endDate") LocalDateTime endDate, 
                                     Pageable pageable);
    
    // Find recent actions
    @Query("SELECT aa FROM AdminAction aa ORDER BY aa.createdAt DESC")
    List<AdminAction> findRecentActions(Pageable pageable);
    
    // Count actions by admin
    Long countByAdmin(User admin);
    
    // Count actions by severity
    Long countBySeverity(AdminAction.ActionSeverity severity);
    
    // Count actions by action type
    Long countByActionType(String actionType);
    
    // Count actions in date range
    @Query("SELECT COUNT(aa) FROM AdminAction aa WHERE aa.createdAt BETWEEN :startDate AND :endDate")
    Long countByDateRange(@Param("startDate") LocalDateTime startDate, 
                         @Param("endDate") LocalDateTime endDate);
    
    // Get admin activity statistics
    @Query("SELECT aa.admin.username, COUNT(aa) FROM AdminAction aa " +
           "WHERE aa.createdAt >= :startDate " +
           "GROUP BY aa.admin.username " +
           "ORDER BY COUNT(aa) DESC")
    List<Object[]> getAdminActivityStats(@Param("startDate") LocalDateTime startDate);
    
    // Get action type statistics
    @Query("SELECT aa.actionType, COUNT(aa) FROM AdminAction aa " +
           "WHERE aa.createdAt >= :startDate " +
           "GROUP BY aa.actionType " +
           "ORDER BY COUNT(aa) DESC")
    List<Object[]> getActionTypeStats(@Param("startDate") LocalDateTime startDate);
    
    // Get daily action counts for charts
    @Query("SELECT DATE(aa.createdAt), COUNT(aa) FROM AdminAction aa " +
           "WHERE aa.createdAt >= :startDate " +
           "GROUP BY DATE(aa.createdAt) " +
           "ORDER BY DATE(aa.createdAt)")
    List<Object[]> getDailyActionCounts(@Param("startDate") LocalDateTime startDate);
    
    // Find critical actions
    @Query("SELECT aa FROM AdminAction aa WHERE aa.severity = 'CRITICAL' " +
           "AND aa.createdAt >= :startDate " +
           "ORDER BY aa.createdAt DESC")
    List<AdminAction> findCriticalActions(@Param("startDate") LocalDateTime startDate);
    
    // Find reversible actions
    @Query("SELECT aa FROM AdminAction aa WHERE aa.isReversible = true " +
           "AND aa.targetUser = :user " +
           "ORDER BY aa.createdAt DESC")
    List<AdminAction> findReversibleActionsByUser(@Param("user") User user);
    
    // Find expired temporary actions (like temporary bans)
    @Query("SELECT aa FROM AdminAction aa WHERE aa.expiresAt IS NOT NULL " +
           "AND aa.expiresAt < :now " +
           "AND aa.actionType LIKE '%BAN%'")
    List<AdminAction> findExpiredTemporaryActions(@Param("now") LocalDateTime now);
    
    // Search actions with filters
    @Query("SELECT aa FROM AdminAction aa WHERE " +
           "(:adminId IS NULL OR aa.admin.id = :adminId) " +
           "AND (:targetUserId IS NULL OR aa.targetUser.id = :targetUserId) " +
           "AND (:actionType IS NULL OR aa.actionType = :actionType) " +
           "AND (:severity IS NULL OR aa.severity = :severity) " +
           "AND (:entityType IS NULL OR aa.entityType = :entityType) " +
           "AND aa.createdAt BETWEEN :startDate AND :endDate " +
           "ORDER BY aa.createdAt DESC")
    Page<AdminAction> searchWithFilters(
            @Param("adminId") Long adminId,
            @Param("targetUserId") Long targetUserId,
            @Param("actionType") String actionType,
            @Param("severity") AdminAction.ActionSeverity severity,
            @Param("entityType") String entityType,
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate,
            Pageable pageable);
    
    // Get user ban history
    @Query("SELECT aa FROM AdminAction aa WHERE aa.targetUser = :user " +
           "AND (aa.actionType = 'USER_BANNED' OR aa.actionType = 'USER_UNBANNED') " +
           "ORDER BY aa.createdAt DESC")
    List<AdminAction> getUserBanHistory(@Param("user") User user);
    
    // Get property actions for a specific property
    @Query("SELECT aa FROM AdminAction aa WHERE aa.entityType = 'PROPERTY' " +
           "AND aa.entityId = :propertyId " +
           "ORDER BY aa.createdAt DESC")
    List<AdminAction> getPropertyActions(@Param("propertyId") Long propertyId);
    
    // Get most active admins in the last N days
    @Query("SELECT aa.admin, COUNT(aa) as actionCount FROM AdminAction aa " +
           "WHERE aa.createdAt >= :startDate " +
           "GROUP BY aa.admin " +
           "ORDER BY actionCount DESC")
    List<Object[]> getMostActiveAdmins(@Param("startDate") LocalDateTime startDate, Pageable pageable);
    
    // Get action summary for dashboard
    @Query("SELECT aa.actionType, aa.severity, COUNT(aa) FROM AdminAction aa " +
           "WHERE aa.createdAt >= :startDate " +
           "GROUP BY aa.actionType, aa.severity " +
           "ORDER BY COUNT(aa) DESC")
    List<Object[]> getActionSummary(@Param("startDate") LocalDateTime startDate);
} 