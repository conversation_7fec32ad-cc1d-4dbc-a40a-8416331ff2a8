package com.realestate.membership.util;

import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.List;

public class ImageValidationUtil {
    
    private static final List<String> VALID_IMAGE_EXTENSIONS = Arrays.asList(
        "jpg", "jpeg", "png", "gif", "webp"
    );
    
    private static final List<String> VALID_CONTENT_TYPES = Arrays.asList(
        "image/jpeg", "image/png", "image/gif", "image/webp"
    );
    
    private static final long MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
    
    public static boolean isValidImageFile(String filename) {
        if (filename == null || filename.trim().isEmpty()) {
            return false;
        }
        
        int lastDotIndex = filename.lastIndexOf(".");
        if (lastDotIndex == -1 || lastDotIndex == 0 || lastDotIndex == filename.length() - 1) {
            return false;
        }
        
        String extension = filename.substring(lastDotIndex + 1).toLowerCase();
        return VALID_IMAGE_EXTENSIONS.contains(extension);
    }
    
    public static boolean isValidFileSize(MultipartFile file) {
        if (file == null) {
            return false;
        }
        
        long size = file.getSize();
        return size > 0 && size <= MAX_FILE_SIZE;
    }
    
    public static boolean isValidContentType(MultipartFile file) {
        if (file == null || file.getContentType() == null) {
            return false;
        }
        
        return VALID_CONTENT_TYPES.contains(file.getContentType().toLowerCase());
    }
    
    public static void validateImageFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("File cannot be null or empty");
        }
        
        if (!isValidImageFile(file.getOriginalFilename())) {
            throw new IllegalArgumentException("Invalid file type. Only JPG, JPEG, PNG, GIF, and WebP files are allowed.");
        }
        
        if (!isValidFileSize(file)) {
            throw new IllegalArgumentException("File size must be between 1 byte and 10MB");
        }
        
        if (!isValidContentType(file)) {
            throw new IllegalArgumentException("Invalid content type. File content doesn't match the expected image format.");
        }
    }
    
    public static String generateUniqueFileName(String originalFilename) {
        if (originalFilename == null || originalFilename.trim().isEmpty()) {
            return System.currentTimeMillis() + "_" + System.nanoTime() + ".jpg";
        }
        
        String baseName = originalFilename;
        String extension = "";
        
        int lastDotIndex = originalFilename.lastIndexOf(".");
        if (lastDotIndex != -1) {
            baseName = originalFilename.substring(0, lastDotIndex);
            extension = originalFilename.substring(lastDotIndex);
        }
        
        // Sanitize the base name
        baseName = baseName.replaceAll("[^a-zA-Z0-9._-]", "_");
        
        // Generate unique name with timestamp + nanotime for uniqueness
        long timestamp = System.currentTimeMillis();
        long nanoTime = System.nanoTime();
        return timestamp + "_" + (nanoTime % 1000000) + "_" + baseName + extension;
    }
}
