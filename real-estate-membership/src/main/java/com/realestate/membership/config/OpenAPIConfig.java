package com.realestate.membership.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.servers.Server;
import io.swagger.v3.oas.models.tags.Tag;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
public class OpenAPIConfig {
    
    @Bean
    public OpenAPI customOpenAPI() {
        String description = "## 🏠 AI-Powered Real Estate Management System\n\n" +
                "### 🌟 Core Features:\n" +
                "- 🔐 **Secure Authentication** (JWT + OAuth2 Google)\n" +
                "- 🏘️ **Property Management** with SEO-friendly slugs & image uploads\n" +
                "- 👤 **User & Membership Management** with tiered subscriptions\n" +
                "- 🤖 **AI Chatbot Integration** (GPT-4) for property search assistance\n" +
                "- 💳 **Payment Gateway Integration** (VNPay, MoMo, Stripe)\n" +
                "- 🔔 **Notification System** (Email, Push, SMS)\n" +
                "- 👨‍💼 **Admin Panel** with user management & analytics\n" +
                "- 📱 **RESTful API** with comprehensive documentation\n\n" +
                
                "### 💳 Payment Integration:\n" +
                "- **VNPay**: Vietnam's leading payment gateway (TEST environment)\n" +
                "- **MoMo**: Popular e-wallet in Vietnam (TEST environment) \n" +
                "- **Stripe**: International credit card processing (TEST environment)\n" +
                "- Automatic membership activation after successful payment\n" +
                "- Webhook security with signature verification\n" +
                "- Multi-currency support (VND, USD)\n\n" +
                
                "### 🔔 Notification Features:\n" +
                "- **20+ Notification Types**: Payment success, property approval, membership expiry, etc.\n" +
                "- **Multi-Channel Delivery**: Email, Push notifications, SMS\n" +
                "- **Scheduled Notifications**: Membership expiry warnings (7/3/1 days)\n" +
                "- **Real-time In-App**: Action buttons, read/unread status\n" +
                "- **Automatic Retry**: Failed notifications retry with exponential backoff\n\n" +
                
                "### 🛡️ Security Features:\n" +
                "- BCrypt password hashing\n" +
                "- JWT token-based authentication with refresh tokens\n" +
                "- Role-based access control (USER/ADMIN)\n" +
                "- Payment webhook signature verification\n" +
                "- CORS protection with configurable origins\n" +
                "- Input validation and sanitization\n\n" +
                
                "### 🚀 Quick Start Guide:\n" +
                "1. **Register/Login**: Use `/auth/register` or `/auth/login` to get JWT token\n" +
                "2. **Authorize**: Add `Bearer {token}` in Authorization header\n" +
                "3. **Explore**: Browse properties with `/properties` endpoints\n" +
                "4. **Purchase**: Upgrade membership via `/payments` endpoints\n" +
                "5. **Manage**: Use admin endpoints for user/property management\n\n" +
                
                "### 🧪 Test Data:\n" +
                "- **Admin User**: `admin / admin123`\n" +
                "- **Payment Cards**: Use test cards from Stripe/VNPay documentation\n" +
                "- **Webhooks**: All payment webhooks are configured for localhost:8080\n\n" +
                
                "### 📚 API Groups:\n" +
                "- **Authentication**: Login, register, OAuth\n" +
                "- **Properties**: CRUD operations, search, image upload\n" +
                "- **Memberships**: Subscription management\n" +
                "- **Payments**: Multi-gateway payment processing\n" +
                "- **Notifications**: Real-time notification system\n" +
                "- **Admin**: User management, analytics, moderation\n" +
                "- **Chatbot**: AI-powered property assistance";
                
        return new OpenAPI()
                .info(new Info()
                        .title("🏠 Real Estate Membership API")
                        .description(description)
                        .version("v2.0.0")
                        .contact(new Contact()
                                .name("Real Estate Development Team")
                                .email("<EMAIL>")
                                .url("https://github.com/realestate/membership"))
                        .license(new License()
                                .name("MIT License")
                                .url("https://opensource.org/licenses/MIT")))
                .servers(List.of(
                        new Server().url("http://localhost:8080/api/v1").description("🧪 Development Server"),
                        new Server().url("https://api.realestate.com/v1").description("🚀 Production Server")
                ))
                .addSecurityItem(new SecurityRequirement().addList("bearerAuth"))
                .components(new Components()
                        .addSecuritySchemes("bearerAuth",
                                new SecurityScheme()
                                        .type(SecurityScheme.Type.HTTP)
                                        .scheme("bearer")
                                        .bearerFormat("JWT")
                                        .description("🔑 Enter JWT token obtained from /auth/login")))
                .tags(List.of(
                        new Tag().name("🔐 Authentication").description("User registration, login, and OAuth operations"),
                        new Tag().name("🏘️ Properties").description("Property CRUD operations, search, and image management"),
                        new Tag().name("📂 Categories").description("Property category management"),
                        new Tag().name("👤 Memberships").description("Subscription plans and user membership management"),
                        new Tag().name("💳 Payments").description("Multi-gateway payment processing (VNPay, MoMo, Stripe)"),
                        new Tag().name("🔔 Notifications").description("Real-time notification system with multi-channel delivery"),
                        new Tag().name("👨‍💼 Admin").description("Administrative operations for user and content management"),
                        new Tag().name("🤖 AI Chatbot").description("AI-powered property search and assistance"),
                        new Tag().name("🔗 OAuth").description("OAuth2 integration with Google and other providers")
                ));
    }
}
