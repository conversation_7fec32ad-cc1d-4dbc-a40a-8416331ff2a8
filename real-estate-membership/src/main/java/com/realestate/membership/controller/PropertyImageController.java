package com.realestate.membership.controller;

import com.realestate.membership.dto.PropertyImageRequest;
import com.realestate.membership.dto.PropertyImageResponse;
import com.realestate.membership.entity.User;
import com.realestate.membership.service.PropertyImageService;
import com.realestate.membership.service.PropertyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
@RequestMapping("/properties/{propertyId}/images")
@RequiredArgsConstructor
@Tag(name = "🏘️ Properties", description = "Property CRUD operations, search, and image management")
public class PropertyImageController {

    private final PropertyImageService propertyImageService;
    private final PropertyService propertyService;

    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "Upload property image", security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<PropertyImageResponse> uploadImage(
            @PathVariable Long propertyId,
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "altText", required = false) String altText,
            @RequestParam(value = "isPrimary", defaultValue = "false") Boolean isPrimary,
            @RequestParam(value = "sortOrder", defaultValue = "0") Integer sortOrder,
            @AuthenticationPrincipal User currentUser) {

        // Verify property ownership
        propertyService.verifyPropertyOwnership(propertyId, currentUser.getId());

        PropertyImageRequest request = new PropertyImageRequest(altText, isPrimary, sortOrder);
        PropertyImageResponse response = propertyImageService.uploadImage(propertyId, file, request);
        return ResponseEntity.ok(response);
    }

    @GetMapping
    @Operation(summary = "Get property images")
    public ResponseEntity<List<PropertyImageResponse>> getPropertyImages(@PathVariable Long propertyId) {
        List<PropertyImageResponse> images = propertyImageService.getPropertyImages(propertyId);
        return ResponseEntity.ok(images);
    }

    @DeleteMapping("/{imageId}")
    @Operation(summary = "Delete property image", security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<Void> deleteImage(
            @PathVariable Long propertyId,
            @PathVariable Long imageId,
            @AuthenticationPrincipal User currentUser) {

        // Verify property ownership
        propertyService.verifyPropertyOwnership(propertyId, currentUser.getId());

        propertyImageService.deleteImage(propertyId, imageId);
        return ResponseEntity.noContent().build();
    }
} 