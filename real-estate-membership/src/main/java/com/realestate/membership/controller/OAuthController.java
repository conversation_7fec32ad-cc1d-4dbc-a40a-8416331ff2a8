package com.realestate.membership.controller;

import com.realestate.membership.service.OAuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/oauth")
@RequiredArgsConstructor
@Tag(name = "🔗 OAuth", description = "OAuth2 integration with Google and other providers")
@Slf4j
public class OAuthController {

    private final OAuthService oAuthService;
    
    @Value("${app.frontend.url:http://localhost:3000}")
    private String frontendUrl;

    @GetMapping("/login/success")
    @Operation(summary = "OAuth login success callback")
    public ResponseEntity<Map<String, Object>> loginSuccess(
            @AuthenticationPrincipal OAuth2User oauth2User) {
        
        try {
            String userEmail = oauth2User.getAttribute("email");
            log.info("OAuth login success for user: {}", userEmail);
            
            // Process OAuth user and generate JWT
            Map<String, Object> authResponse = oAuthService.processOAuthUser(oauth2User);
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Login successful",
                "user", authResponse.get("user"),
                "token", authResponse.get("token"),
                "redirectUrl", frontendUrl + "/dashboard"
            ));
            
        } catch (Exception e) {
            log.error("Error processing OAuth login", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "error", "OAuth login failed",
                "redirectUrl", frontendUrl + "/login?error=oauth_failed"
            ));
        }
    }

    @GetMapping("/login/failure")
    @Operation(summary = "OAuth login failure callback")
    public ResponseEntity<Map<String, Object>> loginFailure(
            @RequestParam(required = false) String error) {
        
        log.warn("OAuth login failure: {}", error);
        
        return ResponseEntity.badRequest().body(Map.of(
            "success", false,
            "error", "OAuth authentication failed",
            "details", error != null ? error : "Unknown error",
            "redirectUrl", frontendUrl + "/login?error=oauth_failed"
        ));
    }

    @PostMapping("/link-account")
    @Operation(summary = "Link OAuth account to existing user")
    public ResponseEntity<Map<String, Object>> linkAccount(
            @AuthenticationPrincipal OAuth2User oauth2User,
            @RequestBody Map<String, String> request) {
        
        try {
            String existingEmail = request.get("email");
            String password = request.get("password");
            
            Map<String, Object> result = oAuthService.linkOAuthAccount(oauth2User, existingEmail, password);
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Account linked successfully",
                "user", result.get("user"),
                "token", result.get("token")
            ));
            
        } catch (Exception e) {
            log.error("Error linking OAuth account", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "error", e.getMessage()
            ));
        }
    }

    @GetMapping("/user-info")
    @Operation(summary = "Get OAuth user information")
    public ResponseEntity<Map<String, Object>> getUserInfo(
            @AuthenticationPrincipal OAuth2User oauth2User) {
        
        if (oauth2User == null) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "error", "No OAuth user found"
            ));
        }
        
        return ResponseEntity.ok(Map.of(
            "success", true,
            "userInfo", Map.of(
                "email", oauth2User.getAttribute("email"),
                "name", oauth2User.getAttribute("name"),
                "picture", oauth2User.getAttribute("picture"),
                "emailVerified", oauth2User.getAttribute("email_verified")
            )
        ));
    }

    @PostMapping("/disconnect")
    @Operation(summary = "Disconnect OAuth account")
    public ResponseEntity<Map<String, Object>> disconnectOAuth(
            @RequestParam String userId) {
        
        try {
            oAuthService.disconnectOAuthAccount(Long.parseLong(userId));
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "OAuth account disconnected successfully"
            ));
            
        } catch (Exception e) {
            log.error("Error disconnecting OAuth account", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "error", "Failed to disconnect OAuth account"
            ));
        }
    }
}