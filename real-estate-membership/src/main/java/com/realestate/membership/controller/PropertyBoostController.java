package com.realestate.membership.controller;

import com.realestate.membership.dto.PropertyBoostResponse;
import com.realestate.membership.entity.User;
import com.realestate.membership.service.PropertyBoostService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/properties/boost")
@RequiredArgsConstructor
@Tag(name = "🚀 Property Boost", description = "Push top and property boost management")
public class PropertyBoostController {
    
    private final PropertyBoostService propertyBoostService;
    
    @PostMapping("/{propertyId}/push-top")
    @Operation(summary = "Push property to top",
               description = "Push a property to the top of search results. Requires Advanced membership and available monthly quota.",
               security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<Map<String, Object>> pushPropertyToTop(
            @Parameter(description = "Property ID to boost", example = "123")
            @PathVariable Long propertyId,
            @AuthenticationPrincipal User currentUser) {

        Map<String, Object> result = propertyBoostService.pushPropertyToTop(currentUser.getId(), propertyId);
        return ResponseEntity.ok(result);
    }
    
    @GetMapping("/status")
    @Operation(summary = "Get user push top status and limits", security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<Map<String, Object>> getPushTopStatus(@AuthenticationPrincipal User currentUser) {
        Map<String, Object> status = propertyBoostService.getUserPushTopStatus(currentUser.getId());
        return ResponseEntity.ok(status);
    }
    
    @GetMapping("/history")
    @Operation(summary = "Get user boost history",
               description = "Get paginated history of all property boosts for the current user",
               security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<Page<PropertyBoostResponse>> getBoostHistory(
            @Parameter(description = "Page number (0-based)", example = "0")
            @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size", example = "10")
            @RequestParam(defaultValue = "10") int size,
            @AuthenticationPrincipal User currentUser) {

        Pageable pageable = PageRequest.of(page, size);
        Page<PropertyBoostResponse> history = propertyBoostService.getUserBoostHistory(currentUser.getId(), pageable);
        return ResponseEntity.ok(history);
    }

    @GetMapping("/active")
    @Operation(summary = "Get user active boosts",
               description = "Get all currently active property boosts for the current user",
               security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<List<PropertyBoostResponse>> getActiveBoosts(@AuthenticationPrincipal User currentUser) {
        List<PropertyBoostResponse> activeBoosts = propertyBoostService.getUserActiveBoosts(currentUser.getId());
        return ResponseEntity.ok(activeBoosts);
    }
}
