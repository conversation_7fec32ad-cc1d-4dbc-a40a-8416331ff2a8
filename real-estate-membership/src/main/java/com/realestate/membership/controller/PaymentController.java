package com.realestate.membership.controller;

import com.realestate.membership.dto.PaymentRequest;
import com.realestate.membership.dto.PaymentResponse;
import com.realestate.membership.dto.PaymentCallbackRequest;
import com.realestate.membership.entity.Payment;
import com.realestate.membership.service.PaymentService;
import com.realestate.membership.service.VNPayService;
import com.realestate.membership.service.MoMoService;
import com.realestate.membership.service.StripeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.Map;

@RestController
@RequestMapping("/payments")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "💳 Payments", description = "Multi-gateway payment processing (VNPay, MoMo, Stripe)")
public class PaymentController {

    private final PaymentService paymentService;
    private final VNPayService vnPayService;
    private final MoMoService moMoService;
    private final StripeService stripeService;

    // =====================================================
    // PAYMENT CREATION ENDPOINTS
    // =====================================================

    @PostMapping("/create")
    @Operation(summary = "Create new payment", security = @SecurityRequirement(name = "bearerAuth"))
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<PaymentResponse> createPayment(
            @Valid @RequestBody PaymentRequest request,
            HttpServletRequest httpRequest) {
        
        log.info("Creating payment for membership: {}, method: {}", 
                request.getMembershipId(), request.getPaymentMethod());
        
        PaymentResponse response = paymentService.createPayment(request, httpRequest);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/vnpay/create")
    @Operation(summary = "Create VNPay payment URL", security = @SecurityRequirement(name = "bearerAuth"))
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> createVNPayPayment(
            @Valid @RequestBody PaymentRequest request,
            HttpServletRequest httpRequest) {
        
        log.info("Creating VNPay payment for membership: {}", request.getMembershipId());
        
        Map<String, Object> vnPayData = vnPayService.createPayment(request, httpRequest);
        return ResponseEntity.ok(vnPayData);
    }

    @PostMapping("/momo/create")
    @Operation(summary = "Create MoMo payment URL", security = @SecurityRequirement(name = "bearerAuth"))
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> createMoMoPayment(
            @Valid @RequestBody PaymentRequest request,
            HttpServletRequest httpRequest) {
        
        log.info("Creating MoMo payment for membership: {}", request.getMembershipId());
        
        Map<String, Object> moMoData = moMoService.createPayment(request, httpRequest);
        return ResponseEntity.ok(moMoData);
    }

    @PostMapping("/stripe/create-intent")
    @Operation(summary = "Create Stripe PaymentIntent", security = @SecurityRequirement(name = "bearerAuth"))
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> createStripePaymentIntent(
            @Valid @RequestBody PaymentRequest request,
            HttpServletRequest httpRequest) {
        
        log.info("Creating Stripe PaymentIntent for membership: {}", request.getMembershipId());
        
        Map<String, Object> stripeData = stripeService.createPaymentIntent(request, httpRequest);
        return ResponseEntity.ok(stripeData);
    }

    @PostMapping("/stripe/create-checkout")
    @Operation(summary = "Create Stripe Checkout Session", security = @SecurityRequirement(name = "bearerAuth"))
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> createStripeCheckout(
            @Valid @RequestBody PaymentRequest request,
            HttpServletRequest httpRequest) {
        
        log.info("Creating Stripe Checkout for membership: {}", request.getMembershipId());
        
        Map<String, Object> stripeData = stripeService.createCheckoutSession(request, httpRequest);
        return ResponseEntity.ok(stripeData);
    }

    // =====================================================
    // PAYMENT CALLBACK/WEBHOOK ENDPOINTS
    // =====================================================

    @GetMapping("/vnpay/callback")
    @Operation(summary = "VNPay payment callback (GET)")
    public void vnPayCallback(
            HttpServletRequest request,
            HttpServletResponse response) throws IOException {
        
        log.info("VNPay callback received via GET");
        
        try {
            PaymentResponse result = vnPayService.processCallback(request);
            
            // Redirect to frontend with result
            String redirectUrl = buildRedirectUrl(result);
            response.sendRedirect(redirectUrl);
            
        } catch (Exception e) {
            log.error("Error processing VNPay callback", e);
            String errorUrl = buildErrorRedirectUrl("VNPay payment failed");
            response.sendRedirect(errorUrl);
        }
    }

    @PostMapping("/vnpay/webhook")
    @Operation(summary = "VNPay payment webhook (POST)")
    public ResponseEntity<Map<String, String>> vnPayWebhook(
            @RequestBody Map<String, String> requestData,
            HttpServletRequest request) {
        
        log.info("VNPay webhook received: {}", requestData);
        
        try {
            PaymentResponse result = vnPayService.processWebhook(requestData, request);
            
            Map<String, String> response = Map.of(
                "RspCode", "00",
                "Message", "Confirm Success"
            );
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Error processing VNPay webhook", e);
            
            Map<String, String> errorResponse = Map.of(
                "RspCode", "99", 
                "Message", "Confirm Fail"
            );
            
            return ResponseEntity.ok(errorResponse);
        }
    }

    @PostMapping("/momo/callback")
    @Operation(summary = "MoMo payment callback")
    public ResponseEntity<Map<String, Object>> moMoCallback(
            @RequestBody PaymentCallbackRequest callbackRequest,
            HttpServletRequest request) {
        
        log.info("MoMo callback received: {}", callbackRequest);
        
        try {
            PaymentResponse result = moMoService.processCallback(callbackRequest, request);
            
            Map<String, Object> response = Map.of(
                "status", "success",
                "message", "Payment processed successfully",
                "data", result
            );
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Error processing MoMo callback", e);
            
            Map<String, Object> errorResponse = Map.of(
                "status", "error",
                "message", "Payment processing failed",
                "error", e.getMessage()
            );
            
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    @PostMapping("/momo/webhook")
    @Operation(summary = "MoMo payment webhook")
    public ResponseEntity<Map<String, String>> moMoWebhook(
            @RequestBody Map<String, Object> webhookData,
            HttpServletRequest request) {
        
        log.info("MoMo webhook received: {}", webhookData);
        
        try {
            moMoService.processWebhook(webhookData, request);
            
            Map<String, String> response = Map.of(
                "status", "success",
                "message", "Webhook processed successfully"
            );
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Error processing MoMo webhook", e);
            
            Map<String, String> errorResponse = Map.of(
                "status", "error",
                "message", "Webhook processing failed"
            );
            
            return ResponseEntity.ok(errorResponse);
        }
    }

    @PostMapping("/stripe/webhook")
    @Operation(summary = "Stripe payment webhook")
    public ResponseEntity<Map<String, String>> stripeWebhook(
            @RequestBody String payload,
            @RequestHeader("Stripe-Signature") String sigHeader,
            HttpServletRequest request) {
        
        log.info("Stripe webhook received");
        
        try {
            stripeService.processWebhook(payload, sigHeader, request);
            
            Map<String, String> response = Map.of(
                "status", "success",
                "message", "Webhook processed successfully"
            );
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Error processing Stripe webhook", e);
            
            Map<String, String> errorResponse = Map.of(
                "status", "error",
                "message", "Webhook processing failed"
            );
            
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    // =====================================================
    // PAYMENT VERIFICATION & STATUS
    // =====================================================

    @GetMapping("/{paymentId}/status")
    @Operation(summary = "Check payment status", security = @SecurityRequirement(name = "bearerAuth"))
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<PaymentResponse> getPaymentStatus(@PathVariable Long paymentId) {
        PaymentResponse payment = paymentService.getPaymentById(paymentId);
        return ResponseEntity.ok(payment);
    }

    @PostMapping("/{paymentId}/verify")
    @Operation(summary = "Verify payment status with gateway", security = @SecurityRequirement(name = "bearerAuth"))
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<PaymentResponse> verifyPayment(@PathVariable Long paymentId) {
        PaymentResponse payment = paymentService.verifyPaymentStatus(paymentId);
        return ResponseEntity.ok(payment);
    }

    @PostMapping("/{paymentId}/cancel")
    @Operation(summary = "Cancel payment", security = @SecurityRequirement(name = "bearerAuth"))
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> cancelPayment(
            @PathVariable Long paymentId,
            @RequestParam(required = false) String reason) {
        
        paymentService.cancelPayment(paymentId, reason);
        
        return ResponseEntity.ok(Map.of(
            "success", true,
            "message", "Payment cancelled successfully",
            "paymentId", paymentId
        ));
    }

    // =====================================================
    // PAYMENT HISTORY & LISTING
    // =====================================================

    @GetMapping("/my-payments")
    @Operation(summary = "Get user's payment history", security = @SecurityRequirement(name = "bearerAuth"))
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<Page<PaymentResponse>> getMyPayments(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "paymentDate") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            @RequestParam(required = false) Payment.PaymentStatus status) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                   Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        
        Pageable pageable = PageRequest.of(page, size, sort);
        Page<PaymentResponse> payments = paymentService.getUserPayments(pageable, status);
        
        return ResponseEntity.ok(payments);
    }

    @GetMapping("/membership/{membershipId}")
    @Operation(summary = "Get payments for specific membership", security = @SecurityRequirement(name = "bearerAuth"))
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<Page<PaymentResponse>> getPaymentsByMembership(
            @PathVariable Long membershipId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("paymentDate").descending());
        Page<PaymentResponse> payments = paymentService.getPaymentsByMembership(membershipId, pageable);
        
        return ResponseEntity.ok(payments);
    }

    // =====================================================
    // PAYMENT METHODS & CONFIGURATION
    // =====================================================

    @GetMapping("/methods")
    @Operation(summary = "Get available payment methods")
    public ResponseEntity<Map<String, Object>> getPaymentMethods() {
        Map<String, Object> methods = paymentService.getAvailablePaymentMethods();
        return ResponseEntity.ok(methods);
    }

    @GetMapping("/exchange-rate")
    @Operation(summary = "Get current exchange rates")
    public ResponseEntity<Map<String, Object>> getExchangeRates() {
        Map<String, Object> rates = paymentService.getExchangeRates();
        return ResponseEntity.ok(rates);
    }

    // =====================================================
    // ADMIN PAYMENT MANAGEMENT
    // =====================================================

    @GetMapping("/admin/all")
    @Operation(summary = "Get all payments (admin only)", security = @SecurityRequirement(name = "bearerAuth"))
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Page<PaymentResponse>> getAllPayments(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "paymentDate") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            @RequestParam(required = false) Payment.PaymentStatus status,
            @RequestParam(required = false) Payment.PaymentMethod method) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                   Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        
        Pageable pageable = PageRequest.of(page, size, sort);
        Page<PaymentResponse> payments = paymentService.getAllPayments(pageable, status, method);
        
        return ResponseEntity.ok(payments);
    }

    @PostMapping("/admin/{paymentId}/refund")
    @Operation(summary = "Process refund (admin only)", security = @SecurityRequirement(name = "bearerAuth"))
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> processRefund(
            @PathVariable Long paymentId,
            @RequestParam String reason,
            @RequestParam(required = false) String refundAmount) {
        
        PaymentResponse refund = paymentService.processRefund(paymentId, reason, refundAmount);
        
        return ResponseEntity.ok(Map.of(
            "success", true,
            "message", "Refund processed successfully",
            "refund", refund
        ));
    }

    // =====================================================
    // PRIVATE HELPER METHODS
    // =====================================================

    private String buildRedirectUrl(PaymentResponse payment) {
        String baseUrl = "http://localhost:3000"; // Frontend URL
        String status = payment.getStatus().toString().toLowerCase();
        
        return String.format("%s/payment/result?status=%s&paymentId=%d&amount=%s", 
                           baseUrl, status, payment.getId(), payment.getAmount());
    }
    
    private String buildErrorRedirectUrl(String errorMessage) {
        String baseUrl = "http://localhost:3000"; // Frontend URL
        return String.format("%s/payment/result?status=error&message=%s", 
                           baseUrl, errorMessage);
    }
} 