package com.realestate.membership.controller;

import com.realestate.membership.dto.*;
import com.realestate.membership.service.AuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
@Tag(name = "🔐 Authentication", description = "Comprehensive authentication system with email verification and password reset")
public class AuthController {
    
    private final AuthService authService;
    
    @PostMapping("/register")
    @Operation(summary = "📝 Đăng ký tài khoản", 
               description = "Tạo tài khoản mới và gửi email xác thực")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "Đăng ký thành công, email xác thực đã được gửi"),
        @ApiResponse(responseCode = "400", description = "Thông tin đăng ký không hợp lệ"),
        @ApiResponse(responseCode = "409", description = "Username hoặc email đã được sử dụng")
    })
    public ResponseEntity<AuthResponse> register(@Valid @RequestBody RegisterRequest request) {
        AuthResponse response = authService.register(request);
        return ResponseEntity.ok(response);
    }
    
    @PostMapping("/login")
    @Operation(summary = "🔓 Đăng nhập", 
               description = "Xác thực người dùng và trả về JWT token")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "Đăng nhập thành công"),
        @ApiResponse(responseCode = "401", description = "Thông tin đăng nhập không chính xác"),
        @ApiResponse(responseCode = "403", description = "Email chưa được xác thực")
    })
    public ResponseEntity<AuthResponse> login(@Valid @RequestBody LoginRequest request) {
        AuthResponse response = authService.login(request);
        return ResponseEntity.ok(response);
    }

    // =====================================================
    // EMAIL VERIFICATION ENDPOINTS
    // =====================================================

    @GetMapping("/verify-email")
    @Operation(summary = "📧 Xác thực email", 
               description = "Xác thực email thông qua token được gửi qua email")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "Email được xác thực thành công"),
        @ApiResponse(responseCode = "400", description = "Token không hợp lệ hoặc đã hết hạn"),
        @ApiResponse(responseCode = "404", description = "Không tìm thấy tài khoản")
    })
    public ResponseEntity<Map<String, Object>> verifyEmail(
            @RequestParam String token,
            @RequestParam String email) {
        Map<String, Object> response = authService.verifyEmail(token, email);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/resend-verification")
    @Operation(summary = "🔄 Gửi lại email xác thực", 
               description = "Gửi lại email xác thực cho tài khoản chưa được xác thực")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "Email xác thực đã được gửi lại"),
        @ApiResponse(responseCode = "400", description = "Email đã được xác thực hoặc không hợp lệ"),
        @ApiResponse(responseCode = "404", description = "Không tìm thấy tài khoản")
    })
    public ResponseEntity<Map<String, Object>> resendVerificationEmail(
            @Valid @RequestBody EmailVerificationRequest request) {
        Map<String, Object> response = authService.resendVerificationEmail(request);
        return ResponseEntity.ok(response);
    }

    // =====================================================
    // PASSWORD RESET ENDPOINTS
    // =====================================================

    @PostMapping("/forgot-password")
    @Operation(summary = "🔒 Quên mật khẩu", 
               description = "Gửi email reset mật khẩu đến địa chỉ email đã đăng ký")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "Email reset mật khẩu đã được gửi (nếu email tồn tại)"),
        @ApiResponse(responseCode = "400", description = "Email không hợp lệ")
    })
    public ResponseEntity<Map<String, Object>> forgotPassword(
            @Valid @RequestBody ForgotPasswordRequest request) {
        Map<String, Object> response = authService.forgotPassword(request);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/reset-password")
    @Operation(summary = "🔑 Đặt lại mật khẩu", 
               description = "Đặt lại mật khẩu mới thông qua token được gửi qua email")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "Mật khẩu đã được đặt lại thành công"),
        @ApiResponse(responseCode = "400", description = "Token không hợp lệ, đã hết hạn, hoặc mật khẩu không khớp"),
        @ApiResponse(responseCode = "404", description = "Không tìm thấy tài khoản")
    })
    public ResponseEntity<Map<String, Object>> resetPassword(
            @Valid @RequestBody PasswordResetRequest request) {
        Map<String, Object> response = authService.resetPassword(request);
        return ResponseEntity.ok(response);
    }

    // =====================================================
    // UTILITY ENDPOINTS
    // =====================================================

    @GetMapping("/check-email")
    @Operation(summary = "✉️ Kiểm tra email", 
               description = "Kiểm tra xem email đã được đăng ký chưa")
    public ResponseEntity<Map<String, Object>> checkEmail(@RequestParam String email) {
        // This is a simple utility endpoint for frontend validation
        boolean exists = authService.isEmailExists(email);
        return ResponseEntity.ok(Map.of(
            "exists", exists,
            "message", exists ? "Email đã được sử dụng" : "Email có thể sử dụng"
        ));
    }

    @GetMapping("/check-username")
    @Operation(summary = "👤 Kiểm tra username", 
               description = "Kiểm tra xem username đã được sử dụng chưa")
    public ResponseEntity<Map<String, Object>> checkUsername(@RequestParam String username) {
        // This is a simple utility endpoint for frontend validation
        boolean exists = authService.isUsernameExists(username);
        return ResponseEntity.ok(Map.of(
            "exists", exists,
            "message", exists ? "Username đã được sử dụng" : "Username có thể sử dụng"
        ));
    }
}
