package com.realestate.membership.controller;

import com.realestate.membership.entity.User;
import com.realestate.membership.service.UserDashboardService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/dashboard")
@RequiredArgsConstructor
@Tag(name = "📊 User Dashboard", description = "User dashboard with statistics and insights")
public class UserDashboardController {
    
    private final UserDashboardService userDashboardService;
    
    @GetMapping
    @Operation(summary = "Get user dashboard overview", security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<Map<String, Object>> getUserDashboard(@AuthenticationPrincipal User currentUser) {
        Map<String, Object> dashboard = userDashboardService.getUserDashboard(currentUser.getId());
        return ResponseEntity.ok(dashboard);
    }
    
    @GetMapping("/seo/score/{propertyId}")
    @Operation(summary = "Get property SEO score and recommendations", security = @SecurityRequirement(name = "bearerAuth"))
    public ResponseEntity<Map<String, Object>> getPropertySEOScore(
            @PathVariable Long propertyId,
            @AuthenticationPrincipal User currentUser) {
        
        Map<String, Object> seoData = userDashboardService.getPropertySEOScore(currentUser.getId(), propertyId);
        return ResponseEntity.ok(seoData);
    }
}
