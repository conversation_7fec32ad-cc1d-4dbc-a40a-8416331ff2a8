package com.realestate.membership.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.realestate.membership.service.tools.PropertySearchTool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.*;

@Service
@Slf4j
public class OpenAIService {

    private final WebClient webClient;
    private final String model;
    private final Integer maxTokens;
    private final Double temperature;
    private final PropertySearchTool propertySearchTool;
    private final ObjectMapper objectMapper;

    public OpenAIService(
            @Value("${openai.api.key}") String apiKey,
            @Value("${openai.api.model:gpt-4o}") String model,
            @Value("${openai.api.max-tokens:2000}") Integer maxTokens,
            @Value("${openai.api.temperature:0.3}") Double temperature,
            @Value("${openai.api.timeout:45}") Integer timeoutSeconds,
            PropertySearchTool propertySearchTool) {
        
        this.model = model;
        this.maxTokens = maxTokens;
        this.temperature = temperature;
        this.propertySearchTool = propertySearchTool;
        this.objectMapper = new ObjectMapper();
        
        this.webClient = WebClient.builder()
            .baseUrl("https://api.openai.com/v1")
            .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + apiKey)
            .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
            .build();
        
        log.info("OpenAI Service initialized with model: {} and function calling support", model);
    }

    /**
     * Generate AI response from user input with function calling
     */
    public String generateResponse(String userMessage, String systemPrompt) {
        try {
            log.debug("Generating response for message: {}", userMessage);
            
            // Create request body with function calling
            Map<String, Object> requestBody = createChatCompletionRequest(userMessage, systemPrompt);
            
            // Call OpenAI API
            String response = webClient.post()
                .uri("/chat/completions")
                .body(BodyInserters.fromValue(requestBody))
                .retrieve()
                .bodyToMono(String.class)
                .timeout(Duration.ofSeconds(45))
                .block();
            
            return processOpenAIResponse(response, userMessage, systemPrompt);
            
        } catch (Exception e) {
            log.error("Error generating OpenAI response: {}", e.getMessage());
            log.debug("Full error details", e);

            // Return helpful fallback response
            if (e.getMessage() != null && e.getMessage().contains("401")) {
                return "Hệ thống AI tạm thời không khả dụng. Bạn có thể thử tìm kiếm bất động sản bằng cách sử dụng bộ lọc trên trang web.";
            }
            return "Tôi đang gặp sự cố kỹ thuật. Vui lòng thử lại sau hoặc sử dụng chức năng tìm kiếm thông thường.";
        }
    }
    
    private Map<String, Object> createChatCompletionRequest(String userMessage, String systemPrompt) {
        Map<String, Object> request = new HashMap<>();
        request.put("model", model);
        request.put("max_tokens", maxTokens);
        request.put("temperature", temperature);
        
        // Messages
        List<Map<String, Object>> messages = new ArrayList<>();
        
        // System message - Force function usage
        Map<String, Object> systemMessage = new HashMap<>();
        systemMessage.put("role", "system");
        systemMessage.put("content", systemPrompt + 
            "\n\nQUAN TRỌNG: Khi khách hàng hỏi về tìm kiếm bất động sản, BẮT BUỘC phải sử dụng function search_properties để lấy dữ liệu thực từ hệ thống. " +
            "KHÔNG được tự tạo ra danh sách bất động sản. Luôn luôn gọi function trước khi trả lời.");
        messages.add(systemMessage);
        
        // User message
        Map<String, Object> userMsg = new HashMap<>();
        userMsg.put("role", "user");
        userMsg.put("content", userMessage);
        messages.add(userMsg);
        
        request.put("messages", messages);
        
        // Function definition
        List<Map<String, Object>> functions = new ArrayList<>();
        Map<String, Object> function = new HashMap<>();
        function.put("name", "search_properties");
        function.put("description", "Tìm kiếm bất động sản dựa trên tiêu chí của khách hàng. BẮT BUỘC phải sử dụng khi có yêu cầu tìm kiếm bất động sản.");
        function.put("parameters", propertySearchTool.getToolDefinition());
        functions.add(function);
        
        request.put("functions", functions);
        request.put("function_call", "auto"); // Force function calling
        
        return request;
    }
    
    private String processOpenAIResponse(String response, String userMessage, String systemPrompt) {
        try {
            JsonNode responseNode = objectMapper.readTree(response);
            JsonNode choices = responseNode.get("choices");
            
            if (choices != null && choices.isArray() && choices.size() > 0) {
                JsonNode firstChoice = choices.get(0);
                JsonNode message = firstChoice.get("message");
                
                // Check if AI wants to call a function
                if (message.has("function_call")) {
                    JsonNode functionCall = message.get("function_call");
                    String functionName = functionCall.get("name").asText();
                    
                    if ("search_properties".equals(functionName)) {
                        // Execute property search
                        String arguments = functionCall.get("arguments").asText();
                        String searchResult = propertySearchTool.executeSearch(arguments);
                        
                        // Create follow-up request with function result
                        return generateFollowUpResponse(userMessage, systemPrompt, message, searchResult);
                    }
                }
                
                // Return regular response if no function call
                if (message.has("content")) {
                    return message.get("content").asText();
                }
            }
            
            log.warn("No valid response from OpenAI");
            return "Xin lỗi, tôi không thể tạo ra phản hồi lúc này. Vui lòng thử lại.";
            
        } catch (Exception e) {
            log.error("Error processing OpenAI response", e);
            return "Tôi đang gặp sự cố kỹ thuật. Vui lòng thử lại sau.";
        }
    }
    
    private String generateFollowUpResponse(String userMessage, String systemPrompt, 
                                          JsonNode assistantMessage, String functionResult) {
        try {
            Map<String, Object> followUpRequest = new HashMap<>();
            followUpRequest.put("model", model);
            followUpRequest.put("max_tokens", maxTokens);
            followUpRequest.put("temperature", temperature);
            
            // Messages with function result
            List<Map<String, Object>> messages = new ArrayList<>();
            
            // System message
            Map<String, Object> systemMessage = new HashMap<>();
            systemMessage.put("role", "system");
            systemMessage.put("content", systemPrompt);
            messages.add(systemMessage);
            
            // User message
            Map<String, Object> userMsg = new HashMap<>();
            userMsg.put("role", "user");
            userMsg.put("content", userMessage);
            messages.add(userMsg);
            
            // Assistant message with function call
            Map<String, Object> assistantMsg = objectMapper.convertValue(assistantMessage, Map.class);
            assistantMsg.put("role", "assistant");
            messages.add(assistantMsg);
            
            // Function result message
            Map<String, Object> functionMsg = new HashMap<>();
            functionMsg.put("role", "function");
            functionMsg.put("name", "search_properties");
            functionMsg.put("content", functionResult);
            messages.add(functionMsg);
            
            followUpRequest.put("messages", messages);
            
            // Call OpenAI API again
            String response = webClient.post()
                .uri("/chat/completions")
                .body(BodyInserters.fromValue(followUpRequest))
                .retrieve()
                .bodyToMono(String.class)
                .timeout(Duration.ofSeconds(45))
                .block();
            
            // Process final response
            JsonNode responseNode = objectMapper.readTree(response);
            JsonNode choices = responseNode.get("choices");
            
            if (choices != null && choices.isArray() && choices.size() > 0) {
                JsonNode firstChoice = choices.get(0);
                JsonNode message = firstChoice.get("message");
                
                if (message.has("content")) {
                    return message.get("content").asText();
                }
            }
            
            return "Xin lỗi, tôi không thể xử lý kết quả tìm kiếm.";
            
        } catch (Exception e) {
            log.error("Error generating follow-up response", e);
            return "Đã tìm thấy kết quả nhưng gặp lỗi khi xử lý: " + functionResult;
        }
    }

    /**
     * Generate response with conversation history
     */
    public String generateResponseWithHistory(List<com.theokanning.openai.completion.chat.ChatMessage> messages, String systemPrompt) {
        // Simple implementation without function calling for now
        try {
            Map<String, Object> request = new HashMap<>();
            request.put("model", model);
            request.put("max_tokens", maxTokens);
            request.put("temperature", temperature);
            
            List<Map<String, Object>> apiMessages = new ArrayList<>();
            
            // Add system message
            Map<String, Object> systemMessage = new HashMap<>();
            systemMessage.put("role", "system");
            systemMessage.put("content", systemPrompt);
            apiMessages.add(systemMessage);
            
            // Convert messages
            for (com.theokanning.openai.completion.chat.ChatMessage msg : messages) {
                Map<String, Object> apiMsg = new HashMap<>();
                apiMsg.put("role", msg.getRole());
                apiMsg.put("content", msg.getContent());
                apiMessages.add(apiMsg);
            }
            
            request.put("messages", apiMessages);
            
            String response = webClient.post()
                .uri("/chat/completions")
                .body(BodyInserters.fromValue(request))
                .retrieve()
                .bodyToMono(String.class)
                .timeout(Duration.ofSeconds(45))
                .block();
            
            JsonNode responseNode = objectMapper.readTree(response);
            JsonNode choices = responseNode.get("choices");
            
            if (choices != null && choices.isArray() && choices.size() > 0) {
                JsonNode firstChoice = choices.get(0);
                JsonNode message = firstChoice.get("message");
                
                if (message.has("content")) {
                    return message.get("content").asText();
                }
            }
            
            return "Xin lỗi, tôi không thể tạo ra phản hồi lúc này.";
            
        } catch (Exception e) {
            log.error("Error generating OpenAI response with history", e);
            return "Tôi đang gặp sự cố kỹ thuật. Vui lòng thử lại sau.";
        }
    }

    /**
     * Generate response asynchronously
     */
    public Mono<String> generateResponseAsync(String userMessage, String systemPrompt) {
        return Mono.fromCallable(() -> generateResponse(userMessage, systemPrompt));
    }

    /**
     * Analyze user intent
     */
    public String analyzeIntent(String userMessage) {
        String intentPrompt = """
            Phân tích ý định của người dùng từ tin nhắn của họ và phân loại vào một trong các danh mục sau:
            - PROPERTY_SEARCH: Người dùng muốn tìm kiếm bất động sản
            - PRICE_INQUIRY: Người dùng hỏi về giá cả hoặc giá trị thị trường
            - LOCATION_INFO: Người dùng hỏi về vị trí hoặc khu vực
            - INVESTMENT_ADVICE: Người dùng tìm kiếm lời khuyên đầu tư
            - LEGAL_PROCESS: Người dùng hỏi về thủ tục pháp lý
            - GENERAL: Câu hỏi chung hoặc trò chuyện
            
            Chỉ trả lời bằng tên danh mục.
            """;
        
        return generateSimpleResponse(userMessage, intentPrompt);
    }

    /**
     * Extract property criteria from user message
     */
    public String extractPropertyCriteria(String userMessage) {
        String extractionPrompt = """
            Trích xuất tiêu chí tìm kiếm bất động sản từ tin nhắn của người dùng và định dạng thành JSON:
            {
                "location": "string",
                "propertyType": "string",
                "priceMin": number,
                "priceMax": number,
                "bedrooms": number,
                "bathrooms": number,
                "area": number,
                "listingType": "SALE hoặc RENT"
            }
            
            Nếu bất kỳ trường nào không được đề cập, sử dụng null. Chỉ trả lời bằng JSON.
            """;
        
        return generateSimpleResponse(userMessage, extractionPrompt);
    }
    
    private String generateSimpleResponse(String userMessage, String systemPrompt) {
        try {
            Map<String, Object> request = new HashMap<>();
            request.put("model", model);
            request.put("max_tokens", 500);
            request.put("temperature", 0.1);
            
            List<Map<String, Object>> messages = new ArrayList<>();
            
            Map<String, Object> systemMessage = new HashMap<>();
            systemMessage.put("role", "system");
            systemMessage.put("content", systemPrompt);
            messages.add(systemMessage);
            
            Map<String, Object> userMsg = new HashMap<>();
            userMsg.put("role", "user");
            userMsg.put("content", userMessage);
            messages.add(userMsg);
            
            request.put("messages", messages);
            
            String response = webClient.post()
                .uri("/chat/completions")
                .body(BodyInserters.fromValue(request))
                .retrieve()
                .bodyToMono(String.class)
                .timeout(Duration.ofSeconds(30))
                .block();
            
            JsonNode responseNode = objectMapper.readTree(response);
            JsonNode choices = responseNode.get("choices");
            
            if (choices != null && choices.isArray() && choices.size() > 0) {
                JsonNode firstChoice = choices.get(0);
                JsonNode message = firstChoice.get("message");
                
                if (message.has("content")) {
                    return message.get("content").asText().trim();
                }
            }
            
            return "GENERAL";
            
        } catch (Exception e) {
            log.error("Error generating simple response: {}", e.getMessage());
            log.debug("Full error details", e);

            // Return appropriate fallback based on user message content
            String userMessageLower = userMessage.toLowerCase();
            if (userMessageLower.contains("apartment") || userMessageLower.contains("căn hộ")) {
                return "PROPERTY_SEARCH";
            } else if (userMessageLower.contains("house") || userMessageLower.contains("nhà")) {
                return "PROPERTY_SEARCH";
            } else if (userMessageLower.contains("price") || userMessageLower.contains("giá")) {
                return "PROPERTY_SEARCH";
            } else if (userMessageLower.contains("search") || userMessageLower.contains("tìm")) {
                return "PROPERTY_SEARCH";
            }
            return "GENERAL";
        }
    }
}
