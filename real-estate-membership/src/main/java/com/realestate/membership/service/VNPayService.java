package com.realestate.membership.service;

import com.realestate.membership.dto.PaymentRequest;
import com.realestate.membership.dto.PaymentResponse;
import com.realestate.membership.entity.Payment;
import com.realestate.membership.entity.User;
import com.realestate.membership.repository.PaymentRepository;
import com.realestate.membership.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.servlet.http.HttpServletRequest;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class VNPayService {

    private final PaymentRepository paymentRepository;
    private final UserRepository userRepository;
    private final PaymentService paymentService;
    
    // VNPay Configuration Constants
    @Value("${vnpay.tmn-code:DEMO}")
    private String vnp_TmnCode;
    
    @Value("${vnpay.secret-key:DEMO_SECRET_KEY}")
    private String vnp_HashSecret;
    
    @Value("${vnpay.api-url:https://sandbox.vnpayment.vn/paymentv2/vpcpay.html}")
    private String vnp_PayUrl;
    
    @Value("${vnpay.return-url:http://localhost:8080/api/v1/payments/vnpay/callback}")
    private String vnp_ReturnUrl;
    
    @Value("${vnpay.version:2.1.0}")
    private String vnp_Version;
    
    @Value("${vnpay.command:pay}")
    private String vnp_Command;
    
    @Value("${vnpay.order-type:other}")
    private String vnp_OrderType;
    
    @Value("${vnpay.currency-code:VND}")
    private String vnp_CurrCode;
    
    @Value("${vnpay.locale:vn}")
    private String vnp_Locale;

    // =====================================================
    // PAYMENT CREATION
    // =====================================================

    public Map<String, Object> createPayment(PaymentRequest request, HttpServletRequest httpRequest) {
        try {
            // Validate request
            if (!request.isValidForVNPay()) {
                throw new IllegalArgumentException("Invalid payment method for VNPay");
            }
            
            // Set default values
            request.setDefaultValues();
            
            // Get current user
            String username = SecurityContextHolder.getContext().getAuthentication().getName();
            User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new RuntimeException("User not found"));
            
            // Create payment record
            Payment payment = createPaymentRecord(request, user);
            
            // Build VNPay parameters
            Map<String, String> vnp_Params = buildVNPayParams(request, payment, httpRequest);
            
            // Generate secure hash
            String secureHash = generateSecureHash(vnp_Params);
            vnp_Params.put("vnp_SecureHash", secureHash);
            
            // Build payment URL
            String paymentUrl = buildPaymentUrl(vnp_Params);
            
            log.info("VNPay payment URL created for payment ID: {}", payment.getId());
            
            return Map.of(
                "paymentId", payment.getId(),
                "paymentUrl", paymentUrl,
                "orderId", payment.getOrderId(),
                "amount", payment.getAmount(),
                "description", payment.getDescription(),
                "status", "PENDING",
                "gateway", "VNPAY",
                "expires", System.currentTimeMillis() + (15 * 60 * 1000) // 15 minutes
            );
            
        } catch (Exception e) {
            log.error("Error creating VNPay payment", e);
            throw new RuntimeException("Failed to create VNPay payment: " + e.getMessage());
        }
    }

    // =====================================================
    // CALLBACK PROCESSING
    // =====================================================

    public PaymentResponse processCallback(HttpServletRequest request) {
        try {
            // Extract VNPay parameters
            Map<String, String> vnp_Params = extractVNPayParams(request);
            
            log.info("Processing VNPay callback: {}", vnp_Params);
            
            // Verify secure hash
            if (!verifySecureHash(vnp_Params)) {
                throw new SecurityException("Invalid VNPay secure hash");
            }
            
            // Extract payment information
            String vnp_TxnRef = vnp_Params.get("vnp_TxnRef");
            String vnp_ResponseCode = vnp_Params.get("vnp_ResponseCode");
            String vnp_TransactionNo = vnp_Params.get("vnp_TransactionNo");
            String vnp_Amount = vnp_Params.get("vnp_Amount");
            String vnp_BankCode = vnp_Params.get("vnp_BankCode");
            String vnp_PayDate = vnp_Params.get("vnp_PayDate");
            
            // Find payment by order ID
            Payment payment = paymentRepository.findByOrderId(vnp_TxnRef)
                .orElseThrow(() -> new RuntimeException("Payment not found: " + vnp_TxnRef));
            
            // Update payment status
            Payment.PaymentStatus newStatus = "00".equals(vnp_ResponseCode) ? 
                Payment.PaymentStatus.COMPLETED : Payment.PaymentStatus.FAILED;
            
            payment.setStatus(newStatus);
            payment.setGatewayTransactionId(vnp_TransactionNo);
            payment.setGatewayResponseCode(vnp_ResponseCode);
            payment.setGatewayMessage(getVNPayMessage(vnp_ResponseCode));
            payment.setBankCode(vnp_BankCode);
            payment.setUpdatedAt(LocalDateTime.now());
            
            if ("00".equals(vnp_ResponseCode)) {
                payment.setPaymentDate(parseVNPayDate(vnp_PayDate));
                payment.setCompletedAt(LocalDateTime.now());
                
                // Process successful payment
                paymentService.processSuccessfulPayment(payment);
            }
            
            payment = paymentRepository.save(payment);
            
            log.info("VNPay callback processed for payment {}: status={}", 
                    payment.getId(), newStatus);
            
            return paymentService.mapToPaymentResponse(payment);
            
        } catch (Exception e) {
            log.error("Error processing VNPay callback", e);
            throw new RuntimeException("Failed to process VNPay callback: " + e.getMessage());
        }
    }

    public PaymentResponse processWebhook(Map<String, String> webhookData, HttpServletRequest request) {
        // For VNPay, webhook processing is similar to callback
        // but may have different validation requirements
        return processVNPayResponse(webhookData);
    }

    // =====================================================
    // PRIVATE HELPER METHODS
    // =====================================================

    private Payment createPaymentRecord(PaymentRequest request, User user) {
        Payment payment = new Payment();
        payment.setUser(user);
        payment.setMembershipId(request.getMembershipId());
        payment.setAmount(request.getAmount());
        payment.setPaymentMethod(Payment.PaymentMethod.VNPAY);
        payment.setStatus(Payment.PaymentStatus.PENDING);
        payment.setDescription(request.getDescription());
        payment.setOrderId(generateOrderId());
        payment.setCreatedAt(LocalDateTime.now());
        payment.setUpdatedAt(LocalDateTime.now());
        
        return paymentRepository.save(payment);
    }

    private Map<String, String> buildVNPayParams(PaymentRequest request, Payment payment, HttpServletRequest httpRequest) {
        Map<String, String> vnp_Params = new HashMap<>();
        
        vnp_Params.put("vnp_Version", vnp_Version);
        vnp_Params.put("vnp_Command", vnp_Command);
        vnp_Params.put("vnp_TmnCode", vnp_TmnCode);
        vnp_Params.put("vnp_Amount", String.valueOf(payment.getAmount().multiply(new BigDecimal(100)).longValue()));
        vnp_Params.put("vnp_CurrCode", vnp_CurrCode);
        
        if (request.getBankCode() != null && !request.getBankCode().trim().isEmpty()) {
            vnp_Params.put("vnp_BankCode", request.getBankCode());
        }
        
        vnp_Params.put("vnp_TxnRef", payment.getOrderId());
        vnp_Params.put("vnp_OrderInfo", request.getOrderInfo());
        vnp_Params.put("vnp_OrderType", vnp_OrderType);
        vnp_Params.put("vnp_Locale", request.getLocale() != null ? request.getLocale() : vnp_Locale);
        vnp_Params.put("vnp_ReturnUrl", vnp_ReturnUrl);
        vnp_Params.put("vnp_IpAddr", getClientIP(httpRequest));
        vnp_Params.put("vnp_CreateDate", getCurrentVNPayDateTime());
        
        // Set expiration time (15 minutes from now)
        Calendar cal = Calendar.getInstance(TimeZone.getTimeZone("Etc/GMT+7"));
        cal.add(Calendar.MINUTE, 15);
        String vnp_ExpireDate = new SimpleDateFormat("yyyyMMddHHmmss").format(cal.getTime());
        vnp_Params.put("vnp_ExpireDate", vnp_ExpireDate);
        
        return vnp_Params;
    }

    private String generateSecureHash(Map<String, String> params) {
        try {
            // Remove vnp_SecureHash if present
            params.remove("vnp_SecureHash");
            
            // Sort parameters
            List<String> fieldNames = new ArrayList<>(params.keySet());
            Collections.sort(fieldNames);
            
            // Build hash data
            StringBuilder hashData = new StringBuilder();
            for (String fieldName : fieldNames) {
                String fieldValue = params.get(fieldName);
                if (fieldValue != null && !fieldValue.isEmpty()) {
                    if (hashData.length() > 0) {
                        hashData.append('&');
                    }
                    hashData.append(fieldName).append('=').append(URLEncoder.encode(fieldValue, StandardCharsets.UTF_8));
                }
            }
            
            // Generate HMAC SHA512
            Mac sha512_HMAC = Mac.getInstance("HmacSHA512");
            SecretKeySpec secret_key = new SecretKeySpec(vnp_HashSecret.getBytes(), "HmacSHA512");
            sha512_HMAC.init(secret_key);
            
            byte[] hash = sha512_HMAC.doFinal(hashData.toString().getBytes(StandardCharsets.UTF_8));
            return bytesToHex(hash);
            
        } catch (Exception e) {
            log.error("Error generating VNPay secure hash", e);
            throw new RuntimeException("Failed to generate secure hash", e);
        }
    }

    private boolean verifySecureHash(Map<String, String> params) {
        try {
            String vnp_SecureHash = params.get("vnp_SecureHash");
            params.remove("vnp_SecureHash");
            
            String calculatedHash = generateSecureHash(params);
            return calculatedHash.equalsIgnoreCase(vnp_SecureHash);
            
        } catch (Exception e) {
            log.error("Error verifying VNPay secure hash", e);
            return false;
        }
    }

    private String buildPaymentUrl(Map<String, String> params) {
        try {
            StringBuilder query = new StringBuilder();
            for (Map.Entry<String, String> entry : params.entrySet()) {
                if (query.length() > 0) {
                    query.append('&');
                }
                query.append(URLEncoder.encode(entry.getKey(), StandardCharsets.UTF_8))
                     .append('=')
                     .append(URLEncoder.encode(entry.getValue(), StandardCharsets.UTF_8));
            }
            
            return vnp_PayUrl + "?" + query.toString();
            
        } catch (Exception e) {
            log.error("Error building VNPay payment URL", e);
            throw new RuntimeException("Failed to build payment URL", e);
        }
    }

    private Map<String, String> extractVNPayParams(HttpServletRequest request) {
        Map<String, String> params = new HashMap<>();
        Enumeration<String> parameterNames = request.getParameterNames();
        
        while (parameterNames.hasMoreElements()) {
            String paramName = parameterNames.nextElement();
            String paramValue = request.getParameter(paramName);
            if (paramValue != null && !paramValue.isEmpty()) {
                params.put(paramName, paramValue);
            }
        }
        
        return params;
    }

    private PaymentResponse processVNPayResponse(Map<String, String> vnpayData) {
        String vnp_TxnRef = vnpayData.get("vnp_TxnRef");
        String vnp_ResponseCode = vnpayData.get("vnp_ResponseCode");
        
        Payment payment = paymentRepository.findByOrderId(vnp_TxnRef)
            .orElseThrow(() -> new RuntimeException("Payment not found: " + vnp_TxnRef));
        
        Payment.PaymentStatus newStatus = "00".equals(vnp_ResponseCode) ? 
            Payment.PaymentStatus.COMPLETED : Payment.PaymentStatus.FAILED;
        
        payment.setStatus(newStatus);
        payment.setGatewayTransactionId(vnpayData.get("vnp_TransactionNo"));
        payment.setGatewayResponseCode(vnp_ResponseCode);
        payment.setGatewayMessage(getVNPayMessage(vnp_ResponseCode));
        payment.setUpdatedAt(LocalDateTime.now());
        
        if ("00".equals(vnp_ResponseCode)) {
            payment.setCompletedAt(LocalDateTime.now());
            paymentService.processSuccessfulPayment(payment);
        }
        
        payment = paymentRepository.save(payment);
        return paymentService.mapToPaymentResponse(payment);
    }

    private String getVNPayMessage(String responseCode) {
        switch (responseCode) {
            case "00": return "Giao dịch thành công";
            case "07": return "Trừ tiền thành công. Giao dịch bị nghi ngờ";
            case "09": return "Thẻ/Tài khoản chưa đăng ký InternetBanking";
            case "10": return "Xác thực thông tin không đúng quá 3 lần";
            case "11": return "Đã hết hạn chờ thanh toán";
            case "12": return "Thẻ/Tài khoản bị khóa";
            case "13": return "Nhập sai mật khẩu OTP";
            case "24": return "Khách hàng hủy giao dịch";
            case "51": return "Tài khoản không đủ số dư";
            case "65": return "Vượt quá hạn mức giao dịch trong ngày";
            case "75": return "Ngân hàng thanh toán đang bảo trì";
            case "79": return "Nhập sai mật khẩu thanh toán quá số lần quy định";
            default: return "Giao dịch thất bại";
        }
    }

    private LocalDateTime parseVNPayDate(String vnpPayDate) {
        try {
            if (vnpPayDate != null && vnpPayDate.length() == 14) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
                return LocalDateTime.parse(vnpPayDate, formatter);
            }
        } catch (Exception e) {
            log.warn("Failed to parse VNPay date: {}", vnpPayDate);
        }
        return LocalDateTime.now();
    }

    private String generateOrderId() {
        return "VNP" + System.currentTimeMillis();
    }

    private String getCurrentVNPayDateTime() {
        Calendar cal = Calendar.getInstance(TimeZone.getTimeZone("Etc/GMT+7"));
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
        return formatter.format(cal.getTime());
    }

    private String getClientIP(HttpServletRequest request) {
        String xfHeader = request.getHeader("X-Forwarded-For");
        if (xfHeader == null || xfHeader.isEmpty() || "unknown".equalsIgnoreCase(xfHeader)) {
            return request.getRemoteAddr();
        }
        return xfHeader.split(",")[0].trim();
    }

    private String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }
} 