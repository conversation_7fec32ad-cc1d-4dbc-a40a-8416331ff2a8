package com.realestate.membership.service;

import com.realestate.membership.dto.MembershipResponse;
import com.realestate.membership.entity.Membership;
import com.realestate.membership.entity.User;
import com.realestate.membership.entity.UserMembership;
import com.realestate.membership.exception.ResourceNotFoundException;
import com.realestate.membership.repository.MembershipRepository;
import com.realestate.membership.repository.UserMembershipRepository;
import com.realestate.membership.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
@Slf4j
public class MembershipService implements CommandLineRunner {
    
    private final MembershipRepository membershipRepository;
    private final UserMembershipRepository userMembershipRepository;
    private final UserRepository userRepository;
    
    public List<MembershipResponse> getAllActiveMemberships() {
        return membershipRepository.findActiveMembershipsByPriceAsc()
                .stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }
    
    public MembershipResponse getMembershipById(Long id) {
        Membership membership = membershipRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Membership not found with id: " + id));
        return mapToResponse(membership);
    }
    
    public UserMembership subscribeMembership(Long userId, Long membershipId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));
        
        Membership membership = membershipRepository.findById(membershipId)
                .orElseThrow(() -> new ResourceNotFoundException("Membership not found with id: " + membershipId));
        
        // Check if user already has an active membership
        Optional<UserMembership> existingMembership = userMembershipRepository
                .findActiveByUserId(userId, LocalDateTime.now());
        
        if (existingMembership.isPresent()) {
            // Cancel existing membership
            UserMembership existing = existingMembership.get();
            existing.setStatus(UserMembership.MembershipStatus.CANCELLED);
            existing.setUpdatedAt(LocalDateTime.now());
            userMembershipRepository.save(existing);
        }
        
        // Create new membership
        UserMembership userMembership = new UserMembership();
        userMembership.setUser(user);
        userMembership.setMembership(membership);
        userMembership.setStartDate(LocalDateTime.now());
        userMembership.setEndDate(LocalDateTime.now().plusMonths(membership.getDurationMonths()));
        userMembership.setStatus(UserMembership.MembershipStatus.ACTIVE);
        userMembership.setAutoRenewal(false);
        userMembership.setPropertiesUsed(0);
        userMembership.setCreatedAt(LocalDateTime.now());
        userMembership.setUpdatedAt(LocalDateTime.now());
        
        return userMembershipRepository.save(userMembership);
    }
    
    public Optional<UserMembership> getUserActiveMembership(Long userId) {
        return userMembershipRepository.findActiveByUserId(userId, LocalDateTime.now());
    }
    
    public List<UserMembership> getUserMembershipHistory(Long userId) {
        return userMembershipRepository.findByUserIdOrderByCreatedAtDesc(userId);
    }
    
    public void processExpiredMemberships() {
        List<UserMembership> expiredMemberships = userMembershipRepository
                .findExpiredMemberships(LocalDateTime.now());
        
        for (UserMembership membership : expiredMemberships) {
            membership.setStatus(UserMembership.MembershipStatus.EXPIRED);
            membership.setUpdatedAt(LocalDateTime.now());
        }
        
        userMembershipRepository.saveAll(expiredMemberships);
    }
    
    private MembershipResponse mapToResponse(Membership membership) {
        MembershipResponse response = new MembershipResponse();
        response.setId(membership.getId());
        response.setName(membership.getName());
        response.setDescription(membership.getDescription());
        response.setPrice(membership.getPrice());
        response.setDurationMonths(membership.getDurationMonths());
        response.setMaxProperties(membership.getMaxProperties());
        response.setFeaturedProperties(membership.getFeaturedProperties());
        response.setPrioritySupport(membership.getPrioritySupport());
        response.setAnalyticsAccess(membership.getAnalyticsAccess());
        response.setMultipleImages(membership.getMultipleImages());
        response.setContactInfoDisplay(membership.getContactInfoDisplay());
        response.setAiContentGeneration(membership.getAiContentGeneration());
        response.setPushTopLimit(membership.getPushTopLimit());
        response.setType(membership.getType());
        response.setIsActive(membership.getIsActive());
        response.setCreatedAt(membership.getCreatedAt());
        response.setUpdatedAt(membership.getUpdatedAt());
        return response;
    }

    @Override
    public void run(String... args) throws Exception {
        initializeDefaultMemberships();
    }

    private void initializeDefaultMemberships() {
        if (membershipRepository.count() == 0) {
            // Basic membership
            Membership basicMembership = new Membership();
            basicMembership.setName("Basic");
            basicMembership.setType(Membership.MembershipType.BASIC);
            basicMembership.setPrice(new BigDecimal("99000"));
            basicMembership.setDurationMonths(1);
            basicMembership.setMaxProperties(10);
            basicMembership.setFeaturedProperties(0);
            basicMembership.setPrioritySupport(false);
            basicMembership.setAnalyticsAccess(false);
            basicMembership.setMultipleImages(true);
            basicMembership.setContactInfoDisplay(true);
            basicMembership.setAiContentGeneration(false);
            basicMembership.setPushTopLimit(0);
            basicMembership.setIsActive(true);
            basicMembership.setDescription("Gói cơ bản cho người dùng cá nhân - Đăng bài thông thường");
            membershipRepository.save(basicMembership);

            // Advanced membership
            Membership advancedMembership = new Membership();
            advancedMembership.setName("Advanced");
            advancedMembership.setType(Membership.MembershipType.ADVANCED);
            advancedMembership.setPrice(new BigDecimal("299000"));
            advancedMembership.setDurationMonths(1);
            advancedMembership.setMaxProperties(50);
            advancedMembership.setFeaturedProperties(10);
            advancedMembership.setPrioritySupport(true);
            advancedMembership.setAnalyticsAccess(true);
            advancedMembership.setMultipleImages(true);
            advancedMembership.setContactInfoDisplay(true);
            advancedMembership.setAiContentGeneration(true);
            advancedMembership.setPushTopLimit(10);
            advancedMembership.setIsActive(true);
            advancedMembership.setDescription("Gói nâng cao với AI Generation và Push Top (10 lượt/tháng)");
            membershipRepository.save(advancedMembership);

            log.info("Default memberships initialized: Basic and Advanced");
        }
    }
}
