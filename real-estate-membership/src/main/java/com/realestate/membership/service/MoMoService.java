package com.realestate.membership.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.realestate.membership.dto.PaymentCallbackRequest;
import com.realestate.membership.dto.PaymentRequest;
import com.realestate.membership.dto.PaymentResponse;
import com.realestate.membership.entity.Payment;
import com.realestate.membership.entity.User;
import com.realestate.membership.repository.PaymentRepository;
import com.realestate.membership.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import jakarta.servlet.http.HttpServletRequest;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class MoMoService {

    private final PaymentRepository paymentRepository;
    private final UserRepository userRepository;
    private final PaymentService paymentService;
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    
    // MoMo Configuration Constants
    @Value("${momo.partner-code:MOMOBKUN20180529}")
    private String partnerCode;
    
    @Value("${momo.access-key:klm05TvNBzhg7h7j}")
    private String accessKey;
    
    @Value("${momo.secret-key:at67qH6mk8w5Y1nAyMoYKMWACiEi2bsa}")
    private String secretKey;
    
    @Value("${momo.api-endpoint:https://test-payment.momo.vn/v2/gateway/api/create}")
    private String momoApiEndpoint;
    
    @Value("${momo.query-endpoint:https://test-payment.momo.vn/v2/gateway/api/query}")
    private String momoQueryEndpoint;
    
    @Value("${momo.return-url:http://localhost:3000/payment/result}")
    private String returnUrl;
    
    @Value("${momo.notify-url:http://localhost:8080/api/v1/payments/momo/callback}")
    private String notifyUrl;

    // =====================================================
    // PAYMENT CREATION
    // =====================================================

    public Map<String, Object> createPayment(PaymentRequest request, HttpServletRequest httpRequest) {
        try {
            // Validate request
            if (!request.isValidForMoMo()) {
                throw new IllegalArgumentException("Invalid payment method for MoMo");
            }
            
            // Set default values
            request.setDefaultValues();
            
            // Get current user
            String username = SecurityContextHolder.getContext().getAuthentication().getName();
            User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new RuntimeException("User not found"));
            
            // Create payment record
            Payment payment = createPaymentRecord(request, user);
            
            // Create MoMo payment request
            Map<String, Object> momoRequest = buildMoMoRequest(request, payment, httpRequest);
            
            // Call MoMo API
            Map<String, Object> momoResponse = callMoMoAPI(momoRequest);
            
            // Update payment with MoMo response
            updatePaymentWithMoMoResponse(payment, momoResponse);
            
            log.info("MoMo payment created for payment ID: {}", payment.getId());
            
            return Map.of(
                "paymentId", payment.getId(),
                "payUrl", momoResponse.get("payUrl"),
                "orderId", payment.getOrderId(),
                "amount", payment.getAmount(),
                "description", payment.getDescription(),
                "status", "PENDING",
                "gateway", "MOMO",
                "qrCodeUrl", momoResponse.getOrDefault("qrCodeUrl", ""),
                "deeplink", momoResponse.getOrDefault("deeplink", ""),
                "expires", System.currentTimeMillis() + (15 * 60 * 1000) // 15 minutes
            );
            
        } catch (Exception e) {
            log.error("Error creating MoMo payment", e);
            throw new RuntimeException("Failed to create MoMo payment: " + e.getMessage());
        }
    }

    // =====================================================
    // CALLBACK PROCESSING
    // =====================================================

    public PaymentResponse processCallback(PaymentCallbackRequest callbackRequest, HttpServletRequest request) {
        try {
            log.info("Processing MoMo callback: {}", callbackRequest);
            
            // Verify signature
            if (!verifyMoMoSignature(callbackRequest)) {
                throw new SecurityException("Invalid MoMo signature");
            }
            
            // Find payment by order ID
            Payment payment = paymentRepository.findByOrderId(callbackRequest.getOrderId())
                .orElseThrow(() -> new RuntimeException("Payment not found: " + callbackRequest.getOrderId()));
            
            // Update payment status based on MoMo response
            Payment.PaymentStatus newStatus = callbackRequest.isSuccessfulMoMo() ? 
                Payment.PaymentStatus.COMPLETED : Payment.PaymentStatus.FAILED;
            
            payment.setStatus(newStatus);
            payment.setGatewayTransactionId(callbackRequest.getGatewayTransactionId());
            payment.setGatewayResponseCode(callbackRequest.getResultCode());
            payment.setGatewayMessage(callbackRequest.getMessage());
            payment.setUpdatedAt(LocalDateTime.now());
            
            if (callbackRequest.isSuccessfulMoMo()) {
                payment.setPaymentDate(LocalDateTime.now());
                payment.setCompletedAt(LocalDateTime.now());
                
                // Process successful payment
                paymentService.processSuccessfulPayment(payment);
            }
            
            payment = paymentRepository.save(payment);
            
            log.info("MoMo callback processed for payment {}: status={}", 
                    payment.getId(), newStatus);
            
            return paymentService.mapToPaymentResponse(payment);
            
        } catch (Exception e) {
            log.error("Error processing MoMo callback", e);
            throw new RuntimeException("Failed to process MoMo callback: " + e.getMessage());
        }
    }

    public void processWebhook(Map<String, Object> webhookData, HttpServletRequest request) {
        try {
            log.info("Processing MoMo webhook: {}", webhookData);
            
            // Convert webhook data to callback request
            PaymentCallbackRequest callbackRequest = convertWebhookToCallback(webhookData);
            
            // Process as regular callback
            processCallback(callbackRequest, request);
            
        } catch (Exception e) {
            log.error("Error processing MoMo webhook", e);
            throw new RuntimeException("Failed to process MoMo webhook: " + e.getMessage());
        }
    }

    // =====================================================
    // PAYMENT QUERY
    // =====================================================

    public PaymentResponse queryPaymentStatus(String orderId) {
        try {
            // Build query request
            Map<String, Object> queryRequest = buildMoMoQueryRequest(orderId);
            
            // Call MoMo query API
            Map<String, Object> queryResponse = callMoMoQueryAPI(queryRequest);
            
            // Find and update payment
            Payment payment = paymentRepository.findByOrderId(orderId)
                .orElseThrow(() -> new RuntimeException("Payment not found: " + orderId));
            
            // Update payment based on query response
            updatePaymentWithQueryResponse(payment, queryResponse);
            
            return paymentService.mapToPaymentResponse(payment);
            
        } catch (Exception e) {
            log.error("Error querying MoMo payment status", e);
            throw new RuntimeException("Failed to query payment status: " + e.getMessage());
        }
    }

    // =====================================================
    // PRIVATE HELPER METHODS
    // =====================================================

    private Payment createPaymentRecord(PaymentRequest request, User user) {
        Payment payment = new Payment();
        payment.setUser(user);
        payment.setMembershipId(request.getMembershipId());
        payment.setAmount(request.getAmount());
        payment.setPaymentMethod(Payment.PaymentMethod.MOMO);
        payment.setStatus(Payment.PaymentStatus.PENDING);
        payment.setDescription(request.getDescription());
        payment.setOrderId(generateOrderId());
        payment.setCreatedAt(LocalDateTime.now());
        payment.setUpdatedAt(LocalDateTime.now());
        
        return paymentRepository.save(payment);
    }

    private Map<String, Object> buildMoMoRequest(PaymentRequest request, Payment payment, HttpServletRequest httpRequest) {
        String requestId = UUID.randomUUID().toString();
        String orderInfo = request.getOrderInfo();
        String extraData = request.getExtraData() != null ? request.getExtraData() : "";
        
        Map<String, Object> momoRequest = new HashMap<>();
        momoRequest.put("partnerCode", partnerCode);
        momoRequest.put("partnerName", "Real Estate Membership");
        momoRequest.put("storeId", "RealEstate");
        momoRequest.put("requestId", requestId);
        momoRequest.put("amount", payment.getAmount().longValue());
        momoRequest.put("orderId", payment.getOrderId());
        momoRequest.put("orderInfo", orderInfo);
        momoRequest.put("redirectUrl", returnUrl);
        momoRequest.put("ipnUrl", notifyUrl);
        momoRequest.put("lang", "vi");
        momoRequest.put("extraData", extraData);
        momoRequest.put("requestType", request.getRequestType());
        momoRequest.put("signature", ""); // Will be set after signature generation
        
        // Generate signature
        String signature = generateMoMoSignature(momoRequest);
        momoRequest.put("signature", signature);
        
        return momoRequest;
    }

    private Map<String, Object> callMoMoAPI(Map<String, Object> request) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(request, headers);
            
            ResponseEntity<Map> response = restTemplate.postForEntity(
                momoApiEndpoint, entity, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                return response.getBody();
            } else {
                throw new RuntimeException("MoMo API call failed with status: " + response.getStatusCode());
            }
            
        } catch (Exception e) {
            log.error("Error calling MoMo API", e);
            throw new RuntimeException("Failed to call MoMo API: " + e.getMessage());
        }
    }

    private Map<String, Object> buildMoMoQueryRequest(String orderId) {
        String requestId = UUID.randomUUID().toString();
        
        Map<String, Object> queryRequest = new HashMap<>();
        queryRequest.put("partnerCode", partnerCode);
        queryRequest.put("requestId", requestId);
        queryRequest.put("orderId", orderId);
        queryRequest.put("lang", "vi");
        queryRequest.put("signature", ""); // Will be set after signature generation
        
        // Generate signature for query
        String signature = generateMoMoQuerySignature(queryRequest);
        queryRequest.put("signature", signature);
        
        return queryRequest;
    }

    private Map<String, Object> callMoMoQueryAPI(Map<String, Object> request) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(request, headers);
            
            ResponseEntity<Map> response = restTemplate.postForEntity(
                momoQueryEndpoint, entity, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                return response.getBody();
            } else {
                throw new RuntimeException("MoMo query API call failed with status: " + response.getStatusCode());
            }
            
        } catch (Exception e) {
            log.error("Error calling MoMo query API", e);
            throw new RuntimeException("Failed to call MoMo query API: " + e.getMessage());
        }
    }

    private String generateMoMoSignature(Map<String, Object> request) {
        try {
            String rawData = String.format(
                "accessKey=%s&amount=%s&extraData=%s&ipnUrl=%s&orderId=%s&orderInfo=%s&partnerCode=%s&redirectUrl=%s&requestId=%s&requestType=%s",
                accessKey,
                request.get("amount"),
                request.get("extraData"),
                request.get("ipnUrl"),
                request.get("orderId"),
                request.get("orderInfo"),
                request.get("partnerCode"),
                request.get("redirectUrl"),
                request.get("requestId"),
                request.get("requestType")
            );
            
            return hmacSHA256(rawData, secretKey);
            
        } catch (Exception e) {
            log.error("Error generating MoMo signature", e);
            throw new RuntimeException("Failed to generate MoMo signature", e);
        }
    }

    private String generateMoMoQuerySignature(Map<String, Object> request) {
        try {
            String rawData = String.format(
                "accessKey=%s&orderId=%s&partnerCode=%s&requestId=%s",
                accessKey,
                request.get("orderId"),
                request.get("partnerCode"),
                request.get("requestId")
            );
            
            return hmacSHA256(rawData, secretKey);
            
        } catch (Exception e) {
            log.error("Error generating MoMo query signature", e);
            throw new RuntimeException("Failed to generate MoMo query signature", e);
        }
    }

    private boolean verifyMoMoSignature(PaymentCallbackRequest callback) {
        try {
            String rawData = String.format(
                "accessKey=%s&amount=%s&extraData=%s&message=%s&orderId=%s&orderInfo=%s&orderType=%s&partnerCode=%s&payType=%s&requestId=%s&responseTime=%s&resultCode=%s&transId=%s",
                accessKey,
                callback.getAmount(),
                callback.getExtraData() != null ? callback.getExtraData() : "",
                callback.getMessage(),
                callback.getOrderId(),
                callback.getOrderInfo(),
                callback.getOrderType(),
                callback.getPartnerCode(),
                callback.getPayType(),
                callback.getRequestId(),
                callback.getResponseTime(),
                callback.getResultCode(),
                callback.getTransId()
            );
            
            String expectedSignature = hmacSHA256(rawData, secretKey);
            return expectedSignature.equals(callback.getSignature());
            
        } catch (Exception e) {
            log.error("Error verifying MoMo signature", e);
            return false;
        }
    }

    private void updatePaymentWithMoMoResponse(Payment payment, Map<String, Object> response) {
        payment.setGatewayTransactionId((String) response.get("requestId"));
        payment.setGatewayResponseCode((String) response.get("resultCode"));
        payment.setGatewayMessage((String) response.get("message"));
        paymentRepository.save(payment);
    }

    private void updatePaymentWithQueryResponse(Payment payment, Map<String, Object> response) {
        String resultCode = (String) response.get("resultCode");
        Payment.PaymentStatus newStatus = "0".equals(resultCode) ? 
            Payment.PaymentStatus.COMPLETED : Payment.PaymentStatus.FAILED;
        
        payment.setStatus(newStatus);
        payment.setGatewayResponseCode(resultCode);
        payment.setGatewayMessage((String) response.get("message"));
        payment.setUpdatedAt(LocalDateTime.now());
        
        if ("0".equals(resultCode)) {
            payment.setCompletedAt(LocalDateTime.now());
            paymentService.processSuccessfulPayment(payment);
        }
        
        paymentRepository.save(payment);
    }

    private PaymentCallbackRequest convertWebhookToCallback(Map<String, Object> webhookData) {
        PaymentCallbackRequest callback = new PaymentCallbackRequest();
        
        callback.setPartnerCode((String) webhookData.get("partnerCode"));
        callback.setOrderId((String) webhookData.get("orderId"));
        callback.setRequestId((String) webhookData.get("requestId"));
        callback.setAmount(new BigDecimal(webhookData.get("amount").toString()));
        callback.setOrderInfo((String) webhookData.get("orderInfo"));
        callback.setOrderType((String) webhookData.get("orderType"));
        callback.setTransId(((Number) webhookData.get("transId")).longValue());
        callback.setResultCode((String) webhookData.get("resultCode"));
        callback.setMessage((String) webhookData.get("message"));
        callback.setPayType((String) webhookData.get("payType"));
        callback.setResponseTime((String) webhookData.get("responseTime"));
        callback.setExtraData((String) webhookData.get("extraData"));
        callback.setSignature((String) webhookData.get("signature"));
        
        return callback;
    }

    private String hmacSHA256(String data, String key) throws Exception {
        Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
        SecretKeySpec secret_key = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
        sha256_HMAC.init(secret_key);
        byte[] hash = sha256_HMAC.doFinal(data.getBytes(StandardCharsets.UTF_8));
        
        StringBuilder result = new StringBuilder();
        for (byte b : hash) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }

    private String generateOrderId() {
        return "MOMO" + System.currentTimeMillis();
    }
} 