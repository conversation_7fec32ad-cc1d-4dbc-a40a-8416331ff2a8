package com.realestate.membership.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.realestate.membership.entity.Notification;
import com.realestate.membership.entity.User;
import com.realestate.membership.repository.NotificationRepository;
import com.realestate.membership.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.mail.MailException;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class NotificationService {

    private volatile boolean initialized = false;

    private final NotificationRepository notificationRepository;
    private final UserRepository userRepository;
    private final JavaMailSender mailSender;
    private final ObjectMapper objectMapper;

    // Configuration
    @Value("${spring.mail.username}")
    private String fromEmail;

    @Value("${app.frontend.url:http://localhost:3000}")
    private String frontendUrl;

    @PostConstruct
    public void initializeService() {
        // Delay initialization to ensure database is ready
        CompletableFuture.runAsync(() -> {
            try {
                Thread.sleep(5000); // Wait 5 seconds
                initialized = true;
                log.info("NotificationService initialized and ready for scheduled tasks");
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("Failed to initialize NotificationService", e);
            }
        });
    }

    // =====================================================
    // NOTIFICATION CREATION
    // =====================================================

    public Notification createNotification(Notification notification) {
        try {
            // Prevent duplicate notifications
            if (isDuplicate(notification)) {
                log.debug("Skipping duplicate notification for user {} type {}", 
                         notification.getUser().getId(), notification.getType());
                return null;
            }

            Notification saved = notificationRepository.save(notification);
            log.info("Created notification {} for user {}", saved.getId(), saved.getUser().getUsername());

            // Send immediately if not scheduled
            if (notification.getScheduledAt() == null) {
                sendNotificationAsync(saved);
            }

            return saved;
        } catch (Exception e) {
            log.error("Failed to create notification", e);
            throw new RuntimeException("Failed to create notification: " + e.getMessage());
        }
    }

    public void createAndSendNotification(User user, Notification.NotificationType type, 
                                        String title, String message, Map<String, Object> options) {
        Notification notification = new Notification();
        notification.setUser(user);
        notification.setType(type);
        notification.setTitle(title);
        notification.setMessage(message);
        notification.setPriority(getDefaultPriority(type));
        notification.setCreatedAt(LocalDateTime.now());

        // Apply options
        if (options != null) {
            notification.setSendEmail((Boolean) options.getOrDefault("sendEmail", false));
            notification.setSendPush((Boolean) options.getOrDefault("sendPush", false));
            notification.setSendSms((Boolean) options.getOrDefault("sendSms", false));
            notification.setActionUrl((String) options.get("actionUrl"));
            notification.setActionText((String) options.get("actionText"));
            notification.setPropertyId((Long) options.get("propertyId"));
            notification.setMembershipId((Long) options.get("membershipId"));
            notification.setPaymentId((Long) options.get("paymentId"));
            // Convert metadata Map to JSON string
            Object metadata = options.get("metadata");
            if (metadata != null) {
                try {
                    notification.setMetadata(objectMapper.writeValueAsString(metadata));
                } catch (Exception e) {
                    log.warn("Failed to serialize metadata: {}", e.getMessage());
                    notification.setMetadata("{}");
                }
            }
            
            if (options.containsKey("expiresAt")) {
                notification.setExpiresAt((LocalDateTime) options.get("expiresAt"));
            }
            if (options.containsKey("scheduledAt")) {
                notification.setScheduledAt((LocalDateTime) options.get("scheduledAt"));
            }
        }

        createNotification(notification);
    }

    // =====================================================
    // CONVENIENCE METHODS FOR COMMON NOTIFICATIONS
    // =====================================================

    public void notifyMembershipExpiring(User user, int daysLeft, Long membershipId) {
        Notification notification = Notification.membershipExpiring(user, daysLeft, membershipId);
        createNotification(notification);
    }

    public void notifyPropertyApproved(User user, Long propertyId, String propertyTitle) {
        Notification notification = Notification.propertyApproved(user, propertyId, propertyTitle);
        createNotification(notification);
    }

    public void notifyPropertyRejected(User user, Long propertyId, String propertyTitle, String reason) {
        Map<String, Object> options = Map.of(
            "sendEmail", true,
            "sendPush", true,
            "propertyId", propertyId,
            "actionUrl", "/properties/" + propertyId + "/edit",
            "actionText", "Chỉnh sửa tin đăng",
            "expiresAt", LocalDateTime.now().plusDays(7)
        );

        createAndSendNotification(user, Notification.NotificationType.PROPERTY_REJECTED,
            "Tin đăng bị từ chối",
            String.format("Tin đăng '%s' bị từ chối. Lý do: %s", propertyTitle, reason),
            options);
    }

    public void notifyPaymentSuccess(User user, Long paymentId, String amount) {
        Notification notification = Notification.paymentSuccess(user, paymentId, amount);
        createNotification(notification);
    }

    public void notifyContactInquiry(User user, Long propertyId, String inquirerName, String propertyTitle) {
        Notification notification = Notification.contactInquiry(user, propertyId, inquirerName, propertyTitle);
        createNotification(notification);
    }

    public void notifyWelcome(User user) {
        Map<String, Object> options = Map.of(
            "sendEmail", true,
            "actionUrl", "/dashboard",
            "actionText", "Bắt đầu sử dụng",
            "expiresAt", LocalDateTime.now().plusDays(30)
        );

        createAndSendNotification(user, Notification.NotificationType.WELCOME,
            "Chào mừng đến với hệ thống!",
            String.format("Xin chào %s! Chào mừng bạn đến với nền tảng bất động sản của chúng tôi.", user.getFirstName()),
            options);
    }

    // =====================================================
    // NOTIFICATION RETRIEVAL
    // =====================================================

    public Page<Notification> getUserNotifications(Pageable pageable) {
        User currentUser = getCurrentUser();
        return notificationRepository.findByUserAndStatusNot(
            currentUser, Notification.NotificationStatus.DELETED, pageable);
    }

    public List<Notification> getUnreadNotifications() {
        User currentUser = getCurrentUser();
        return notificationRepository.findByUserAndStatus(
            currentUser, Notification.NotificationStatus.UNREAD);
    }

    public Long getUnreadCount() {
        User currentUser = getCurrentUser();
        return notificationRepository.countUnreadByUser(currentUser);
    }

    public Optional<Notification> getNotificationById(Long id) {
        return notificationRepository.findById(id);
    }

    // =====================================================
    // NOTIFICATION MANAGEMENT
    // =====================================================

    public void markAsRead(Long notificationId) {
        User currentUser = getCurrentUser();
        Optional<Notification> notificationOpt = notificationRepository.findById(notificationId);
        
        if (notificationOpt.isPresent()) {
            Notification notification = notificationOpt.get();
            if (notification.getUser().getId().equals(currentUser.getId())) {
                notification.markAsRead();
                notificationRepository.save(notification);
                log.info("Marked notification {} as read for user {}", notificationId, currentUser.getUsername());
            }
        }
    }

    public void markAllAsRead() {
        User currentUser = getCurrentUser();
        int updated = notificationRepository.markAllAsReadByUser(currentUser, LocalDateTime.now());
        log.info("Marked {} notifications as read for user {}", updated, currentUser.getUsername());
    }

    public void markAsArchived(Long notificationId) {
        User currentUser = getCurrentUser();
        Optional<Notification> notificationOpt = notificationRepository.findById(notificationId);
        
        if (notificationOpt.isPresent()) {
            Notification notification = notificationOpt.get();
            if (notification.getUser().getId().equals(currentUser.getId())) {
                notification.setStatus(Notification.NotificationStatus.ARCHIVED);
                notification.setUpdatedAt(LocalDateTime.now());
                notificationRepository.save(notification);
            }
        }
    }

    public void deleteNotification(Long notificationId) {
        User currentUser = getCurrentUser();
        Optional<Notification> notificationOpt = notificationRepository.findById(notificationId);
        
        if (notificationOpt.isPresent()) {
            Notification notification = notificationOpt.get();
            if (notification.getUser().getId().equals(currentUser.getId())) {
                notification.setStatus(Notification.NotificationStatus.DELETED);
                notification.setUpdatedAt(LocalDateTime.now());
                notificationRepository.save(notification);
            }
        }
    }

    // =====================================================
    // NOTIFICATION SENDING
    // =====================================================

    @Async
    public CompletableFuture<Void> sendNotificationAsync(Notification notification) {
        try {
            if (notification.getSendEmail() && !notification.getEmailSent()) {
                sendEmailNotification(notification);
            }

            if (notification.getSendPush() && !notification.getPushSent()) {
                sendPushNotification(notification);
            }

            if (notification.getSendSms() && !notification.getSmsSent()) {
                sendSmsNotification(notification);
            }

            notification.markAsSent();
            notificationRepository.save(notification);

        } catch (Exception e) {
            log.error("Failed to send notification {}", notification.getId(), e);
            notification.setLastError(e.getMessage());
            notification.incrementRetryCount();
            notificationRepository.save(notification);
        }

        return CompletableFuture.completedFuture(null);
    }

    private void sendEmailNotification(Notification notification) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(fromEmail);
            message.setTo(notification.getUser().getEmail());
            message.setSubject(notification.getTitle());
            
            StringBuilder emailBody = new StringBuilder();
            emailBody.append(notification.getMessage());
            
            if (notification.getActionUrl() != null) {
                String fullUrl = frontendUrl + notification.getActionUrl();
                emailBody.append("\n\n");
                emailBody.append("Nhấn vào link sau để thực hiện hành động: ");
                emailBody.append(fullUrl);
            }
            
            emailBody.append("\n\n");
            emailBody.append("Trân trọng,\n");
            emailBody.append("Đội ngũ hỗ trợ");

            message.setText(emailBody.toString());
            mailSender.send(message);

            notification.setEmailSent(true);
            log.info("Email sent for notification {} to {}", notification.getId(), notification.getUser().getEmail());

        } catch (MailException e) {
            log.error("Failed to send email for notification {}", notification.getId(), e);
            throw e;
        }
    }

    private void sendPushNotification(Notification notification) {
        // TODO: Implement push notification (Firebase, OneSignal, etc.)
        // For now, just mark as sent
        notification.setPushSent(true);
        log.info("Push notification sent for notification {}", notification.getId());
    }

    private void sendSmsNotification(Notification notification) {
        // TODO: Implement SMS notification (Twilio, AWS SNS, etc.)
        // For now, just mark as sent
        notification.setSmsSent(true);
        log.info("SMS notification sent for notification {}", notification.getId());
    }

    // =====================================================
    // SCHEDULED TASKS
    // =====================================================

    @Scheduled(fixedRate = 60000) // Every minute
    public void processPendingNotifications() {
        if (!initialized) {
            log.debug("NotificationService not yet initialized, skipping pending notifications processing");
            return;
        }

        try {
            List<Notification> pendingNotifications = notificationRepository.findPendingNotifications();
            log.debug("Processing {} pending notifications", pendingNotifications.size());

            for (Notification notification : pendingNotifications) {
                if (!notification.isExpired()) {
                    sendNotificationAsync(notification);
                }
            }
        } catch (Exception e) {
            log.error("Error processing pending notifications", e);
        }
    }

    @Scheduled(fixedRate = 300000) // Every 5 minutes
    public void processScheduledNotifications() {
        if (!initialized) {
            log.debug("NotificationService not yet initialized, skipping scheduled notifications processing");
            return;
        }

        try {
            List<Notification> scheduledNotifications = notificationRepository
                .findScheduledNotificationsReadyToSend(LocalDateTime.now());

            log.debug("Processing {} scheduled notifications", scheduledNotifications.size());

            for (Notification notification : scheduledNotifications) {
                if (!notification.isExpired()) {
                    sendNotificationAsync(notification);
                }
            }
        } catch (Exception e) {
            log.error("Error processing scheduled notifications", e);
        }
    }

    @Scheduled(fixedRate = 3600000) // Every hour
    public void retryFailedNotifications() {
        if (!initialized) {
            log.debug("NotificationService not yet initialized, skipping failed notifications retry");
            return;
        }

        try {
            List<Notification> retryableNotifications = notificationRepository.findRetryableNotifications();
            log.debug("Retrying {} failed notifications", retryableNotifications.size());

            for (Notification notification : retryableNotifications) {
                if (!notification.isExpired() && notification.canRetry()) {
                    sendNotificationAsync(notification);
                }
            }
        } catch (Exception e) {
            log.error("Error retrying failed notifications", e);
        }
    }

    @Scheduled(cron = "0 0 2 * * ?") // Daily at 2 AM
    public void cleanupOldNotifications() {
        if (!initialized) {
            log.debug("NotificationService not yet initialized, skipping old notifications cleanup");
            return;
        }

        try {
            LocalDateTime cutoffDate = LocalDateTime.now().minusDays(30);
            int deleted = notificationRepository.deleteOldNotifications(cutoffDate);
            log.info("Cleaned up {} old notifications", deleted);
        } catch (Exception e) {
            log.error("Error cleaning up old notifications", e);
        }
    }

    // =====================================================
    // HELPER METHODS
    // =====================================================

    private User getCurrentUser() {
        String username = SecurityContextHolder.getContext().getAuthentication().getName();
        return userRepository.findByUsername(username)
            .orElseThrow(() -> new RuntimeException("User not found"));
    }

    private boolean isDuplicate(Notification notification) {
        // Check for duplicate notifications in the last hour
        LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
        Long count = notificationRepository.countRecentNotificationsByType(
            notification.getUser(), notification.getType(), oneHourAgo);
        return count > 0;
    }

    private Notification.NotificationPriority getDefaultPriority(Notification.NotificationType type) {
        return switch (type) {
            case MEMBERSHIP_EXPIRY_WARNING, MEMBERSHIP_EXPIRED, PROPERTY_CONTACT_INQUIRY -> 
                Notification.NotificationPriority.HIGH;
            case SYSTEM_MAINTENANCE, PAYMENT_FAILED -> 
                Notification.NotificationPriority.URGENT;
            case WELCOME, NEWSLETTER, PROMOTION -> 
                Notification.NotificationPriority.LOW;
            default -> 
                Notification.NotificationPriority.NORMAL;
        };
    }
} 