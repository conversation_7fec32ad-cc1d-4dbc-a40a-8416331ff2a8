package com.realestate.membership.service;

import com.realestate.membership.entity.Category;
import com.realestate.membership.entity.Membership;
import com.realestate.membership.entity.AIAgent;
import com.realestate.membership.repository.CategoryRepository;
import com.realestate.membership.repository.MembershipRepository;
import com.realestate.membership.repository.AIAgentRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Service
@RequiredArgsConstructor
@Slf4j
@Order(2) // Run after AdminInitializationService
public class DataInitializationService implements CommandLineRunner {

    private final MembershipRepository membershipRepository;
    private final CategoryRepository categoryRepository;
    private final AIAgentRepository aiAgentRepository;

    @Override
    @Transactional
    public void run(String... args) throws Exception {
        log.info("🔄 Initializing sample data...");
        
        initializeMemberships();
        initializeCategories();
        initializeAIAgents();
        
        log.info("✅ Sample data initialization completed!");
    }

    private void initializeMemberships() {
        if (membershipRepository.count() == 0) {
            log.info("📦 Creating sample memberships...");

            // FREE Membership (Default for new users)
            Membership freeMembership = new Membership();
            freeMembership.setName("FREE");
            freeMembership.setDescription("Gói miễn phí - Xem bài đăng, không thể đăng bài");
            freeMembership.setType(Membership.MembershipType.BASIC);
            freeMembership.setPrice(new BigDecimal("0"));
            freeMembership.setDurationMonths(12); // 1 year free
            freeMembership.setMaxProperties(0); // Cannot post properties
            freeMembership.setFeaturedProperties(0);
            freeMembership.setPrioritySupport(false);
            freeMembership.setAnalyticsAccess(false);
            freeMembership.setMultipleImages(false);
            freeMembership.setContactInfoDisplay(false);
            freeMembership.setAiContentGeneration(false);
            freeMembership.setPushTopLimit(0);
            freeMembership.setIsActive(true);
            freeMembership.setCreatedAt(LocalDateTime.now());
            freeMembership.setUpdatedAt(LocalDateTime.now());
            membershipRepository.save(freeMembership);

            // BASIC Membership
            Membership basicMembership = new Membership();
            basicMembership.setName("BASIC");
            basicMembership.setDescription("Gói cơ bản cho người dùng cá nhân - Đăng bài thông thường");
            basicMembership.setType(Membership.MembershipType.BASIC);
            basicMembership.setPrice(new BigDecimal("99000"));
            basicMembership.setDurationMonths(1);
            basicMembership.setMaxProperties(10);
            basicMembership.setFeaturedProperties(0);
            basicMembership.setPrioritySupport(false);
            basicMembership.setAnalyticsAccess(false);
            basicMembership.setMultipleImages(true);
            basicMembership.setContactInfoDisplay(true);
            basicMembership.setAiContentGeneration(false);
            basicMembership.setPushTopLimit(0);
            basicMembership.setIsActive(true);
            basicMembership.setCreatedAt(LocalDateTime.now());
            basicMembership.setUpdatedAt(LocalDateTime.now());
            membershipRepository.save(basicMembership);

            // ADVANCED Membership
            Membership advancedMembership = new Membership();
            advancedMembership.setName("Advanced");
            advancedMembership.setDescription("Gói nâng cao với AI Generation và Push Top (10 lượt/tháng)");
            advancedMembership.setType(Membership.MembershipType.ADVANCED);
            advancedMembership.setPrice(new BigDecimal("299000"));
            advancedMembership.setDurationMonths(1);
            advancedMembership.setMaxProperties(50);
            advancedMembership.setFeaturedProperties(10);
            advancedMembership.setPrioritySupport(true);
            advancedMembership.setAnalyticsAccess(true);
            advancedMembership.setMultipleImages(true);
            advancedMembership.setContactInfoDisplay(true);
            advancedMembership.setAiContentGeneration(true);
            advancedMembership.setPushTopLimit(10);
            advancedMembership.setIsActive(true);
            advancedMembership.setCreatedAt(LocalDateTime.now());
            advancedMembership.setUpdatedAt(LocalDateTime.now());
            membershipRepository.save(advancedMembership);



            log.info("✅ Created {} membership plans: FREE, BASIC and ADVANCED", membershipRepository.count());
        } else {
            log.info("📦 Memberships already exist, skipping initialization");
        }
    }

    private void initializeCategories() {
        if (categoryRepository.count() == 0) {
            log.info("📂 Creating sample categories...");
            
            String[] categoryData = {
                "Apartment,Căn hộ chung cư,/icons/apartment.svg",
                "House,Nhà riêng,/icons/house.svg",
                "Villa,Biệt thự,/icons/villa.svg",
                "Townhouse,Nhà phố,/icons/townhouse.svg",
                "Office,Văn phòng,/icons/office.svg",
                "Shop,Cửa hàng,/icons/shop.svg",
                "Land,Đất nền,/icons/land.svg",
                "Warehouse,Kho xưởng,/icons/warehouse.svg"
            };

            for (int i = 0; i < categoryData.length; i++) {
                String[] parts = categoryData[i].split(",");
                Category category = new Category();
                category.setName(parts[0]);
                category.setDescription(parts[1]);
                category.setIconUrl(parts[2]);
                category.setIsActive(true);
                category.setSortOrder(i + 1);
                category.setCreatedAt(LocalDateTime.now());
                category.setUpdatedAt(LocalDateTime.now());
                categoryRepository.save(category);
            }

            log.info("✅ Created {} categories", categoryRepository.count());
        } else {
            log.info("📂 Categories already exist, skipping initialization");
        }
    }

    private void initializeAIAgents() {
        if (aiAgentRepository.count() == 0) {
            log.info("🤖 Creating sample AI agents...");
            
            // Property Assistant
            AIAgent propertyAssistant = new AIAgent();
            propertyAssistant.setName("property_assistant");
            propertyAssistant.setDisplayName("Trợ lý Bất động sản");
            propertyAssistant.setDescription("AI assistant for property search and recommendations");
            propertyAssistant.setModelName("gpt-4");
            propertyAssistant.setApiProvider("OPENAI");
            propertyAssistant.setPromptTemplate("Bạn là chuyên gia tư vấn bất động sản thông minh tại Việt Nam. Hãy giúp người dùng tìm kiếm và tư vấn về bất động sản phù hợp với nhu cầu của họ. Câu hỏi: {question}");
            propertyAssistant.setSystemPrompt("Bạn là chuyên gia tư vấn bất động sản với nhiều năm kinh nghiệm tại Việt Nam. Hãy tư vấn một cách chuyên nghiệp và hữu ích.");
            propertyAssistant.setTemperature(new BigDecimal("0.3"));
            propertyAssistant.setMaxTokens(2000);
            propertyAssistant.setPriority(1);
            propertyAssistant.setIsActive(true);
            propertyAssistant.setResponseFormat("TEXT");
            propertyAssistant.setCreatedAt(LocalDateTime.now());
            propertyAssistant.setUpdatedAt(LocalDateTime.now());
            aiAgentRepository.save(propertyAssistant);

            // Market Analyzer
            AIAgent marketAnalyzer = new AIAgent();
            marketAnalyzer.setName("market_analyzer");
            marketAnalyzer.setDisplayName("Chuyên gia Phân tích Thị trường");
            marketAnalyzer.setDescription("AI agent for market analysis and price predictions");
            marketAnalyzer.setModelName("gpt-4");
            marketAnalyzer.setApiProvider("OPENAI");
            marketAnalyzer.setPromptTemplate("Bạn là chuyên gia phân tích thị trường bất động sản. Hãy phân tích xu hướng giá và đưa ra dự đoán về thị trường. Câu hỏi: {question}");
            marketAnalyzer.setSystemPrompt("Bạn là chuyên gia phân tích thị trường bất động sản với khả năng dự đoán xu hướng giá chính xác.");
            marketAnalyzer.setTemperature(new BigDecimal("0.2"));
            marketAnalyzer.setMaxTokens(2000);
            marketAnalyzer.setPriority(2);
            marketAnalyzer.setIsActive(true);
            marketAnalyzer.setResponseFormat("TEXT");
            marketAnalyzer.setCreatedAt(LocalDateTime.now());
            marketAnalyzer.setUpdatedAt(LocalDateTime.now());
            aiAgentRepository.save(marketAnalyzer);

            log.info("✅ Created {} AI agents", aiAgentRepository.count());
        } else {
            log.info("🤖 AI agents already exist, skipping initialization");
        }
    }
}
