package com.realestate.membership.service;

import com.realestate.membership.entity.UserMembership;
import com.realestate.membership.entity.UserMonthlyUsage;
import com.realestate.membership.exception.InsufficientMembershipException;
import com.realestate.membership.repository.UserMonthlyUsageRepository;
import com.realestate.membership.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.YearMonth;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Transactional
@Slf4j
public class AIContentService {
    
    private final OpenAIService openAIService;
    private final MembershipService membershipService;
    private final UserMonthlyUsageRepository userMonthlyUsageRepository;
    private final UserRepository userRepository;
    
    public Map<String, Object> generatePropertyContent(Long userId, String description, String imageDescription) {
        // Check user membership
        Optional<UserMembership> activeMembership = membershipService.getUserActiveMembership(userId);
        if (activeMembership.isEmpty()) {
            throw new InsufficientMembershipException("No active membership found");
        }
        
        UserMembership membership = activeMembership.get();
        if (!membership.getMembership().getAiContentGeneration()) {
            throw new InsufficientMembershipException("Your membership plan doesn't include AI content generation");
        }
        
        // Track usage (optional limit for future)
        String currentMonth = YearMonth.now().toString();
        UserMonthlyUsage monthlyUsage = getOrCreateMonthlyUsage(userId, currentMonth);
        
        try {
            // Generate SEO-optimized content
            String seoContent = generateSEOContent(description, imageDescription);
            
            // Generate title suggestions
            String titleSuggestions = generateTitleSuggestions(description);
            
            // Generate meta description
            String metaDescription = generateMetaDescription(description);
            
            // Update usage tracking
            monthlyUsage.incrementAiContentUsage();
            userMonthlyUsageRepository.save(monthlyUsage);
            
            log.info("AI content generated for user {}", userId);
            
            return Map.of(
                "success", true,
                "content", Map.of(
                    "seoOptimizedDescription", seoContent,
                    "titleSuggestions", titleSuggestions.split("\n"),
                    "metaDescription", metaDescription,
                    "seoScore", calculateSEOScore(seoContent),
                    "keywords", extractKeywords(description)
                ),
                "usage", Map.of(
                    "aiContentUsed", monthlyUsage.getAiContentUsed(),
                    "month", currentMonth
                )
            );
            
        } catch (Exception e) {
            log.error("Error generating AI content for user {}", userId, e);
            throw new RuntimeException("Failed to generate AI content: " + e.getMessage());
        }
    }
    
    private String generateSEOContent(String description, String imageDescription) {
        String prompt = String.format("""
            Tạo nội dung SEO tối ưu cho bài đăng bất động sản bằng tiếng Việt.
            
            Mô tả gốc: %s
            Mô tả hình ảnh: %s
            
            Yêu cầu:
            1. Viết lại mô tả chi tiết, hấp dẫn (300-500 từ)
            2. Sử dụng từ khóa bất động sản phổ biến
            3. Cấu trúc rõ ràng với đoạn văn ngắn
            4. Tạo cảm giác tin cậy và chuyên nghiệp
            5. Bao gồm thông tin về vị trí, tiện ích, ưu điểm
            
            Chỉ trả về nội dung đã được tối ưu SEO:
            """, description, imageDescription != null ? imageDescription : "Không có mô tả hình ảnh");
        
        return openAIService.generateResponse(prompt, 
            "Bạn là chuyên gia viết content bất động sản SEO. Tạo nội dung hấp dẫn, chuyên nghiệp.");
    }
    
    private String generateTitleSuggestions(String description) {
        String prompt = String.format("""
            Tạo 5 tiêu đề hấp dẫn cho bài đăng bất động sản dựa trên mô tả:
            %s
            
            Yêu cầu tiêu đề:
            - Ngắn gọn (50-60 ký tự)
            - Hấp dẫn, thu hút click
            - Chứa từ khóa quan trọng
            - Tạo cảm giác cấp bách hoặc độc quyền
            
            Trả về 5 tiêu đề, mỗi tiêu đề một dòng:
            """, description);
        
        return openAIService.generateResponse(prompt, 
            "Bạn là chuyên gia marketing bất động sản. Tạo tiêu đề thu hút và chuyên nghiệp.");
    }
    
    private String generateMetaDescription(String description) {
        String prompt = String.format("""
            Tạo meta description SEO cho bài đăng bất động sản:
            %s
            
            Yêu cầu:
            - Độ dài 150-160 ký tự
            - Tóm tắt hấp dẫn nhất
            - Chứa từ khóa chính
            - Có call-to-action
            
            Chỉ trả về meta description:
            """, description);
        
        return openAIService.generateResponse(prompt, 
            "Bạn là chuyên gia SEO bất động sản. Tạo meta description tối ưu.");
    }
    
    private int calculateSEOScore(String content) {
        int score = 0;
        
        // Check content length
        if (content.length() >= 300 && content.length() <= 1000) score += 20;
        
        // Check for real estate keywords
        String[] keywords = {"bất động sản", "nhà", "căn hộ", "đất", "mua", "bán", "thuê", "vị trí", "tiện ích"};
        for (String keyword : keywords) {
            if (content.toLowerCase().contains(keyword)) score += 5;
        }
        
        // Check for location keywords
        String[] locations = {"quận", "huyện", "phường", "đường", "gần", "trung tâm"};
        for (String location : locations) {
            if (content.toLowerCase().contains(location)) score += 5;
        }
        
        // Check paragraph structure
        if (content.split("\n").length >= 3) score += 10;
        
        // Check for numbers (price, area, etc.)
        if (content.matches(".*\\d+.*")) score += 10;
        
        return Math.min(score, 100);
    }
    
    private String[] extractKeywords(String description) {
        // Simple keyword extraction - can be enhanced with NLP
        String[] commonKeywords = {
            "căn hộ", "nhà phố", "biệt thự", "đất nền", "chung cư", 
            "mặt tiền", "hẻm", "gần chợ", "gần trường", "view đẹp"
        };
        
        return java.util.Arrays.stream(commonKeywords)
                .filter(keyword -> description.toLowerCase().contains(keyword))
                .toArray(String[]::new);
    }
    
    private UserMonthlyUsage getOrCreateMonthlyUsage(Long userId, String yearMonth) {
        return userMonthlyUsageRepository.findByUserIdAndYearMonth(userId, yearMonth)
                .orElseGet(() -> {
                    var user = userRepository.findById(userId)
                            .orElseThrow(() -> new RuntimeException("User not found"));
                    
                    UserMonthlyUsage usage = new UserMonthlyUsage();
                    usage.setUser(user);
                    usage.setYearMonth(yearMonth);
                    usage.setPushTopUsed(0);
                    usage.setAiContentUsed(0);
                    
                    return userMonthlyUsageRepository.save(usage);
                });
    }
}
