package com.realestate.membership.service;

import com.realestate.membership.dto.PropertyImageRequest;
import com.realestate.membership.dto.PropertyImageResponse;
import com.realestate.membership.entity.Property;
import com.realestate.membership.entity.PropertyImage;
import com.realestate.membership.exception.ResourceNotFoundException;
import com.realestate.membership.repository.PropertyImageRepository;
import com.realestate.membership.repository.PropertyRepository;
import com.realestate.membership.util.ImageValidationUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
public class PropertyImageService {

    private final PropertyImageRepository propertyImageRepository;
    private final PropertyRepository propertyRepository;
    private final S3Service s3Service;

    @Value("${app.upload.max-images-per-property:10}")
    private int maxImagesPerProperty;

    public PropertyImageResponse uploadImage(Long propertyId, MultipartFile file, PropertyImageRequest request) {
        // Validate property exists
        Property property = propertyRepository.findById(propertyId)
                .orElseThrow(() -> new ResourceNotFoundException("Property not found with id: " + propertyId));

        // Validate image file using our utility
        ImageValidationUtil.validateImageFile(file);
        
        // Check maximum images limit
        long currentImageCount = propertyImageRepository.countByPropertyId(propertyId);
        if (currentImageCount >= maxImagesPerProperty) {
            throw new IllegalArgumentException("Maximum number of images (" + maxImagesPerProperty + ") reached for this property");
        }

        try {
            // Upload file to S3
            String imageUrl = s3Service.uploadFile(file, "properties/" + propertyId);

            // Create and save image record
            PropertyImage image = new PropertyImage();
            image.setProperty(property);
            image.setImageUrl(imageUrl);
            image.setAltText(request.getAltText());
            image.setIsPrimary(request.getIsPrimary());
            image.setSortOrder(request.getSortOrder());

            PropertyImage savedImage = propertyImageRepository.save(image);

            // If this is primary image, update other images
            if (Boolean.TRUE.equals(request.getIsPrimary())) {
                propertyImageRepository.findByPropertyIdAndIsPrimaryTrue(propertyId)
                        .forEach(img -> {
                            if (!img.getId().equals(savedImage.getId())) {
                                img.setIsPrimary(false);
                                propertyImageRepository.save(img);
                            }
                        });
            }

            return mapToResponse(savedImage);

        } catch (IOException e) {
            throw new RuntimeException("Failed to store file " + file.getOriginalFilename(), e);
        }
    }

    public List<PropertyImageResponse> getPropertyImages(Long propertyId) {
        return propertyImageRepository.findByPropertyIdOrderBySortOrderAsc(propertyId)
                .stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    public void deleteImage(Long propertyId, Long imageId) {
        PropertyImage image = propertyImageRepository.findById(imageId)
                .orElseThrow(() -> new ResourceNotFoundException("Image not found with id: " + imageId));

        if (!image.getProperty().getId().equals(propertyId)) {
            throw new IllegalArgumentException("Image does not belong to the specified property");
        }

        try {
            // Delete file from S3
            s3Service.deleteFile(image.getImageUrl());

            // Delete record
            propertyImageRepository.delete(image);
        } catch (Exception e) {
            throw new RuntimeException("Failed to delete file", e);
        }
    }

    private PropertyImageResponse mapToResponse(PropertyImage image) {
        return new PropertyImageResponse(
                image.getId(),
                image.getImageUrl(),
                image.getAltText(),
                image.getIsPrimary(),
                image.getSortOrder()
        );
    }
} 