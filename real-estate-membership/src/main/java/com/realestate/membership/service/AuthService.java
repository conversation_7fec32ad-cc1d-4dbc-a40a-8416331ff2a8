package com.realestate.membership.service;

import com.realestate.membership.dto.*;
import com.realestate.membership.entity.Membership;
import com.realestate.membership.entity.User;
import com.realestate.membership.entity.UserMembership;
import com.realestate.membership.exception.EmailAlreadyExistsException;
import com.realestate.membership.exception.ResourceNotFoundException;
import com.realestate.membership.exception.UsernameAlreadyExistsException;
import com.realestate.membership.repository.MembershipRepository;
import com.realestate.membership.repository.UserMembershipRepository;
import com.realestate.membership.repository.UserRepository;
import com.realestate.membership.security.JwtTokenProvider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Transactional
@Slf4j
public class AuthService {
    
    private final UserRepository userRepository;
    private final MembershipRepository membershipRepository;
    private final UserMembershipRepository userMembershipRepository;
    private final PasswordEncoder passwordEncoder;
    private final AuthenticationManager authenticationManager;
    private final JwtTokenProvider tokenProvider;
    private final EmailService emailService;
    private final NotificationService notificationService;
    
    @Value("${email.verification.enabled:true}")
    private boolean emailVerificationEnabled;
    
    private static final SecureRandom secureRandom = new SecureRandom();
    private static final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    
    public AuthResponse register(RegisterRequest request) {
        if (userRepository.existsByUsername(request.getUsername())) {
            throw new UsernameAlreadyExistsException("Username đã được sử dụng!");
        }
        
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new EmailAlreadyExistsException("Email đã được sử dụng!");
        }
        
        User user = new User();
        user.setUsername(request.getUsername());
        user.setEmail(request.getEmail());
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setFirstName(request.getFirstName());
        user.setLastName(request.getLastName());
        user.setPhoneNumber(request.getPhoneNumber());
        user.setRole(User.Role.USER);
        user.setStatus(User.UserStatus.ACTIVE);
        user.setEmailVerified(!emailVerificationEnabled); // Skip verification if disabled
        user.setOauthProvider(User.OAuthProvider.LOCAL);
        user.setCreatedAt(LocalDateTime.now());
        user.setUpdatedAt(LocalDateTime.now());
        
        // Generate email verification token only if verification is enabled
        if (emailVerificationEnabled) {
            String verificationToken = generateSecureToken();
            user.setEmailVerificationToken(verificationToken);
            user.setEmailVerificationExpiresAt(LocalDateTime.now().plusHours(24));
        } else {
            user.setEmailVerifiedAt(LocalDateTime.now());
        }
        
        User savedUser = userRepository.save(user);
        log.info("User registered successfully: {}", savedUser.getEmail());

        // Assign FREE membership to new user
        assignFreeMembershipToUser(savedUser);

        // Send verification email only if verification is enabled
        if (emailVerificationEnabled) {
            try {
                emailService.sendEmailVerification(savedUser, savedUser.getEmailVerificationToken());
            } catch (Exception e) {
                log.error("Failed to send verification email to: {}", savedUser.getEmail(), e);
            }
            
            return new AuthResponse(null, savedUser.getUsername(), 
                                   savedUser.getEmail(), savedUser.getRole().name(), 
                                   "Tài khoản đã được tạo. Vui lòng kiểm tra email để xác thực tài khoản.");
        } else {
            // Generate token immediately if email verification is disabled
            String token = tokenProvider.generateToken(savedUser);
            return new AuthResponse(token, savedUser.getUsername(), 
                                   savedUser.getEmail(), savedUser.getRole().name(), 
                                   "Tài khoản đã được tạo và đăng nhập thành công!");
        }
    }
    
    public AuthResponse login(LoginRequest request) {
        // Validate request
        if (!request.isValid()) {
            throw new BadCredentialsException("Username/Email và mật khẩu không được để trống");
        }
        
        String loginIdentifier = request.getUsernameOrEmail();
        
        // Find user by username or email
        Optional<User> userOpt = userRepository.findByUsernameOrEmail(
                loginIdentifier, loginIdentifier);
        
        if (userOpt.isEmpty()) {
            throw new BadCredentialsException("Thông tin đăng nhập không chính xác");
        }
        
        User user = userOpt.get();
        
        // Check if email is verified only if verification is enabled
        if (emailVerificationEnabled && !user.getEmailVerified()) {
            throw new BadCredentialsException("Email chưa được xác thực. Vui lòng kiểm tra email và xác thực tài khoản.");
        }
        
        Authentication authentication = authenticationManager.authenticate(
            new UsernamePasswordAuthenticationToken(
                loginIdentifier,
                request.getPassword()
            )
        );
        
        // Update last login
        user.updateLastLogin();
        userRepository.save(user);
        
        String token = tokenProvider.generateToken(user);
        log.info("User logged in successfully: {}", user.getEmail());
        
        return new AuthResponse(token, user.getUsername(), 
                               user.getEmail(), user.getRole().name(), 
                               "Đăng nhập thành công!");
    }

    // =====================================================
    // EMAIL VERIFICATION
    // =====================================================

    public Map<String, Object> verifyEmail(String token, String email) {
        Optional<User> userOpt = userRepository.findByEmail(email);
        
        if (userOpt.isEmpty()) {
            throw new ResourceNotFoundException("Không tìm thấy tài khoản với email này");
        }
        
        User user = userOpt.get();
        
        if (user.getEmailVerified()) {
            return Map.of(
                "success", true,
                "message", "Email đã được xác thực trước đó",
                "redirectUrl", "/login"
            );
        }
        
        if (user.getEmailVerificationToken() == null || 
            !user.getEmailVerificationToken().equals(token)) {
            throw new IllegalArgumentException("Token xác thực không hợp lệ");
        }
        
        if (user.getEmailVerificationExpiresAt().isBefore(LocalDateTime.now())) {
            throw new IllegalArgumentException("Token xác thực đã hết hạn");
        }
        
        // Verify email
        user.setEmailVerified(true);
        user.setEmailVerifiedAt(LocalDateTime.now());
        user.setEmailVerificationToken(null);
        user.setEmailVerificationExpiresAt(null);
        userRepository.save(user);
        
        log.info("Email verified successfully for user: {}", user.getEmail());
        
        // Send welcome email and notification
        try {
            emailService.sendWelcomeEmail(user);
            notificationService.notifyWelcome(user);
        } catch (Exception e) {
            log.error("Failed to send welcome email/notification to: {}", user.getEmail(), e);
        }
        
        return Map.of(
            "success", true,
            "message", "Email đã được xác thực thành công! Bạn có thể đăng nhập ngay bây giờ.",
            "redirectUrl", "/login"
        );
    }

    public Map<String, Object> resendVerificationEmail(EmailVerificationRequest request) {
        Optional<User> userOpt = userRepository.findByEmail(request.getEmail());
        
        if (userOpt.isEmpty()) {
            throw new ResourceNotFoundException("Không tìm thấy tài khoản với email này");
        }
        
        User user = userOpt.get();
        
        if (user.getEmailVerified()) {
            return Map.of(
                "success", false,
                "message", "Email đã được xác thực trước đó"
            );
        }
        
        // Generate new verification token
        String verificationToken = generateSecureToken();
        user.setEmailVerificationToken(verificationToken);
        user.setEmailVerificationExpiresAt(LocalDateTime.now().plusHours(24));
        userRepository.save(user);
        
        // Send verification email
        try {
            emailService.sendEmailVerification(user, verificationToken);
        } catch (Exception e) {
            log.error("Failed to resend verification email to: {}", user.getEmail(), e);
            throw new RuntimeException("Không thể gửi email xác thực. Vui lòng thử lại sau.");
        }
        
        return Map.of(
            "success", true,
            "message", "Email xác thực đã được gửi lại. Vui lòng kiểm tra hộp thư của bạn."
        );
    }

    // =====================================================
    // PASSWORD RESET
    // =====================================================

    public Map<String, Object> forgotPassword(ForgotPasswordRequest request) {
        Optional<User> userOpt = userRepository.findByEmail(request.getEmail());
        
        if (userOpt.isEmpty()) {
            // Don't reveal that email doesn't exist for security
            return Map.of(
                "success", true,
                "message", "Nếu email tồn tại trong hệ thống, bạn sẽ nhận được email đặt lại mật khẩu."
            );
        }
        
        User user = userOpt.get();
        
        // Generate password reset token
        String resetToken = generateSecureToken();
        user.setPasswordResetToken(resetToken);
        user.setPasswordResetExpiresAt(LocalDateTime.now().plusHours(1)); // 1 hour expiry
        userRepository.save(user);
        
        // Send password reset email
        try {
            emailService.sendPasswordReset(user, resetToken);
            log.info("Password reset email sent to: {}", user.getEmail());
        } catch (Exception e) {
            log.error("Failed to send password reset email to: {}", user.getEmail(), e);
        }
        
        return Map.of(
            "success", true,
            "message", "Nếu email tồn tại trong hệ thống, bạn sẽ nhận được email đặt lại mật khẩu."
        );
    }

    public Map<String, Object> resetPassword(PasswordResetRequest request) {
        if (!request.getNewPassword().equals(request.getConfirmPassword())) {
            throw new IllegalArgumentException("Mật khẩu xác nhận không khớp");
        }
        
        Optional<User> userOpt = userRepository.findByEmail(request.getEmail());
        
        if (userOpt.isEmpty()) {
            throw new ResourceNotFoundException("Không tìm thấy tài khoản với email này");
        }
        
        User user = userOpt.get();
        
        if (user.getPasswordResetToken() == null || 
            !user.getPasswordResetToken().equals(request.getToken())) {
            throw new IllegalArgumentException("Token đặt lại mật khẩu không hợp lệ");
        }
        
        if (user.getPasswordResetExpiresAt().isBefore(LocalDateTime.now())) {
            throw new IllegalArgumentException("Token đặt lại mật khẩu đã hết hạn");
        }
        
        // Reset password
        user.setPassword(passwordEncoder.encode(request.getNewPassword()));
        user.setPasswordResetToken(null);
        user.setPasswordResetExpiresAt(null);
        user.setUpdatedAt(LocalDateTime.now());
        userRepository.save(user);
        
        log.info("Password reset successfully for user: {}", user.getEmail());
        
        // Send confirmation email
        try {
            emailService.sendPasswordChangedNotification(user);
        } catch (Exception e) {
            log.error("Failed to send password changed notification to: {}", user.getEmail(), e);
        }
        
        return Map.of(
            "success", true,
            "message", "Mật khẩu đã được đặt lại thành công. Bạn có thể đăng nhập với mật khẩu mới."
        );
    }

    // =====================================================
    // UTILITY METHODS
    // =====================================================

    public boolean isEmailExists(String email) {
        return userRepository.existsByEmail(email);
    }

    public boolean isUsernameExists(String username) {
        return userRepository.existsByUsername(username);
    }

    private String generateSecureToken() {
        StringBuilder token = new StringBuilder(32);
        for (int i = 0; i < 32; i++) {
            token.append(CHARACTERS.charAt(secureRandom.nextInt(CHARACTERS.length())));
        }
        return token.toString();
    }

    /**
     * Assign FREE membership to new user
     */
    private void assignFreeMembershipToUser(User user) {
        try {
            // Find FREE membership (ID = 1)
            Membership freeMembership = membershipRepository.findById(1L)
                .orElseThrow(() -> new RuntimeException("FREE membership not found"));

            // Create UserMembership
            UserMembership userMembership = new UserMembership();
            userMembership.setUser(user);
            userMembership.setMembership(freeMembership);
            userMembership.setStartDate(LocalDateTime.now());
            userMembership.setEndDate(LocalDateTime.now().plusMonths(freeMembership.getDurationMonths()));
            userMembership.setStatus(UserMembership.MembershipStatus.ACTIVE);
            userMembership.setCreatedAt(LocalDateTime.now());
            userMembership.setUpdatedAt(LocalDateTime.now());

            userMembershipRepository.save(userMembership);
            log.info("✅ Assigned FREE membership to user: {}", user.getEmail());

        } catch (Exception e) {
            log.error("❌ Failed to assign FREE membership to user: {}", user.getEmail(), e);
        }
    }
}
