package com.realestate.membership.service;

import com.realestate.membership.entity.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.springframework.scheduling.annotation.Async;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import java.util.concurrent.CompletableFuture;

@Service
@RequiredArgsConstructor
@Slf4j
public class EmailService {

    private final JavaMailSender mailSender;
    
    @Value("${spring.mail.username}")
    private String fromEmail;
    
    @Value("${app.frontend.url:http://localhost:3000}")
    private String frontendUrl;

    @Async
    public CompletableFuture<Void> sendEmailVerification(User user, String token) {
        try {
            String verificationUrl = frontendUrl + "/verify-email?token=" + token + "&email=" + user.getEmail();
            
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
            
            helper.setFrom(fromEmail);
            helper.setTo(user.getEmail());
            helper.setSubject("🔐 Xác thực tài khoản - Real Estate System");
            
            String htmlContent = buildEmailVerificationTemplate(user, verificationUrl);
            helper.setText(htmlContent, true);
            
            mailSender.send(message);
            log.info("Email verification sent to: {}", user.getEmail());
            
        } catch (MessagingException e) {
            log.error("Failed to send email verification to: {}", user.getEmail(), e);
            throw new RuntimeException("Failed to send email verification", e);
        }
        
        return CompletableFuture.completedFuture(null);
    }

    @Async
    public CompletableFuture<Void> sendPasswordReset(User user, String token) {
        try {
            String resetUrl = frontendUrl + "/reset-password?token=" + token + "&email=" + user.getEmail();
            
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
            
            helper.setFrom(fromEmail);
            helper.setTo(user.getEmail());
            helper.setSubject("🔑 Đặt lại mật khẩu - Real Estate System");
            
            String htmlContent = buildPasswordResetTemplate(user, resetUrl);
            helper.setText(htmlContent, true);
            
            mailSender.send(message);
            log.info("Password reset email sent to: {}", user.getEmail());
            
        } catch (MessagingException e) {
            log.error("Failed to send password reset email to: {}", user.getEmail(), e);
            throw new RuntimeException("Failed to send password reset email", e);
        }
        
        return CompletableFuture.completedFuture(null);
    }

    @Async
    public CompletableFuture<Void> sendWelcomeEmail(User user) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
            
            helper.setFrom(fromEmail);
            helper.setTo(user.getEmail());
            helper.setSubject("🎉 Chào mừng đến với Real Estate System!");
            
            String htmlContent = buildWelcomeTemplate(user);
            helper.setText(htmlContent, true);
            
            mailSender.send(message);
            log.info("Welcome email sent to: {}", user.getEmail());
            
        } catch (MessagingException e) {
            log.error("Failed to send welcome email to: {}", user.getEmail(), e);
        }
        
        return CompletableFuture.completedFuture(null);
    }

    @Async
    public CompletableFuture<Void> sendPasswordChangedNotification(User user) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(fromEmail);
            message.setTo(user.getEmail());
            message.setSubject("🔐 Mật khẩu đã được thay đổi - Real Estate System");
            message.setText(
                String.format(
                    "Xin chào %s,\n\n" +
                    "Mật khẩu của bạn đã được thay đổi thành công.\n\n" +
                    "Nếu bạn không thực hiện thay đổi này, vui lòng liên hệ với chúng tôi ngay lập tức.\n\n" +
                    "Trân trọng,\n" +
                    "Real Estate Team",
                    user.getFirstName() != null ? user.getFirstName() : user.getUsername()
                )
            );
            
            mailSender.send(message);
            log.info("Password changed notification sent to: {}", user.getEmail());
            
        } catch (Exception e) {
            log.error("Failed to send password changed notification to: {}", user.getEmail(), e);
        }
        
        return CompletableFuture.completedFuture(null);
    }

    // =====================================================
    // EMAIL TEMPLATES
    // =====================================================

    private String buildEmailVerificationTemplate(User user, String verificationUrl) {
        return """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
                    .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
                    .button { display: inline-block; background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
                    .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>🏠 Real Estate System</h1>
                        <h2>Xác thực tài khoản của bạn</h2>
                    </div>
                    <div class="content">
                        <p>Xin chào <strong>%s</strong>,</p>
                        <p>Cảm ơn bạn đã đăng ký tài khoản tại Real Estate System!</p>
                        <p>Để hoàn tất quá trình đăng ký, vui lòng nhấn vào nút bên dưới để xác thực email của bạn:</p>
                        <div style="text-align: center;">
                            <a href="%s" class="button">🔐 Xác thực Email</a>
                        </div>
                        <p><small>Hoặc copy link sau vào trình duyệt:<br>%s</small></p>
                        <p><strong>Lưu ý:</strong> Link xác thực này sẽ hết hạn sau 24 giờ.</p>
                        <p>Nếu bạn không đăng ký tài khoản này, vui lòng bỏ qua email này.</p>
                    </div>
                    <div class="footer">
                        <p>© 2024 Real Estate System. All rights reserved.</p>
                        <p>📧 Liên hệ: <EMAIL></p>
                    </div>
                </div>
            </body>
            </html>
            """.formatted(
                user.getFirstName() != null ? user.getFirstName() : user.getUsername(),
                verificationUrl,
                verificationUrl
            );
    }

    private String buildPasswordResetTemplate(User user, String resetUrl) {
        return """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
                    .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
                    .button { display: inline-block; background: #f5576c; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
                    .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
                    .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>🏠 Real Estate System</h1>
                        <h2>Đặt lại mật khẩu</h2>
                    </div>
                    <div class="content">
                        <p>Xin chào <strong>%s</strong>,</p>
                        <p>Chúng tôi nhận được yêu cầu đặt lại mật khẩu cho tài khoản của bạn.</p>
                        <div class="warning">
                            <p><strong>⚠️ Lưu ý bảo mật:</strong> Nếu bạn không yêu cầu đặt lại mật khẩu, vui lòng bỏ qua email này và liên hệ với chúng tôi ngay.</p>
                        </div>
                        <p>Để đặt lại mật khẩu, vui lòng nhấn vào nút bên dưới:</p>
                        <div style="text-align: center;">
                            <a href="%s" class="button">🔑 Đặt lại mật khẩu</a>
                        </div>
                        <p><small>Hoặc copy link sau vào trình duyệt:<br>%s</small></p>
                        <p><strong>Quan trọng:</strong></p>
                        <ul>
                            <li>Link này sẽ hết hạn sau 1 giờ</li>
                            <li>Chỉ sử dụng được 1 lần duy nhất</li>
                            <li>Không chia sẻ link này với ai khác</li>
                        </ul>
                    </div>
                    <div class="footer">
                        <p>© 2024 Real Estate System. All rights reserved.</p>
                        <p>📧 Liên hệ: <EMAIL></p>
                    </div>
                </div>
            </body>
            </html>
            """.formatted(
                user.getFirstName() != null ? user.getFirstName() : user.getUsername(),
                resetUrl,
                resetUrl
            );
    }

    private String buildWelcomeTemplate(User user) {
        return """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
                    .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
                    .feature { background: white; padding: 20px; margin: 15px 0; border-radius: 5px; border-left: 4px solid #4facfe; }
                    .button { display: inline-block; background: #4facfe; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
                    .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>🎉 Chào mừng đến với Real Estate System!</h1>
                    </div>
                    <div class="content">
                        <p>Xin chào <strong>%s</strong>,</p>
                        <p>Chúc mừng! Tài khoản của bạn đã được tạo thành công và đã được xác thực.</p>
                        
                        <h3>🌟 Những tính năng bạn có thể sử dụng:</h3>
                        
                        <div class="feature">
                            <h4>🏘️ Quản lý Bất động sản</h4>
                            <p>Đăng tin, tìm kiếm và quản lý các bất động sản một cách dễ dàng</p>
                        </div>
                        
                        <div class="feature">
                            <h4>🤖 AI Chatbot</h4>
                            <p>Tìm kiếm bất động sản thông minh với sự hỗ trợ của AI</p>
                        </div>
                        
                        <div class="feature">
                            <h4>💳 Gói thành viên</h4>
                            <p>Nâng cấp tài khoản để có thêm nhiều tính năng premium</p>
                        </div>
                        
                        <div class="feature">
                            <h4>🔔 Thông báo real-time</h4>
                            <p>Nhận thông báo ngay khi có cập nhật quan trọng</p>
                        </div>
                        
                        <div style="text-align: center;">
                            <a href="%s/dashboard" class="button">🚀 Bắt đầu sử dụng</a>
                        </div>
                        
                        <p><strong>Thông tin tài khoản:</strong></p>
                        <ul>
                            <li>Username: <strong>%s</strong></li>
                            <li>Email: <strong>%s</strong></li>
                            <li>Ngày đăng ký: <strong>%s</strong></li>
                        </ul>
                        
                        <p>Nếu bạn cần hỗ trợ, đừng ngần ngại liên hệ với chúng tôi!</p>
                    </div>
                    <div class="footer">
                        <p>© 2024 Real Estate System. All rights reserved.</p>
                        <p>📧 Hỗ trợ: <EMAIL> | 📞 Hotline: 1900-1234</p>
                    </div>
                </div>
            </body>
            </html>
            """.formatted(
                user.getFirstName() != null ? user.getFirstName() : user.getUsername(),
                frontendUrl,
                user.getUsername(),
                user.getEmail(),
                user.getCreatedAt() != null ? user.getCreatedAt().toString() : "Hôm nay"
            );
    }
} 