-- =====================================================
-- REAL ESTATE MEMBERSHIP SYSTEM - SAMPLE DATA
-- Initial data for testing and development
-- =====================================================

-- ===== SAMPLE MEMBERSHIPS =====
INSERT INTO memberships (name, description, price, duration_months, max_properties, featured_properties, priority_support, analytics_access, multiple_images, contact_info_display, type, is_active)
SELECT 'FREE', 'Basic free plan for new users', 0.00, 1, 1, 0, FALSE, FALSE, FALSE, TRUE, 'FREE', TRUE
WHERE NOT EXISTS (SELECT 1 FROM memberships WHERE name = 'FREE');

INSERT INTO memberships (name, description, price, duration_months, max_properties, featured_properties, priority_support, analytics_access, multiple_images, contact_info_display, type, is_active)
SELECT 'BASIC', 'Basic plan for individual users', 99000.00, 1, 5, 0, FALSE, FALSE, TRUE, TRUE, 'BASIC', TRUE
WHERE NOT EXISTS (SELECT 1 FROM memberships WHERE name = 'BASIC');

INSERT INTO memberships (name, description, price, duration_months, max_properties, featured_properties, priority_support, analytics_access, multiple_images, contact_info_display, type, is_active)
SELECT 'PREMIUM', 'Premium plan for professional agents', 299000.00, 1, 20, 5, TRUE, TRUE, TRUE, TRUE, 'PREMIUM', TRUE
WHERE NOT EXISTS (SELECT 1 FROM memberships WHERE name = 'PREMIUM');

INSERT INTO memberships (name, description, price, duration_months, max_properties, featured_properties, priority_support, analytics_access, multiple_images, contact_info_display, type, is_active)
SELECT 'VIP', 'VIP plan for real estate companies', 599000.00, 1, 100, 20, TRUE, TRUE, TRUE, TRUE, 'VIP', TRUE
WHERE NOT EXISTS (SELECT 1 FROM memberships WHERE name = 'VIP');

-- ===== SAMPLE CATEGORIES =====
INSERT INTO categories (name, description, icon_url, is_active, sort_order)
SELECT 'Apartment', 'Căn hộ chung cư', '/icons/apartment.svg', TRUE, 1
WHERE NOT EXISTS (SELECT 1 FROM categories WHERE name = 'Apartment');

INSERT INTO categories (name, description, icon_url, is_active, sort_order)
SELECT 'House', 'Nhà riêng', '/icons/house.svg', TRUE, 2
WHERE NOT EXISTS (SELECT 1 FROM categories WHERE name = 'House');

INSERT INTO categories (name, description, icon_url, is_active, sort_order)
SELECT 'Villa', 'Biệt thự', '/icons/villa.svg', TRUE, 3
WHERE NOT EXISTS (SELECT 1 FROM categories WHERE name = 'Villa');

INSERT INTO categories (name, description, icon_url, is_active, sort_order)
SELECT 'Townhouse', 'Nhà phố', '/icons/townhouse.svg', TRUE, 4
WHERE NOT EXISTS (SELECT 1 FROM categories WHERE name = 'Townhouse');

INSERT INTO categories (name, description, icon_url, is_active, sort_order)
SELECT 'Office', 'Văn phòng', '/icons/office.svg', TRUE, 5
WHERE NOT EXISTS (SELECT 1 FROM categories WHERE name = 'Office');

INSERT INTO categories (name, description, icon_url, is_active, sort_order)
SELECT 'Shop', 'Cửa hàng', '/icons/shop.svg', TRUE, 6
WHERE NOT EXISTS (SELECT 1 FROM categories WHERE name = 'Shop');

INSERT INTO categories (name, description, icon_url, is_active, sort_order)
SELECT 'Land', 'Đất nền', '/icons/land.svg', TRUE, 7
WHERE NOT EXISTS (SELECT 1 FROM categories WHERE name = 'Land');

INSERT INTO categories (name, description, icon_url, is_active, sort_order)
SELECT 'Warehouse', 'Kho xưởng', '/icons/warehouse.svg', TRUE, 8
WHERE NOT EXISTS (SELECT 1 FROM categories WHERE name = 'Warehouse');

-- ===== SAMPLE ADMIN USER =====
INSERT INTO users (username, email, password, first_name, last_name, role, status, email_verified) VALUES
('admin', '<EMAIL>', '$2a$10$8K1p/a8jqfNYAQjPiC4X0OwBr94qhEKVvzfyqRqrNx6g5rBgp1rCy', 'Admin', 'User', 'ADMIN', 'ACTIVE', TRUE);

-- ===== ASSIGN FREE MEMBERSHIP TO ADMIN =====
INSERT INTO user_memberships (user_id, membership_id, start_date, end_date, is_active) VALUES
(1, 4, CURRENT_TIMESTAMP, DATEADD('DAY', 365, CURRENT_TIMESTAMP), TRUE);

-- ===== SAMPLE AI AGENTS =====
INSERT INTO ai_agents (name, description, prompt_template, model, max_tokens, temperature, is_active) VALUES
('Property Assistant', 'AI assistant for property search and recommendations', 'Bạn là chuyên gia tư vấn bất động sản thông minh tại Việt Nam. Hãy giúp người dùng tìm kiếm và tư vấn về bất động sản phù hợp với nhu cầu của họ.', 'gpt-4', 2000, 0.3, TRUE),
('Market Analyzer', 'AI agent for market analysis and price predictions', 'Bạn là chuyên gia phân tích thị trường bất động sản. Hãy phân tích xu hướng giá và đưa ra dự đoán về thị trường.', 'gpt-4', 2000, 0.2, TRUE);

-- ===== SAMPLE NOTIFICATIONS FOR ADMIN =====
INSERT INTO notifications (user_id, title, message, type, status, priority, send_email, email_sent) VALUES
(1, 'Chào mừng đến với hệ thống!', 'Cảm ơn bạn đã sử dụng hệ thống quản lý bất động sản của chúng tôi.', 'WELCOME', 'UNREAD', 'NORMAL', FALSE, FALSE),
(1, 'Hệ thống đã sẵn sàng', 'Tất cả các tính năng đã được cấu hình và sẵn sàng sử dụng.', 'INFO', 'UNREAD', 'HIGH', FALSE, FALSE); 
 