# Real Estate Membership Platform

## 📋 Project Overview

A comprehensive real estate platform with membership system, property management, payment integration, and AI-powered features.

## 🏗️ Architecture

- **Backend**: Java Spring Boot 3.4.1
- **Database**: PostgreSQL (AWS RDS)
- **Authentication**: JWT <PERSON>ken
- **Payment**: Stripe Integration
- **File Storage**: AWS S3
- **AI**: OpenAI GPT-3.5-turbo

## 🗄️ Database Configuration

### PostgreSQL Connection
```properties
# Database Configuration
spring.datasource.url=*************************************************************************************************
spring.datasource.username=realestate_user
spring.datasource.password=your_secure_password_here
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA/Hibernate Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.format_sql=true
```

### Database Schema Overview

#### Core Tables:
1. **users** - User accounts and authentication
2. **memberships** - Membership plans (FREE, BASIC, PREMIUM)
3. **user_memberships** - User subscription records
4. **properties** - Real estate listings
5. **categories** - Property categories
6. **property_images** - Property photos
7. **payments** - Payment transactions
8. **property_boosts** - Push-to-top feature records

## ⚙️ Application Properties

### Complete application.properties
```properties
# Server Configuration
server.port=8080
server.servlet.context-path=/api/v1

# Database Configuration
spring.datasource.url=*************************************************************************************************
spring.datasource.username=realestate_user
spring.datasource.password=your_secure_password_here
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA/Hibernate Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.format_sql=true

# JWT Configuration
app.jwt.secret=mySecretKey123456789012345678901234567890
app.jwt.expiration=86400000

# Admin Configuration
app.admin.username=admin
app.admin.email=<EMAIL>
app.admin.password=admin123

# AWS S3 Configuration
aws.s3.bucket-name=realestate-images-bucket
aws.s3.region=us-east-1
aws.access-key-id=YOUR_AWS_ACCESS_KEY
aws.secret-access-key=YOUR_AWS_SECRET_KEY

# Stripe Configuration
stripe.api.key=sk_test_your_stripe_secret_key_here
stripe.webhook.secret=whsec_your_webhook_secret_here

# OpenAI Configuration
openai.api.key=sk-your_openai_api_key_here
openai.model=gpt-3.5-turbo

# File Upload Configuration
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# Logging Configuration
logging.level.com.realestate.membership=DEBUG
logging.level.org.springframework.security=DEBUG
```

## 🚀 Quick Start

### Prerequisites
- Java 21+
- PostgreSQL database
- AWS account (S3, RDS)
- Stripe account
- OpenAI API key

### Installation & Run
```bash
# Install dependencies
mvn clean install -DskipTests

# Build project
mvn clean package -DskipTests

# Run application
mvn spring-boot:run

# Or using Maven Wrapper
./mvnw spring-boot:run
```

### Default Admin Account
- Username: `admin`
- Password: `admin123`
- Email: `<EMAIL>`

## 📊 Membership System

### Membership Tiers

| Feature | FREE | BASIC | PREMIUM |
|---------|------|-------|---------|
| Max Properties | 10 | 10 | 50 |
| AI Content Generation | ❌ | ❌ | ✅ |
| Push to Top | 0/month | 0/month | 10/month |
| Multiple Images | ❌ | ✅ | ✅ |
| Priority Support | ❌ | ❌ | ✅ |
| Analytics Access | ❌ | ❌ | ✅ |
| Price | Free | $99/month | $299/month |

### Auto-Assignment Logic
- New users automatically get **FREE** membership
- Duration: 30 days (1 month)
- Status: ACTIVE
- Properties can be created but require admin approval

## 🔐 Authentication & Authorization

### JWT Token Structure
```json
{
  "sub": "username",
  "authorities": "ROLE_USER,ROLE_ADMIN",
  "role": "USER",
  "userId": 123,
  "iat": **********,
  "exp": **********
}
```

### User Roles
- **USER**: Regular users with membership
- **ADMIN**: Full system access

## 💳 Payment Integration

### Stripe Webhook Events
- `checkout.session.completed` - Successful payment
- `invoice.payment_succeeded` - Subscription renewal
- `customer.subscription.deleted` - Subscription cancelled

### Payment Flow
1. User selects membership plan
2. Stripe Checkout session created
3. User completes payment
4. Webhook processes payment
5. UserMembership record created/updated

## 🏠 Property Management

### Property Status Flow
```
PENDING → APPROVED → PUBLISHED
    ↓
REJECTED
```

### Property Features
- **Status Management**: PENDING, APPROVED, REJECTED
- **Boost System**: Push-to-top feature (premium only)
- **Image Upload**: Multiple images via S3
- **AI Content**: Auto-generated SEO content (premium only)

## 🤖 AI Features

### OpenAI Integration
- **Content Generation**: Property descriptions and SEO content
- **Chatbot**: Property search assistance
- **Function Calling**: Structured property queries

### AI Endpoints
- `POST /chatbot/chat` - Chat with AI assistant
- `POST /properties/{id}/generate-content` - Generate AI content

## 📱 API Endpoints

### Authentication
- `POST /auth/register` - User registration
- `POST /auth/login` - User login
- `POST /auth/logout` - User logout

### Properties
- `GET /properties` - List approved properties
- `POST /properties` - Create property (authenticated)
- `GET /properties/{id}` - Get property details
- `PUT /properties/{id}` - Update property (owner only)

### Admin
- `GET /admin/properties/pending` - Pending approvals
- `POST /admin/properties/{id}/approve` - Approve property
- `POST /admin/properties/{id}/reject` - Reject property
- `GET /admin/stats/dashboard` - Admin dashboard

### Payments
- `POST /payments/create-checkout-session` - Create Stripe session
- `POST /payments/webhook` - Stripe webhook handler

## 🔧 Development Notes

### Current Status
- ✅ JWT token includes role/authorities
- ✅ FREE membership auto-assignment
- ✅ Property creation by FREE users
- ✅ Stripe payment integration
- ✅ Admin dashboard statistics
- ⚠️ Admin property approval (needs debugging)

### Known Limitations
- Admin endpoints may need additional debugging
- File upload size limited to 10MB
- AI content generation requires premium membership

## 🌐 Deployment

### Docker Support
```bash
# Build and run
make init
make stop
make clean
```

### Environment Variables
```bash
export DATABASE_URL="********************************/db"
export JWT_SECRET="your-jwt-secret"
export STRIPE_SECRET_KEY="sk_test_..."
export OPENAI_API_KEY="sk-..."
export AWS_ACCESS_KEY_ID="..."
export AWS_SECRET_ACCESS_KEY="..."
```

## 📈 Business Logic

### User Registration Flow
1. User submits registration form
2. Account created with USER role
3. FREE membership automatically assigned
4. JWT token generated and returned
5. User can immediately create properties (PENDING status)

### Property Approval Process
1. User creates property → Status: PENDING
2. Admin reviews in dashboard
3. Admin approves/rejects with reason
4. Approved properties become publicly visible
5. Rejected properties remain private with reason

---

**Ready for Go Migration** 🚀

This Java Spring Boot application is fully functional and ready to be rewritten in Go. All core features are working including user registration, property management, payment processing, and admin approval system.
