# API Documentation

## Base URL
```
http://localhost:8080/api/v1
```

## Authentication
All protected endpoints require JWT token in Authorization header:
```
Authorization: Bearer <jwt_token>
```

## Response Format
All API responses follow this structure:
```json
{
  "success": true,
  "data": {},
  "message": "Success message",
  "timestamp": "2025-07-08T21:00:00Z"
}
```

## Error Response Format
```json
{
  "timestamp": [2025, 7, 8, 21, 0, 0, 0],
  "status": 400,
  "error": "Bad Request",
  "message": "Validation failed",
  "path": "/api/v1/endpoint"
}
```

## Authentication Endpoints

### POST /auth/register
Register a new user account.

**Request Body:**
```json
{
  "username": "johndoe",
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "<PERSON>",
  "lastName": "Doe",
  "phoneNumber": "**********"
}
```

**Response:**
```json
{
  "token": "eyJhbGciOiJIUzI1NiJ9...",
  "type": "Bearer",
  "username": "johndoe",
  "email": "<EMAIL>",
  "role": "USER",
  "message": "Tài khoản đã được tạo và đăng nhập thành công!"
}
```

### POST /auth/login
User login.

**Request Body:**
```json
{
  "username": "johndoe",
  "password": "password123"
}
```

**Response:**
```json
{
  "token": "eyJhbGciOiJIUzI1NiJ9...",
  "type": "Bearer",
  "username": "johndoe",
  "email": "<EMAIL>",
  "role": "USER",
  "message": "Đăng nhập thành công!"
}
```

## Property Endpoints

### GET /properties
Get all approved properties (public).

**Query Parameters:**
- `page` (int): Page number (default: 0)
- `size` (int): Page size (default: 10)
- `sortBy` (string): Sort field (default: "createdAt")
- `sortDir` (string): Sort direction (default: "desc")

**Response:**
```json
{
  "content": [
    {
      "id": 1,
      "title": "Beautiful Apartment",
      "description": "2BR apartment in city center",
      "price": 1500000000.00,
      "address": "123 Main St",
      "city": "Ho Chi Minh",
      "district": "District 1",
      "property_type": "APARTMENT",
      "listing_type": "SALE",
      "status": "APPROVED",
      "owner_name": "John Doe",
      "owner_phone": "**********",
      "category_name": "Apartment",
      "images": []
    }
  ],
  "pageable": {
    "page_number": 0,
    "page_size": 10
  },
  "total_elements": 1,
  "total_pages": 1
}
```

### POST /properties
Create a new property (authenticated).

**Request Body:**
```json
{
  "title": "Beautiful Apartment",
  "description": "2BR apartment in city center",
  "price": 1500000000,
  "propertyArea": 75.5,
  "bedrooms": 2,
  "bathrooms": 2,
  "address": "123 Main St",
  "city": "Ho Chi Minh",
  "district": "District 1",
  "ward": "Ward 1",
  "categoryId": 1,
  "propertyType": "APARTMENT",
  "listingType": "SALE"
}
```

**Response:**
```json
{
  "id": 1,
  "title": "Beautiful Apartment",
  "status": "PENDING",
  "created_at": [2025, 7, 8, 21, 0, 0, 0],
  "owner_name": "John Doe"
}
```

### GET /properties/{id}
Get property details by ID.

**Response:**
```json
{
  "id": 1,
  "title": "Beautiful Apartment",
  "description": "2BR apartment in city center",
  "price": 1500000000.00,
  "address": "123 Main St",
  "city": "Ho Chi Minh",
  "district": "District 1",
  "property_area": 75.5,
  "bedrooms": 2,
  "bathrooms": 2,
  "property_type": "APARTMENT",
  "listing_type": "SALE",
  "status": "APPROVED",
  "view_count": 5,
  "contact_count": 2,
  "owner_name": "John Doe",
  "owner_phone": "**********",
  "category_name": "Apartment",
  "images": []
}
```

## User Dashboard

### GET /dashboard
Get user dashboard data (authenticated).

**Response:**
```json
{
  "membership": {
    "planName": "FREE",
    "planType": "BASIC",
    "maxProperties": 10,
    "propertiesUsed": 2,
    "propertiesRemaining": 8,
    "hasAiGeneration": false,
    "pushTopLimit": 0,
    "daysRemaining": 25,
    "hasActiveMembership": true
  },
  "properties": {
    "totalProperties": 2,
    "approvedProperties": 1,
    "pendingProperties": 1,
    "rejectedProperties": 0,
    "totalViews": 15,
    "totalContacts": 3
  },
  "monthlyUsage": {
    "month": "2025-07",
    "pushTopUsed": 0,
    "pushTopLimit": 0,
    "aiContentUsed": 0
  }
}
```

## Payment Endpoints

### POST /payments/create-checkout-session
Create Stripe checkout session.

**Request Body:**
```json
{
  "membershipId": 2,
  "successUrl": "http://localhost:3000/success",
  "cancelUrl": "http://localhost:3000/cancel"
}
```

**Response:**
```json
{
  "sessionId": "cs_test_...",
  "url": "https://checkout.stripe.com/pay/cs_test_..."
}
```

### POST /payments/webhook
Stripe webhook handler (internal).

## Admin Endpoints

### GET /admin/stats/dashboard
Get admin dashboard statistics (admin only).

**Response:**
```json
{
  "overview": {
    "total_users": 10,
    "total_properties": 5,
    "pending_approvals": 3,
    "total_revenue": 1000.00
  },
  "users": {
    "total_users": 10,
    "active_users": 9,
    "new_users_today": 2
  },
  "properties": {
    "total_properties": 5,
    "approved_properties": 2,
    "pending_properties": 3,
    "rejected_properties": 0
  }
}
```

### GET /admin/properties/pending
Get pending properties for approval (admin only).

**Response:**
```json
{
  "content": [
    {
      "id": 1,
      "title": "Pending Property",
      "owner_name": "John Doe",
      "owner_email": "<EMAIL>",
      "status": "PENDING",
      "created_at": [2025, 7, 8, 21, 0, 0, 0]
    }
  ]
}
```

### POST /admin/properties/{id}/approve
Approve a property (admin only).

**Query Parameters:**
- `isFeatured` (boolean): Mark as featured (optional)
- `adminNote` (string): Admin note (optional)

**Response:**
```json
{
  "success": true,
  "message": "Property approved successfully",
  "propertyId": 1,
  "isFeatured": false
}
```

### POST /admin/properties/{id}/reject
Reject a property (admin only).

**Query Parameters:**
- `reason` (string): Rejection reason (required)

**Response:**
```json
{
  "success": true,
  "message": "Property rejected successfully",
  "propertyId": 1,
  "reason": "Incomplete information"
}
```

## AI Chatbot Endpoints

### POST /chatbot/chat
Chat with AI assistant (authenticated).

**Request Body:**
```json
{
  "message": "Find me a 2-bedroom apartment in District 1",
  "conversationId": "conv_123"
}
```

**Response:**
```json
{
  "response": "I found several 2-bedroom apartments in District 1...",
  "conversationId": "conv_123",
  "properties": [
    {
      "id": 1,
      "title": "2BR Apartment",
      "price": 1500000000,
      "district": "District 1"
    }
  ]
}
```

## File Upload Endpoints

### POST /properties/{id}/upload-images
Upload property images (authenticated).

**Request:** Multipart form data with image files.

**Response:**
```json
{
  "success": true,
  "message": "Images uploaded successfully",
  "images": [
    {
      "id": 1,
      "imageUrl": "https://s3.amazonaws.com/bucket/image1.jpg",
      "isPrimary": true
    }
  ]
}
```

## Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `500` - Internal Server Error

## Rate Limiting

- Authentication endpoints: 5 requests per minute
- Property creation: 10 requests per hour
- File upload: 20 requests per hour
- General API: 100 requests per minute

## Pagination

All list endpoints support pagination:
- `page`: Page number (0-based)
- `size`: Items per page (max 100)
- `sort`: Sort field and direction (e.g., "createdAt,desc")

## Error Codes

- `AUTH_001` - Invalid credentials
- `AUTH_002` - Token expired
- `PROP_001` - Property not found
- `PROP_002` - Membership limit exceeded
- `PAY_001` - Payment failed
- `ADMIN_001` - Admin access required
