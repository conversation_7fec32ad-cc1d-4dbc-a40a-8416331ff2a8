-- ===============================================
-- DATABASE CLEANUP SCRIPT
-- XÓA HẾT DATA VÀ BẢNG TRONG DATABASE
-- ===============================================

-- Disable foreign key checks temporarily
SET session_replication_role = replica;

-- Drop all tables in correct order (reverse dependency order)
DROP TABLE IF EXISTS admin_actions CASCADE;
DROP TABLE IF EXISTS property_boosts CASCADE;
DROP TABLE IF EXISTS property_images CASCADE;
DROP TABLE IF EXISTS payments CASCADE;
DROP TABLE IF EXISTS user_memberships CASCADE;
DROP TABLE IF EXISTS properties CASCADE;
DROP TABLE IF EXISTS categories CASCADE;
DROP TABLE IF EXISTS memberships CASCADE;
DROP TABLE IF EXISTS users CASCADE;
DROP TABLE IF EXISTS ai_agents CASCADE;

-- Drop any remaining sequences
DROP SEQUENCE IF EXISTS users_id_seq CASCADE;
DROP SEQUENCE IF EXISTS memberships_id_seq CASCADE;
DROP SEQUENCE IF EXISTS user_memberships_id_seq CASCADE;
DROP SEQUENCE IF EXISTS categories_id_seq CASCADE;
DROP SEQUENCE IF EXISTS properties_id_seq CASCADE;
DROP SEQUENCE IF EXISTS property_images_id_seq CASCADE;
DROP SEQUENCE IF EXISTS payments_id_seq CASCADE;
DROP SEQUENCE IF EXISTS property_boosts_id_seq CASCADE;
DROP SEQUENCE IF EXISTS admin_actions_id_seq CASCADE;
DROP SEQUENCE IF EXISTS ai_agents_id_seq CASCADE;

-- Drop any remaining indexes
DROP INDEX IF EXISTS idx_users_email;
DROP INDEX IF EXISTS idx_users_username;
DROP INDEX IF EXISTS idx_properties_user_id;
DROP INDEX IF EXISTS idx_properties_status;
DROP INDEX IF EXISTS idx_properties_city_district;
DROP INDEX IF EXISTS idx_properties_price;
DROP INDEX IF EXISTS idx_user_memberships_user_id;
DROP INDEX IF EXISTS idx_user_memberships_status;
DROP INDEX IF EXISTS idx_payments_user_id;
DROP INDEX IF EXISTS idx_payments_status;
DROP INDEX IF EXISTS idx_property_boosts_property_id;
DROP INDEX IF EXISTS idx_property_boosts_active;

-- Re-enable foreign key checks
SET session_replication_role = DEFAULT;

-- Verify all tables are dropped
SELECT tablename FROM pg_tables WHERE schemaname = 'public';

-- Show remaining sequences
SELECT sequence_name FROM information_schema.sequences WHERE sequence_schema = 'public';

-- Show remaining indexes
SELECT indexname FROM pg_indexes WHERE schemaname = 'public';

COMMIT;
