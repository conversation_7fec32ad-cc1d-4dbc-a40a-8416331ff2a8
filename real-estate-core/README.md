# Real Estate Backend - Go Implementation

A modern real estate platform backend built with Go, implementing the Strategy Design Pattern for scalable and maintainable code architecture.

## Features

- **User Management**: Registration, authentication, and role-based access control
- **Property Management**: CRUD operations for real estate listings
- **Membership System**: Tiered membership plans with usage limits
- **Payment Integration**: Stripe payment processing
- **Admin Panel**: Property approval, user management, and analytics
- **AI Chatbot**: Property recommendations and search assistance
- **File Upload**: Property image management with AWS S3
- **Search & Filtering**: Advanced property search capabilities

## Architecture

This project implements the **Strategy Design Pattern** to provide:

- **Flexible Authentication**: Multiple authentication strategies (JWT, OAuth)
- **Scalable Property Management**: Different strategies for user vs admin operations
- **Payment Processing**: Pluggable payment providers (Stripe, PayPal, etc.)
- **Search Strategies**: Various search and recommendation algorithms
- **Notification Systems**: Multiple notification channels (email, SMS, push)

## Tech Stack

- **Language**: Go 1.21+
- **Framework**: Gin (HTTP router)
- **Database**: PostgreSQL with GORM ORM
- **Authentication**: JWT tokens
- **Payment**: Stripe API
- **File Storage**: AWS S3
- **AI Integration**: OpenAI API
- **Containerization**: Docker

## Project Structure

```
.
├── cmd/                    # Application entrypoints
│   └── main.go
├── internal/               # Private application code
│   ├── config/            # Configuration management
│   ├── database/          # Database connection and migrations
│   ├── handlers/          # HTTP request handlers
│   ├── middleware/        # HTTP middleware
│   ├── models/            # Data models
│   ├── repositories/      # Data access layer
│   ├── services/          # Business logic layer
│   └── strategies/        # Strategy pattern implementations
├── pkg/                   # Public library code
│   ├── auth/             # Authentication utilities
│   ├── errors/           # Error handling
│   └── utils/            # Common utilities
├── migrations/           # Database migrations
├── docs/                # Documentation
├── Makefile             # Build and development commands
├── go.mod               # Go module definition
├── go.sum               # Go module checksums
└── README.md            # This file
```

## Getting Started

### Prerequisites

- Go 1.21 or higher
- PostgreSQL 12+
- Make (optional, for using Makefile commands)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd real-estate-go-backend
   ```

2. **Install dependencies**
   ```bash
   go mod download
   # or using Makefile
   make deps
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Create database**
   ```bash
   createdb real_estate
   # or using Makefile
   make db-create
   ```

5. **Run the application**
   ```bash
   go run cmd/main.go
   # or using Makefile
   make run
   ```

### Development

For development with hot reload:

```bash
# Install air for hot reload
go install github.com/cosmtrek/air@latest

# Run in development mode
make dev
```

## API Documentation

The API follows RESTful conventions and includes the following endpoints:

### Authentication
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User login

### Properties
- `GET /api/v1/properties` - Get public properties (with filters)
- `GET /api/v1/properties/:id` - Get property details
- `POST /api/v1/properties` - Create property (authenticated)
- `PUT /api/v1/properties/:id` - Update property (authenticated)
- `DELETE /api/v1/properties/:id` - Delete property (authenticated)

### User Dashboard
- `GET /api/v1/dashboard` - Get user dashboard data
- `GET /api/v1/my-properties` - Get user's properties

### Admin
- `GET /api/v1/admin/stats/dashboard` - Admin dashboard stats
- `GET /api/v1/admin/properties/pending` - Pending properties
- `POST /api/v1/admin/properties/:id/approve` - Approve property
- `POST /api/v1/admin/properties/:id/reject` - Reject property

### Payments
- `POST /api/v1/payments/create-checkout-session` - Create Stripe session
- `POST /api/v1/payments/webhook` - Stripe webhook handler

## Configuration

Key environment variables:

```env
# Server
PORT=8080
GIN_MODE=debug

# Database
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=password
DB_NAME=real_estate

# JWT
JWT_SECRET=your-secret-key
JWT_EXPIRATION=24h

# Stripe
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# AWS S3
AWS_ACCESS_KEY_ID=your-key
AWS_SECRET_ACCESS_KEY=your-secret
AWS_S3_BUCKET=your-bucket

# OpenAI
OPENAI_API_KEY=sk-...
```

## Strategy Pattern Implementation

### Authentication Strategy
```go
type AuthStrategy interface {
    Register(req *RegisterRequest) (*AuthResponse, error)
    Login(req *LoginRequest) (*AuthResponse, error)
    ValidateToken(token string) (*User, error)
}
```

### Property Strategy
```go
type PropertyStrategy interface {
    CreateProperty(userID uint, req *CreatePropertyRequest) (*Property, error)
    GetProperties(filters PropertyFilters) (*PropertyListResponse, error)
    // ... other methods
}
```

### Payment Strategy
```go
type PaymentStrategy interface {
    CreateCheckoutSession(userID uint, membershipID uint, successURL, cancelURL string) (*CheckoutSessionResponse, error)
    ProcessWebhook(payload []byte, signature string) error
}
```

## Testing

Run tests with:

```bash
go test -v ./...
# or using Makefile
make test
```

## Deployment

### Using Docker

1. **Build Docker image**
   ```bash
   docker build -t real-estate-backend .
   # or using Makefile
   make docker-build
   ```

2. **Run container**
   ```bash
   docker run -p 8080:8080 --env-file .env real-estate-backend
   # or using Makefile
   make docker-run
   ```

### Using Docker Compose

```bash
docker-compose up -d
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For support and questions, please open an issue in the GitHub repository.
