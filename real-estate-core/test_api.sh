#!/bin/bash

BASE_URL="http://localhost:8080/api/v1"

echo "=== Testing Real Estate API ==="
echo

# Test health check
echo "1. Testing health check..."
curl -s "http://localhost:8080/health" | jq .
echo

# Test get categories
echo "2. Testing get categories..."
curl -s "$BASE_URL/categories" | jq .
echo

# Test get memberships
echo "3. Testing get memberships..."
curl -s "$BASE_URL/memberships" | jq .
echo

# Test get public properties
echo "4. Testing get public properties..."
curl -s "$BASE_URL/properties" | jq .
echo

# Test user registration with new user
echo "5. Testing user registration..."
TIMESTAMP=$(date +%s)
REGISTER_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/register" \
  -H "Content-Type: application/json" \
  -d "{
    \"username\": \"testuser$TIMESTAMP\",
    \"email\": \"test$<EMAIL>\",
    \"password\": \"password123\",
    \"firstName\": \"Test\",
    \"lastName\": \"User\",
    \"phoneNumber\": \"**********\"
  }")
echo "$REGISTER_RESPONSE" | jq .

# Extract token if registration successful
TOKEN=$(echo "$REGISTER_RESPONSE" | jq -r '.token // empty')
echo

if [ -n "$TOKEN" ]; then
  echo "6. Testing authenticated endpoints with token..."
  
  # Test get user dashboard
  echo "6a. Testing get user dashboard..."
  curl -s "$BASE_URL/dashboard" \
    -H "Authorization: Bearer $TOKEN" | jq .
  echo
  
  # Test create property
  echo "6b. Testing create property..."
  curl -s -X POST "$BASE_URL/properties" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $TOKEN" \
    -d '{
      "title": "Test Property",
      "description": "A beautiful test property",
      "price": 500000,
      "address": "123 Test Street",
      "city": "Ho Chi Minh City",
      "district": "District 1",
      "categoryId": 1,
      "propertyType": "APARTMENT",
      "listingType": "SALE",
      "bedrooms": 2,
      "bathrooms": 2,
      "propertyArea": 80.5
    }' | jq .
  echo
  
  # Test get user properties
  echo "6c. Testing get user properties..."
  curl -s "$BASE_URL/my-properties" \
    -H "Authorization: Bearer $TOKEN" | jq .
  echo
  
else
  echo "Registration failed or no token received. Skipping authenticated tests."
fi

# Test login with existing user
echo "7. Testing user login..."
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "password123"
  }')
echo "$LOGIN_RESPONSE" | jq .
echo

echo "=== API Testing Complete ==="
