# ===== SERVER CONFIGURATION =====
[server]
port = 8080
mode = debug
context_path = /api/v1
compression_enabled = true

# ===== DATABASE CONFIGURATION (AWS RDS PostgreSQL) =====
[database]
host = realestate-membership.cvq0uqo4wdkq.ap-southeast-2.rds.amazonaws.com
port = 5432
user = pich
password = 51224!Pich
dbname = realestate_membership
sslmode = require
max_open_conns = 25
max_idle_conns = 5
conn_max_lifetime = 300s

# ===== JWT CONFIGURATION =====
[jwt]
secret = real-estate-jwt-secret-key-2024
expiration = 24h

# ===== OPENAI API CONFIGURATION =====
[openai]
api_key = ********************************************************************************************************************************************************************
base_url = https://api.openai.com/v1
model = gpt-3.5-turbo
max_tokens = 2000
temperature = 0.3
timeout = 45s

# ===== AWS S3 CONFIGURATION =====
[aws]
region = ap-southeast-2
s3_bucket = realestate-s3-do-an-4
access_key_id = ********************
secret_access_key = bum109Ol1D4AXZY9iZ7caR7NYay2hOVi8BlZy9QT
s3_endpoint = https://s3.ap-southeast-2.amazonaws.com

# ===== STRIPE CONFIGURATION =====
[stripe]
publishable_key = pk_test_51RaWOePgfY69EPSnfledX6IA8fKcCGmeAIwE95PCFRpNUR6JvfIuDqQZuSxlwDu2pApNAxChruXcpgnoM1uuaMNe00PbrY9aOi
secret_key = sk_test_51RaeDg4DeBZQeCGMdy9zyW47HM5EZcAiWuxTufnGRxT594XpEorL7cDhLOUsZLNL2Vu2XCt9D5dEKn7SpxxAEgh400Khez8B6c
webhook_secret = whsec_537e58abd7bc0fa01b72f24f07ffc48ea7f9777ff2432d0ad9981911fa894583
currency = usd
success_url = http://localhost:3000/payment/success
cancel_url = http://localhost:3000/payment/cancel

# ===== PAYMENT CONFIGURATION =====
[payment]
currency = VND
min_amount = 1000
max_amount = 100000000
fee_rate = 0.02
auto_refund_enabled = false

# ===== APPLICATION CONFIGURATION =====
[app]
default_page_size = 10
max_page_size = 100
frontend_url = http://localhost:3000
upload_dir = uploads/
upload_max_size = 10MB
allowed_file_types = jpg,jpeg,png,gif,pdf,doc,docx

# ===== CORS CONFIGURATION =====
[cors]
allowed_origins = http://localhost:3000,http://localhost:3001,http://localhost:8080
allowed_methods = GET,POST,PUT,DELETE,PATCH,OPTIONS
allowed_headers = *
allow_credentials = true

# ===== LOGGING CONFIGURATION =====
[logging]
level = DEBUG
log_file = logs/app.log
max_size = 100MB
max_backups = 5
max_age = 30

# ===== RATE LIMITING =====
[rate_limit]
requests_per_minute = 100
burst_size = 200
auth_requests_per_minute = 5
upload_requests_per_hour = 20
