package config

import (
	"fmt"
	"os"
	"strconv"
	"time"

	"gopkg.in/ini.v1"
)

type Config struct {
	Server   ServerConfig
	Database DatabaseConfig
	JWT      JWTConfig
	Stripe   StripeConfig
	AWS      AWSConfig
	OpenAI   OpenAIConfig
	App      AppConfig
	CORS     CORSConfig
	Payment  PaymentConfig
}

type ServerConfig struct {
	Port        string
	Mode        string
	ContextPath string
}

type DatabaseConfig struct {
	Host            string
	Port            string
	User            string
	Password        string
	DBName          string
	SSLMode         string
	MaxOpenConns    int
	MaxIdleConns    int
	ConnMaxLifetime time.Duration
}

type JWTConfig struct {
	Secret     string
	Expiration time.Duration
}

type StripeConfig struct {
	SecretKey      string
	PublishableKey string
	WebhookSecret  string
	Currency       string
	SuccessURL     string
	CancelURL      string
}

type AWSConfig struct {
	AccessKeyID     string
	SecretAccessKey string
	Region          string
	S3Bucket        string
	S3Endpoint      string
}

type OpenAIConfig struct {
	APIKey      string
	BaseURL     string
	Model       string
	MaxTokens   int
	Temperature float64
	Timeout     time.Duration
}

type AppConfig struct {
	DefaultPageSize  int
	MaxPageSize      int
	FrontendURL      string
	UploadDir        string
	UploadMaxSize    string
	AllowedFileTypes string
}

type CORSConfig struct {
	AllowedOrigins   string
	AllowedMethods   string
	AllowedHeaders   string
	AllowCredentials bool
}

type PaymentConfig struct {
	Currency          string
	MinAmount         int64
	MaxAmount         int64
	FeeRate           float64
	AutoRefundEnabled bool
}

func Load() (*Config, error) {
	// Try to load from INI file first, fallback to environment variables
	configFile := getEnv("CONFIG_FILE", "config/app.ini")
	
	if _, err := os.Stat(configFile); err == nil {
		return loadFromINI(configFile)
	}
	
	return loadFromEnv(), nil
}

func loadFromINI(filename string) (*Config, error) {
	cfg, err := ini.Load(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to load config file: %w", err)
	}

	config := &Config{
		Server: ServerConfig{
			Port:        cfg.Section("server").Key("port").MustString("8080"),
			Mode:        cfg.Section("server").Key("mode").MustString("debug"),
			ContextPath: cfg.Section("server").Key("context_path").MustString("/api/v1"),
		},
		Database: DatabaseConfig{
			Host:            cfg.Section("database").Key("host").MustString("localhost"),
			Port:            cfg.Section("database").Key("port").MustString("5432"),
			User:            cfg.Section("database").Key("user").MustString("postgres"),
			Password:        cfg.Section("database").Key("password").MustString("password"),
			DBName:          cfg.Section("database").Key("dbname").MustString("real_estate"),
			SSLMode:         cfg.Section("database").Key("sslmode").MustString("disable"),
			MaxOpenConns:    cfg.Section("database").Key("max_open_conns").MustInt(25),
			MaxIdleConns:    cfg.Section("database").Key("max_idle_conns").MustInt(5),
			ConnMaxLifetime: cfg.Section("database").Key("conn_max_lifetime").MustDuration(300 * time.Second),
		},
		JWT: JWTConfig{
			Secret:     cfg.Section("jwt").Key("secret").MustString("your-secret-key"),
			Expiration: cfg.Section("jwt").Key("expiration").MustDuration(24 * time.Hour),
		},
		Stripe: StripeConfig{
			SecretKey:      cfg.Section("stripe").Key("secret_key").MustString(""),
			PublishableKey: cfg.Section("stripe").Key("publishable_key").MustString(""),
			WebhookSecret:  cfg.Section("stripe").Key("webhook_secret").MustString(""),
			Currency:       cfg.Section("stripe").Key("currency").MustString("usd"),
			SuccessURL:     cfg.Section("stripe").Key("success_url").MustString("http://localhost:3000/payment/success"),
			CancelURL:      cfg.Section("stripe").Key("cancel_url").MustString("http://localhost:3000/payment/cancel"),
		},
		AWS: AWSConfig{
			AccessKeyID:     cfg.Section("aws").Key("access_key_id").MustString(""),
			SecretAccessKey: cfg.Section("aws").Key("secret_access_key").MustString(""),
			Region:          cfg.Section("aws").Key("region").MustString("us-east-1"),
			S3Bucket:        cfg.Section("aws").Key("s3_bucket").MustString(""),
			S3Endpoint:      cfg.Section("aws").Key("s3_endpoint").MustString(""),
		},
		OpenAI: OpenAIConfig{
			APIKey:      cfg.Section("openai").Key("api_key").MustString(""),
			BaseURL:     cfg.Section("openai").Key("base_url").MustString("https://api.openai.com/v1"),
			Model:       cfg.Section("openai").Key("model").MustString("gpt-3.5-turbo"),
			MaxTokens:   cfg.Section("openai").Key("max_tokens").MustInt(2000),
			Temperature: cfg.Section("openai").Key("temperature").MustFloat64(0.3),
			Timeout:     cfg.Section("openai").Key("timeout").MustDuration(45 * time.Second),
		},
		App: AppConfig{
			DefaultPageSize:  cfg.Section("app").Key("default_page_size").MustInt(10),
			MaxPageSize:      cfg.Section("app").Key("max_page_size").MustInt(100),
			FrontendURL:      cfg.Section("app").Key("frontend_url").MustString("http://localhost:3000"),
			UploadDir:        cfg.Section("app").Key("upload_dir").MustString("uploads/"),
			UploadMaxSize:    cfg.Section("app").Key("upload_max_size").MustString("10MB"),
			AllowedFileTypes: cfg.Section("app").Key("allowed_file_types").MustString("jpg,jpeg,png,gif,pdf,doc,docx"),
		},
		CORS: CORSConfig{
			AllowedOrigins:   cfg.Section("cors").Key("allowed_origins").MustString("http://localhost:3000"),
			AllowedMethods:   cfg.Section("cors").Key("allowed_methods").MustString("GET,POST,PUT,DELETE,PATCH,OPTIONS"),
			AllowedHeaders:   cfg.Section("cors").Key("allowed_headers").MustString("*"),
			AllowCredentials: cfg.Section("cors").Key("allow_credentials").MustBool(true),
		},
		Payment: PaymentConfig{
			Currency:          cfg.Section("payment").Key("currency").MustString("VND"),
			MinAmount:         cfg.Section("payment").Key("min_amount").MustInt64(1000),
			MaxAmount:         cfg.Section("payment").Key("max_amount").MustInt64(100000000),
			FeeRate:           cfg.Section("payment").Key("fee_rate").MustFloat64(0.02),
			AutoRefundEnabled: cfg.Section("payment").Key("auto_refund_enabled").MustBool(false),
		},
	}

	return config, nil
}

func loadFromEnv() *Config {
	return &Config{
		Server: ServerConfig{
			Port: getEnv("PORT", "8080"),
			Mode: getEnv("GIN_MODE", "debug"),
		},
		Database: DatabaseConfig{
			Host:     getEnv("DB_HOST", "localhost"),
			Port:     getEnv("DB_PORT", "5432"),
			User:     getEnv("DB_USER", "postgres"),
			Password: getEnv("DB_PASSWORD", "password"),
			DBName:   getEnv("DB_NAME", "real_estate"),
			SSLMode:  getEnv("DB_SSL_MODE", "disable"),
		},
		JWT: JWTConfig{
			Secret:     getEnv("JWT_SECRET", "your-secret-key"),
			Expiration: getEnvDuration("JWT_EXPIRATION", 24*time.Hour),
		},
		Stripe: StripeConfig{
			SecretKey:      getEnv("STRIPE_SECRET_KEY", ""),
			PublishableKey: getEnv("STRIPE_PUBLISHABLE_KEY", ""),
			WebhookSecret:  getEnv("STRIPE_WEBHOOK_SECRET", ""),
		},
		AWS: AWSConfig{
			AccessKeyID:     getEnv("AWS_ACCESS_KEY_ID", ""),
			SecretAccessKey: getEnv("AWS_SECRET_ACCESS_KEY", ""),
			Region:          getEnv("AWS_REGION", "us-east-1"),
			S3Bucket:        getEnv("AWS_S3_BUCKET", ""),
		},
		OpenAI: OpenAIConfig{
			APIKey: getEnv("OPENAI_API_KEY", ""),
			Model:  getEnv("OPENAI_MODEL", "gpt-3.5-turbo"),
		},
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvDuration(key string, defaultValue time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
	}
	return defaultValue
}

func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}
