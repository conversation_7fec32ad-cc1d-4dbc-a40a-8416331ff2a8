package models

import (
	"time"

	"gorm.io/gorm"
)

type UserRole string
type UserStatus string

const (
	UserRoleUser  UserRole = "USER"
	UserRoleAdmin UserRole = "ADMIN"

	UserStatusActive   UserStatus = "ACTIVE"
	UserStatusInactive UserStatus = "INACTIVE"
	UserStatusBanned   UserStatus = "BANNED"
)

type User struct {
	ID            uint       `json:"id" gorm:"primaryKey"`
	Username      string     `json:"username" gorm:"uniqueIndex;not null"`
	Email         string     `json:"email" gorm:"uniqueIndex;not null"`
	Password      string     `json:"-" gorm:"not null"`
	FirstName     string     `json:"first_name" gorm:"not null"`
	LastName      string     `json:"last_name" gorm:"not null"`
	PhoneNumber   *string    `json:"phone_number"`
	Role          UserRole   `json:"role" gorm:"default:USER"`
	Status        UserStatus `json:"status" gorm:"default:ACTIVE"`
	EmailVerified bool       `json:"email_verified" gorm:"default:false"`
	OAuthProvider string     `json:"oauth_provider" gorm:"default:LOCAL"`
	OAuthID       *string    `json:"oauth_id"`
	CreatedAt     time.Time  `json:"created_at"`
	UpdatedAt     time.Time  `json:"updated_at"`
	DeletedAt     gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	Properties      []Property      `json:"properties,omitempty" gorm:"foreignKey:UserID"`
	UserMemberships []UserMembership `json:"user_memberships,omitempty" gorm:"foreignKey:UserID"`
	Payments        []Payment       `json:"payments,omitempty" gorm:"foreignKey:UserID"`
}

type RegisterRequest struct {
	Username    string `json:"username" binding:"required,min=3,max=50"`
	Email       string `json:"email" binding:"required,email"`
	Password    string `json:"password" binding:"required,min=6"`
	FirstName   string `json:"firstName" binding:"required,min=1,max=50"`
	LastName    string `json:"lastName" binding:"required,min=1,max=50"`
	PhoneNumber string `json:"phoneNumber"`
}

type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

type AuthResponse struct {
	Token    string   `json:"token"`
	Type     string   `json:"type"`
	Username string   `json:"username"`
	Email    string   `json:"email"`
	Role     UserRole `json:"role"`
	Message  string   `json:"message"`
}

type UserProfile struct {
	ID          uint       `json:"id"`
	Username    string     `json:"username"`
	Email       string     `json:"email"`
	FirstName   string     `json:"first_name"`
	LastName    string     `json:"last_name"`
	PhoneNumber *string    `json:"phone_number"`
	Role        UserRole   `json:"role"`
	Status      UserStatus `json:"status"`
	CreatedAt   time.Time  `json:"created_at"`
}

func (u *User) ToProfile() *UserProfile {
	return &UserProfile{
		ID:          u.ID,
		Username:    u.Username,
		Email:       u.Email,
		FirstName:   u.FirstName,
		LastName:    u.LastName,
		PhoneNumber: u.PhoneNumber,
		Role:        u.Role,
		Status:      u.Status,
		CreatedAt:   u.CreatedAt,
	}
}

func (u *User) IsAdmin() bool {
	return u.Role == UserRoleAdmin
}

func (u *User) IsActive() bool {
	return u.Status == UserStatusActive
}
