package models

import (
	"time"

	"gorm.io/gorm"
)

type PropertyType string
type ListingType string
type PropertyStatus string

const (
	PropertyTypeApartment PropertyType = "APARTMENT"
	PropertyTypeHouse     PropertyType = "HOUSE"
	PropertyTypeVilla     PropertyType = "VILLA"
	PropertyTypeLand      PropertyType = "LAND"
	PropertyTypeOffice    PropertyType = "OFFICE"
	PropertyTypeShop      PropertyType = "SHOP"

	ListingTypeSale ListingType = "SALE"
	ListingTypeRent ListingType = "RENT"

	PropertyStatusPending  PropertyStatus = "PENDING"
	PropertyStatusApproved PropertyStatus = "APPROVED"
	PropertyStatusRejected PropertyStatus = "REJECTED"
	PropertyStatusSold     PropertyStatus = "SOLD"
	PropertyStatusRented   PropertyStatus = "RENTED"
	PropertyStatusExpired  PropertyStatus = "EXPIRED"
)

type Property struct {
	ID           uint            `json:"id" gorm:"primaryKey"`
	UserID       uint            `json:"user_id" gorm:"not null"`
	CategoryID   uint            `json:"category_id" gorm:"not null"`
	Title        string          `json:"title" gorm:"not null"`
	Description  *string         `json:"description"`
	Price        float64         `json:"price" gorm:"not null"`
	Address      string          `json:"address" gorm:"not null"`
	City         string          `json:"city" gorm:"not null"`
	District     *string         `json:"district"`
	Ward         *string         `json:"ward"`
	Latitude     *float64        `json:"latitude"`
	Longitude    *float64        `json:"longitude"`
	PropertyArea *float64        `json:"property_area"`
	LandArea     *float64        `json:"land_area"`
	Bedrooms     *int            `json:"bedrooms"`
	Bathrooms    *int            `json:"bathrooms"`
	Floors       *int            `json:"floors"`
	PropertyType PropertyType    `json:"property_type"`
	ListingType  ListingType     `json:"listing_type"`
	Status       PropertyStatus  `json:"status" gorm:"default:PENDING"`
	IsFeatured   bool            `json:"is_featured" gorm:"default:false"`
	ViewCount    int             `json:"view_count" gorm:"default:0"`
	ContactCount int             `json:"contact_count" gorm:"default:0"`
	PublishedAt  *time.Time      `json:"published_at"`
	ExpiresAt    *time.Time      `json:"expires_at"`
	CreatedAt    time.Time       `json:"created_at"`
	UpdatedAt    time.Time       `json:"updated_at"`
	DeletedAt    gorm.DeletedAt  `json:"-" gorm:"index"`

	// Relationships
	User     User            `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Category Category        `json:"category,omitempty" gorm:"foreignKey:CategoryID"`
	Images   []PropertyImage `json:"images,omitempty" gorm:"foreignKey:PropertyID"`
	Boosts   []PropertyBoost `json:"boosts,omitempty" gorm:"foreignKey:PropertyID"`
}

type PropertyImage struct {
	ID           uint      `json:"id" gorm:"primaryKey"`
	PropertyID   uint      `json:"property_id" gorm:"not null"`
	ImageURL     string    `json:"image_url" gorm:"not null"`
	ImageKey     *string   `json:"image_key"`
	IsPrimary    bool      `json:"is_primary" gorm:"default:false"`
	DisplayOrder int       `json:"display_order" gorm:"default:0"`
	CreatedAt    time.Time `json:"created_at"`
}

type PropertyBoost struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	PropertyID uint     `json:"property_id" gorm:"not null"`
	UserID    uint      `json:"user_id" gorm:"not null"`
	BoostType string    `json:"boost_type" gorm:"default:PUSH_TOP"`
	StartTime time.Time `json:"start_time" gorm:"not null"`
	EndTime   time.Time `json:"end_time" gorm:"not null"`
	IsActive  bool      `json:"is_active" gorm:"default:true"`
	CreatedAt time.Time `json:"created_at"`
}

type CreatePropertyRequest struct {
	Title        string       `json:"title" binding:"required,min=1,max=200"`
	Description  string       `json:"description"`
	Price        float64      `json:"price" binding:"required,gt=0"`
	PropertyArea *float64     `json:"propertyArea"`
	Bedrooms     *int         `json:"bedrooms"`
	Bathrooms    *int         `json:"bathrooms"`
	Address      string       `json:"address" binding:"required"`
	City         string       `json:"city" binding:"required"`
	District     string       `json:"district"`
	Ward         string       `json:"ward"`
	CategoryID   uint         `json:"categoryId" binding:"required"`
	PropertyType PropertyType `json:"propertyType" binding:"required"`
	ListingType  ListingType  `json:"listingType" binding:"required"`
}

type PropertyResponse struct {
	ID           uint            `json:"id"`
	Title        string          `json:"title"`
	Description  *string         `json:"description"`
	Price        float64         `json:"price"`
	Address      string          `json:"address"`
	City         string          `json:"city"`
	District     *string         `json:"district"`
	PropertyArea *float64        `json:"property_area"`
	Bedrooms     *int            `json:"bedrooms"`
	Bathrooms    *int            `json:"bathrooms"`
	PropertyType PropertyType    `json:"property_type"`
	ListingType  ListingType     `json:"listing_type"`
	Status       PropertyStatus  `json:"status"`
	ViewCount    int             `json:"view_count"`
	ContactCount int             `json:"contact_count"`
	OwnerName    string          `json:"owner_name"`
	OwnerPhone   *string         `json:"owner_phone"`
	CategoryName string          `json:"category_name"`
	Images       []PropertyImage `json:"images"`
	CreatedAt    time.Time       `json:"created_at"`
}

func (p *Property) ToResponse() *PropertyResponse {
	ownerName := p.User.FirstName + " " + p.User.LastName
	return &PropertyResponse{
		ID:           p.ID,
		Title:        p.Title,
		Description:  p.Description,
		Price:        p.Price,
		Address:      p.Address,
		City:         p.City,
		District:     p.District,
		PropertyArea: p.PropertyArea,
		Bedrooms:     p.Bedrooms,
		Bathrooms:    p.Bathrooms,
		PropertyType: p.PropertyType,
		ListingType:  p.ListingType,
		Status:       p.Status,
		ViewCount:    p.ViewCount,
		ContactCount: p.ContactCount,
		OwnerName:    ownerName,
		OwnerPhone:   p.User.PhoneNumber,
		CategoryName: p.Category.Name,
		Images:       p.Images,
		CreatedAt:    p.CreatedAt,
	}
}
