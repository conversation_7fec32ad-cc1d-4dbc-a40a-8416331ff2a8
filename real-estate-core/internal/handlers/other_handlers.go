package handlers

import (
	"net/http"
	"real-estate-core/internal/services"

	"github.com/gin-gonic/gin"
)

type PaymentHandler struct {
	services *services.Services
}

func NewPaymentHandler(services *services.Services) *PaymentHandler {
	return &PaymentHandler{
		services: services,
	}
}

func (h *PaymentHandler) CreateCheckoutSession(c *gin.Context) {
	c.Set("authStrategy", h.services.Auth)

	c.JSON(http.StatusOK, gin.H{
		"sessionId": "cs_test_session_id",
		"url":       "https://checkout.stripe.com/pay/cs_test_session_id",
	})
}

func (h *PaymentHandler) StripeWebhook(c *gin.Context) {
	c.JSO<PERSON>(http.StatusOK, gin.H{"received": true})
}

type MembershipHandler struct {
	services *services.Services
}

func NewMembershipHandler(services *services.Services) *MembershipHandler {
	return &MembershipHandler{
		services: services,
	}
}

func (h *MembershipHandler) GetMemberships(c *gin.Context) {
	memberships, err := h.services.Membership.GetMemberships()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Internal Server Error",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, memberships)
}

type CategoryHandler struct {
	services *services.Services
}

func NewCategoryHandler(services *services.Services) *CategoryHandler {
	return &CategoryHandler{
		services: services,
	}
}

func (h *CategoryHandler) GetCategories(c *gin.Context) {
	// Simplified implementation
	c.JSON(http.StatusOK, []gin.H{
		{"id": 1, "name": "Apartment", "icon": "apartment"},
		{"id": 2, "name": "House", "icon": "house"},
		{"id": 3, "name": "Villa", "icon": "villa"},
		{"id": 4, "name": "Land", "icon": "land"},
		{"id": 5, "name": "Office", "icon": "office"},
		{"id": 6, "name": "Shop", "icon": "shop"},
	})
}

type DashboardHandler struct {
	services *services.Services
}

func NewDashboardHandler(services *services.Services) *DashboardHandler {
	return &DashboardHandler{
		services: services,
	}
}

func (h *DashboardHandler) GetUserDashboard(c *gin.Context) {
	c.Set("authStrategy", h.services.Auth)

	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "Unauthorized",
			"message": "User ID not found",
		})
		return
	}

	dashboard, err := h.services.Membership.GetDashboardData(userID.(uint))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Internal Server Error",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, dashboard)
}

type ChatbotHandler struct {
	services *services.Services
}

func NewChatbotHandler(services *services.Services) *ChatbotHandler {
	return &ChatbotHandler{
		services: services,
	}
}

func (h *ChatbotHandler) Chat(c *gin.Context) {
	c.Set("authStrategy", h.services.Auth)

	var req struct {
		Message        string `json:"message" binding:"required"`
		ConversationID string `json:"conversationId"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Bad Request",
			"message": "Invalid request data",
		})
		return
	}

	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "Unauthorized",
			"message": "User ID not found",
		})
		return
	}

	response, err := h.services.Chatbot.ProcessMessage(userID.(uint), req.Message, req.ConversationID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Internal Server Error",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, response)
}
