package handlers

import (
	"net/http"
	"real-estate-core/internal/services"
	"strconv"

	"github.com/gin-gonic/gin"
)

type AdminHandler struct {
	services *services.Services
}

func NewAdminHandler(services *services.Services) *AdminHandler {
	return &AdminHandler{
		services: services,
	}
}

func (h *AdminHandler) GetDashboardStats(c *gin.Context) {

	stats, err := h.services.Admin.GetDashboardStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Internal Server Error",
			"message": err.Error(),
		})
		return
	}

	c.<PERSON>(http.StatusOK, stats)
}

func (h *AdminHandler) GetPendingProperties(c *gin.Context) {

	page, _ := strconv.Atoi(c.<PERSON>("page", "0"))
	size, _ := strconv.Atoi(c<PERSON>("size", "10"))

	response, err := h.services.Admin.GetPendingProperties(page, size)
	if err != nil {
		c.J<PERSON>(http.StatusInternalServerError, gin.H{
			"error":   "Internal Server Error",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

func (h *AdminHandler) ApproveProperty(c *gin.Context) {

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Bad Request",
			"message": "Invalid property ID",
		})
		return
	}

	isFeatured, _ := strconv.ParseBool(c.DefaultQuery("isFeatured", "false"))
	adminNote := c.DefaultQuery("adminNote", "")

	err = h.services.Admin.ApproveProperty(uint(id), isFeatured, adminNote)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Bad Request",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":    true,
		"message":    "Property approved successfully",
		"propertyId": id,
		"isFeatured": isFeatured,
	})
}

func (h *AdminHandler) RejectProperty(c *gin.Context) {

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Bad Request",
			"message": "Invalid property ID",
		})
		return
	}

	reason := c.Query("reason")
	if reason == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Bad Request",
			"message": "Rejection reason is required",
		})
		return
	}

	err = h.services.Admin.RejectProperty(uint(id), reason)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Bad Request",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":    true,
		"message":    "Property rejected successfully",
		"propertyId": id,
		"reason":     reason,
	})
}

func (h *AdminHandler) GetUsers(c *gin.Context) {

	page, _ := strconv.Atoi(c.DefaultQuery("page", "0"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "10"))

	response, err := h.services.Admin.GetUsers(page, size)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Internal Server Error",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

func (h *AdminHandler) BanUser(c *gin.Context) {

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Bad Request",
			"message": "Invalid user ID",
		})
		return
	}

	err = h.services.Admin.BanUser(uint(id))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Bad Request",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "User banned successfully",
		"userId":  id,
	})
}

func (h *AdminHandler) UnbanUser(c *gin.Context) {

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Bad Request",
			"message": "Invalid user ID",
		})
		return
	}

	err = h.services.Admin.UnbanUser(uint(id))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Bad Request",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "User unbanned successfully",
		"userId":  id,
	})
}
