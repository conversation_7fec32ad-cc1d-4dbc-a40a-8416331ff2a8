package handlers

import (
	"net/http"
	"real-estate-core/internal/models"
	"real-estate-core/internal/services"

	"github.com/gin-gonic/gin"
)

type AuthHandler struct {
	services *services.Services
}

func NewAuthHandler(services *services.Services) *AuthHandler {
	return &AuthHandler{
		services: services,
	}
}

func (h *AuthHandler) Register(c *gin.Context) {
	var req models.RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Bad Request",
			"message": "Invalid request data",
			"details": err.Error(),
		})
		return
	}

	// Set auth strategy in context for middleware
	c.Set("authStrategy", h.services.Auth)

	response, err := h.services.Auth.Register(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Registration Failed",
			"message": err.<PERSON>rror(),
		})
		return
	}

	c.<PERSON>(http.StatusCreated, response)
}

func (h *AuthHandler) Login(c *gin.Context) {
	var req models.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Bad Request",
			"message": "Invalid request data",
			"details": err.Error(),
		})
		return
	}

	// Set auth strategy in context for middleware
	c.Set("authStrategy", h.services.Auth)

	response, err := h.services.Auth.Login(&req)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "Login Failed",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, response)
}
