package repositories

import (
	"real-estate-core/internal/models"
	"real-estate-core/internal/strategies"
	"real-estate-core/internal/types"
	"strings"

	"gorm.io/gorm"
)

type propertyRepository struct {
	db *gorm.DB
}

func NewPropertyRepository(db interface{}) PropertyRepository {
	return &propertyRepository{
		db: db.(*gorm.DB),
	}
}

func (r *propertyRepository) Create(property *models.Property) error {
	return r.db.Create(property).Error
}

func (r *propertyRepository) GetByID(id uint) (*models.Property, error) {
	var property models.Property
	err := r.db.Preload("User").Preload("Category").Preload("Images").First(&property, id).Error
	if err != nil {
		return nil, err
	}
	return &property, nil
}

func (r *propertyRepository) GetByUserID(userID uint, page, size int) ([]models.Property, int64, error) {
	var properties []models.Property
	var total int64

	// Count total records
	if err := r.db.Model(&models.Property{}).Where("user_id = ?", userID).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get paginated results
	offset := page * size
	err := r.db.Preload("User").Preload("Category").Preload("Images").
		Where("user_id = ?", userID).
		Offset(offset).Limit(size).
		Order("created_at DESC").
		Find(&properties).Error
	if err != nil {
		return nil, 0, err
	}

	return properties, total, nil
}

func (r *propertyRepository) Update(property *models.Property) error {
	return r.db.Save(property).Error
}

func (r *propertyRepository) Delete(id uint) error {
	return r.db.Delete(&models.Property{}, id).Error
}

func (r *propertyRepository) List(filters types.PropertyFilters) ([]models.Property, int64, error) {
	var properties []models.Property
	var total int64

	query := r.db.Model(&models.Property{}).Preload("User").Preload("Category").Preload("Images")

	// Apply filters
	query = r.applyFilters(query, filters)

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination and sorting
	offset := filters.Page * filters.Size
	sortBy := filters.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}
	sortDir := filters.SortDir
	if sortDir == "" {
		sortDir = "desc"
	}

	err := query.Offset(offset).Limit(filters.Size).
		Order(sortBy + " " + sortDir).
		Find(&properties).Error
	if err != nil {
		return nil, 0, err
	}

	return properties, total, nil
}

func (r *propertyRepository) GetPublicProperties(filters types.PropertyFilters) ([]models.Property, int64, error) {
	var properties []models.Property
	var total int64

	query := r.db.Model(&models.Property{}).
		Preload("User").Preload("Category").Preload("Images").
		Where("status = ?", models.PropertyStatusApproved)

	// Apply filters
	query = r.applyFilters(query, filters)

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination and sorting
	offset := filters.Page * filters.Size
	sortBy := filters.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}
	sortDir := filters.SortDir
	if sortDir == "" {
		sortDir = "desc"
	}

	err := query.Offset(offset).Limit(filters.Size).
		Order(sortBy + " " + sortDir).
		Find(&properties).Error
	if err != nil {
		return nil, 0, err
	}

	return properties, total, nil
}

func (r *propertyRepository) GetPendingProperties(page, size int) ([]models.Property, int64, error) {
	var properties []models.Property
	var total int64

	// Count total records
	if err := r.db.Model(&models.Property{}).Where("status = ?", models.PropertyStatusPending).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get paginated results
	offset := page * size
	err := r.db.Preload("User").Preload("Category").
		Where("status = ?", models.PropertyStatusPending).
		Offset(offset).Limit(size).
		Order("created_at ASC").
		Find(&properties).Error
	if err != nil {
		return nil, 0, err
	}

	return properties, total, nil
}

func (r *propertyRepository) UpdateStatus(id uint, status models.PropertyStatus) error {
	return r.db.Model(&models.Property{}).Where("id = ?", id).Update("status", status).Error
}

func (r *propertyRepository) IncrementViewCount(id uint) error {
	return r.db.Model(&models.Property{}).Where("id = ?", id).Update("view_count", gorm.Expr("view_count + ?", 1)).Error
}

func (r *propertyRepository) IncrementContactCount(id uint) error {
	return r.db.Model(&models.Property{}).Where("id = ?", id).Update("contact_count", gorm.Expr("contact_count + ?", 1)).Error
}

func (r *propertyRepository) Search(query string, filters strategies.PropertyFilters) ([]models.Property, int64, error) {
	var properties []models.Property
	var total int64

	dbQuery := r.db.Model(&models.Property{}).
		Preload("User").Preload("Category").Preload("Images").
		Where("status = ?", models.PropertyStatusApproved)

	// Apply search query
	if query != "" {
		searchTerm := "%" + strings.ToLower(query) + "%"
		dbQuery = dbQuery.Where("LOWER(title) LIKE ? OR LOWER(description) LIKE ? OR LOWER(address) LIKE ?",
			searchTerm, searchTerm, searchTerm)
	}

	// Apply filters
	dbQuery = r.applyFilters(dbQuery, filters)

	// Count total records
	if err := dbQuery.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination and sorting
	offset := filters.Page * filters.Size
	sortBy := filters.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}
	sortDir := filters.SortDir
	if sortDir == "" {
		sortDir = "desc"
	}

	err := dbQuery.Offset(offset).Limit(filters.Size).
		Order(sortBy + " " + sortDir).
		Find(&properties).Error
	if err != nil {
		return nil, 0, err
	}

	return properties, total, nil
}

func (r *propertyRepository) GetFeatured(limit int) ([]models.Property, error) {
	var properties []models.Property
	err := r.db.Preload("User").Preload("Category").Preload("Images").
		Where("status = ? AND is_featured = ?", models.PropertyStatusApproved, true).
		Limit(limit).
		Order("created_at DESC").
		Find(&properties).Error
	return properties, err
}

func (r *propertyRepository) GetSimilar(propertyID uint, limit int) ([]models.Property, error) {
	// Get the original property to find similar ones
	var originalProperty models.Property
	if err := r.db.First(&originalProperty, propertyID).Error; err != nil {
		return nil, err
	}

	var properties []models.Property
	err := r.db.Preload("User").Preload("Category").Preload("Images").
		Where("status = ? AND id != ? AND (city = ? OR property_type = ? OR category_id = ?)",
			models.PropertyStatusApproved, propertyID, originalProperty.City,
			originalProperty.PropertyType, originalProperty.CategoryID).
		Limit(limit).
		Order("created_at DESC").
		Find(&properties).Error
	return properties, err
}

func (r *propertyRepository) CountByUserID(userID uint) (int64, error) {
	var count int64
	err := r.db.Model(&models.Property{}).Where("user_id = ?", userID).Count(&count).Error
	return count, err
}

func (r *propertyRepository) CountByStatus(userID uint, status models.PropertyStatus) (int64, error) {
	var count int64
	err := r.db.Model(&models.Property{}).Where("user_id = ? AND status = ?", userID, status).Count(&count).Error
	return count, err
}

func (r *propertyRepository) applyFilters(query *gorm.DB, filters strategies.PropertyFilters) *gorm.DB {
	if filters.City != "" {
		query = query.Where("city = ?", filters.City)
	}
	if filters.District != "" {
		query = query.Where("district = ?", filters.District)
	}
	if filters.PropertyType != "" {
		query = query.Where("property_type = ?", filters.PropertyType)
	}
	if filters.ListingType != "" {
		query = query.Where("listing_type = ?", filters.ListingType)
	}
	if filters.MinPrice != nil {
		query = query.Where("price >= ?", *filters.MinPrice)
	}
	if filters.MaxPrice != nil {
		query = query.Where("price <= ?", *filters.MaxPrice)
	}
	if filters.Bedrooms != nil {
		query = query.Where("bedrooms = ?", *filters.Bedrooms)
	}
	if filters.CategoryID != nil {
		query = query.Where("category_id = ?", *filters.CategoryID)
	}
	return query
}
