package repositories

import (
	"real-estate-core/internal/models"

	"gorm.io/gorm"
)

type membershipRepository struct {
	db *gorm.DB
}

func NewMembershipRepository(db interface{}) MembershipRepository {
	return &membershipRepository{
		db: db.(*gorm.DB),
	}
}

func (r *membershipRepository) Create(membership *models.Membership) error {
	return r.db.Create(membership).Error
}

func (r *membershipRepository) GetByID(id uint) (*models.Membership, error) {
	var membership models.Membership
	err := r.db.First(&membership, id).Error
	if err != nil {
		return nil, err
	}
	return &membership, nil
}

func (r *membershipRepository) List() ([]models.Membership, error) {
	var memberships []models.Membership
	err := r.db.Order("price ASC").Find(&memberships).Error
	return memberships, err
}

func (r *membershipRepository) Update(membership *models.Membership) error {
	return r.db.Save(membership).Error
}

func (r *membershipRepository) Delete(id uint) error {
	return r.db.Delete(&models.Membership{}, id).Error
}

func (r *membershipRepository) GetActiveList() ([]models.Membership, error) {
	var memberships []models.Membership
	err := r.db.Where("is_active = ?", true).Order("price ASC").Find(&memberships).Error
	return memberships, err
}

type userMembershipRepository struct {
	db *gorm.DB
}

func NewUserMembershipRepository(db interface{}) UserMembershipRepository {
	return &userMembershipRepository{
		db: db.(*gorm.DB),
	}
}

func (r *userMembershipRepository) Create(userMembership *models.UserMembership) error {
	return r.db.Create(userMembership).Error
}

func (r *userMembershipRepository) GetByID(id uint) (*models.UserMembership, error) {
	var userMembership models.UserMembership
	err := r.db.Preload("User").Preload("Membership").First(&userMembership, id).Error
	if err != nil {
		return nil, err
	}
	return &userMembership, nil
}

func (r *userMembershipRepository) GetActiveByUserID(userID uint) (*models.UserMembership, error) {
	var userMembership models.UserMembership
	err := r.db.Preload("User").Preload("Membership").
		Where("user_id = ? AND status = ? AND end_date > NOW()", userID, models.MembershipStatusActive).
		Order("end_date DESC").
		First(&userMembership).Error
	if err != nil {
		return nil, err
	}
	return &userMembership, nil
}

func (r *userMembershipRepository) GetByUserID(userID uint) ([]models.UserMembership, error) {
	var userMemberships []models.UserMembership
	err := r.db.Preload("User").Preload("Membership").
		Where("user_id = ?", userID).
		Order("created_at DESC").
		Find(&userMemberships).Error
	return userMemberships, err
}

func (r *userMembershipRepository) Update(userMembership *models.UserMembership) error {
	return r.db.Save(userMembership).Error
}

func (r *userMembershipRepository) Delete(id uint) error {
	return r.db.Delete(&models.UserMembership{}, id).Error
}

func (r *userMembershipRepository) UpdateUsage(id uint, propertiesUsed, pushTopUsed int) error {
	return r.db.Model(&models.UserMembership{}).Where("id = ?", id).
		Updates(map[string]interface{}{
			"properties_used": propertiesUsed,
			"push_top_used":   pushTopUsed,
		}).Error
}
