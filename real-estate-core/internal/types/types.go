package types

import (
	"real-estate-core/internal/models"
)

// Common types used across strategies
type PropertyFilters struct {
	City         string
	District     string
	PropertyType models.PropertyType
	ListingType  models.ListingType
	MinPrice     *float64
	MaxPrice     *float64
	Bedrooms     *int
	CategoryID   *uint
	Page         int
	Size         int
	SortBy       string
	SortDir      string
}

type PropertyListResponse struct {
	Content      []models.PropertyResponse `json:"content"`
	Pageable     PageInfo                  `json:"pageable"`
	TotalElements int64                    `json:"total_elements"`
	TotalPages   int                       `json:"total_pages"`
}

type UserListResponse struct {
	Content      []models.UserProfile `json:"content"`
	Pageable     PageInfo             `json:"pageable"`
	TotalElements int64               `json:"total_elements"`
	TotalPages   int                  `json:"total_pages"`
}

type PageInfo struct {
	PageNumber int `json:"page_number"`
	PageSize   int `json:"page_size"`
}

type CheckoutSessionResponse struct {
	SessionID string `json:"sessionId"`
	URL       string `json:"url"`
}

type AdminDashboardStats struct {
	Overview   OverviewStats   `json:"overview"`
	Users      UserStats       `json:"users"`
	Properties PropertyStats   `json:"properties"`
}

type OverviewStats struct {
	TotalUsers       int     `json:"total_users"`
	TotalProperties  int     `json:"total_properties"`
	PendingApprovals int     `json:"pending_approvals"`
	TotalRevenue     float64 `json:"total_revenue"`
}

type UserStats struct {
	TotalUsers     int `json:"total_users"`
	ActiveUsers    int `json:"active_users"`
	NewUsersToday  int `json:"new_users_today"`
}

type PropertyStats struct {
	TotalProperties    int `json:"total_properties"`
	ApprovedProperties int `json:"approved_properties"`
	PendingProperties  int `json:"pending_properties"`
	RejectedProperties int `json:"rejected_properties"`
}

type FileUpload struct {
	Filename string
	Content  []byte
	Size     int64
}

type ChatbotResponse struct {
	Response       string                    `json:"response"`
	ConversationID string                    `json:"conversationId"`
	Properties     []models.PropertyResponse `json:"properties,omitempty"`
}

type UserPreferences struct {
	City         string
	District     string
	PropertyType models.PropertyType
	ListingType  models.ListingType
	MinPrice     *float64
	MaxPrice     *float64
	Bedrooms     *int
}
