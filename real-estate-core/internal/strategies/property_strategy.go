package strategies

import (
	"errors"
	"fmt"
	"real-estate-core/internal/config"
	"real-estate-core/internal/models"
	"real-estate-core/internal/repositories"
	"real-estate-core/internal/types"
	"time"

	"gorm.io/gorm"
)

type propertyStrategy struct {
	propertyRepo       repositories.PropertyRepository
	userMembershipRepo repositories.UserMembershipRepository
	categoryRepo       repositories.CategoryRepository
}

func NewPropertyStrategy(repos *repositories.Repositories, cfg *config.Config) PropertyStrategy {
	return &propertyStrategy{
		propertyRepo:       repos.Property,
		userMembershipRepo: repos.UserMembership,
		categoryRepo:       repos.Category,
	}
}

func (s *propertyStrategy) CreateProperty(userID uint, req *models.CreatePropertyRequest) (*models.Property, error) {
	// Check if user has active membership and property limit
	canCreate, err := s.checkPropertyLimit(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to check property limit: %w", err)
	}
	if !canCreate {
		return nil, errors.New("property limit exceeded for current membership")
	}

	// Validate category exists
	_, err = s.categoryRepo.GetByID(req.CategoryID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("invalid category ID")
		}
		return nil, fmt.Errorf("failed to validate category: %w", err)
	}

	// Create property
	property := &models.Property{
		UserID:       userID,
		CategoryID:   req.CategoryID,
		Title:        req.Title,
		Description:  &req.Description,
		Price:        req.Price,
		Address:      req.Address,
		City:         req.City,
		District:     &req.District,
		Ward:         &req.Ward,
		PropertyArea: req.PropertyArea,
		Bedrooms:     req.Bedrooms,
		Bathrooms:    req.Bathrooms,
		PropertyType: req.PropertyType,
		ListingType:  req.ListingType,
		Status:       models.PropertyStatusPending,
		IsFeatured:   false,
		ViewCount:    0,
		ContactCount: 0,
	}

	if err := s.propertyRepo.Create(property); err != nil {
		return nil, fmt.Errorf("failed to create property: %w", err)
	}

	// Update user membership usage
	if err := s.updateMembershipUsage(userID); err != nil {
		// Log error but don't fail property creation
		fmt.Printf("Warning: Failed to update membership usage for user %d: %v\n", userID, err)
	}

	return property, nil
}

func (s *propertyStrategy) GetProperties(filters types.PropertyFilters) (*types.PropertyListResponse, error) {
	properties, total, err := s.propertyRepo.GetPublicProperties(filters)
	if err != nil {
		return nil, fmt.Errorf("failed to get properties: %w", err)
	}

	// Convert to response format
	responses := make([]models.PropertyResponse, len(properties))
	for i, property := range properties {
		responses[i] = *property.ToResponse()
	}

	totalPages := int((total + int64(filters.Size) - 1) / int64(filters.Size))

	return &types.PropertyListResponse{
		Content: responses,
		Pageable: types.PageInfo{
			PageNumber: filters.Page,
			PageSize:   filters.Size,
		},
		TotalElements: total,
		TotalPages:    totalPages,
	}, nil
}

func (s *propertyStrategy) GetPropertyByID(id uint) (*models.Property, error) {
	property, err := s.propertyRepo.GetByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("property not found")
		}
		return nil, fmt.Errorf("failed to get property: %w", err)
	}

	// Increment view count for approved properties
	if property.Status == models.PropertyStatusApproved {
		if err := s.propertyRepo.IncrementViewCount(id); err != nil {
			// Log error but don't fail the request
			fmt.Printf("Warning: Failed to increment view count for property %d: %v\n", id, err)
		}
	}

	return property, nil
}

func (s *propertyStrategy) UpdateProperty(id uint, userID uint, req *models.CreatePropertyRequest) (*models.Property, error) {
	// Get existing property
	property, err := s.propertyRepo.GetByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("property not found")
		}
		return nil, fmt.Errorf("failed to get property: %w", err)
	}

	// Check ownership
	if property.UserID != userID {
		return nil, errors.New("unauthorized to update this property")
	}

	// Validate category exists
	_, err = s.categoryRepo.GetByID(req.CategoryID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("invalid category ID")
		}
		return nil, fmt.Errorf("failed to validate category: %w", err)
	}

	// Update property fields
	property.CategoryID = req.CategoryID
	property.Title = req.Title
	property.Description = &req.Description
	property.Price = req.Price
	property.Address = req.Address
	property.City = req.City
	property.District = &req.District
	property.Ward = &req.Ward
	property.PropertyArea = req.PropertyArea
	property.Bedrooms = req.Bedrooms
	property.Bathrooms = req.Bathrooms
	property.PropertyType = req.PropertyType
	property.ListingType = req.ListingType

	// Reset status to pending if it was rejected
	if property.Status == models.PropertyStatusRejected {
		property.Status = models.PropertyStatusPending
	}

	if err := s.propertyRepo.Update(property); err != nil {
		return nil, fmt.Errorf("failed to update property: %w", err)
	}

	return property, nil
}

func (s *propertyStrategy) DeleteProperty(id uint, userID uint) error {
	// Get existing property
	property, err := s.propertyRepo.GetByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("property not found")
		}
		return fmt.Errorf("failed to get property: %w", err)
	}

	// Check ownership
	if property.UserID != userID {
		return errors.New("unauthorized to delete this property")
	}

	if err := s.propertyRepo.Delete(id); err != nil {
		return fmt.Errorf("failed to delete property: %w", err)
	}

	return nil
}

func (s *propertyStrategy) GetUserProperties(userID uint, page, size int) (*types.PropertyListResponse, error) {
	properties, total, err := s.propertyRepo.GetByUserID(userID, page, size)
	if err != nil {
		return nil, fmt.Errorf("failed to get user properties: %w", err)
	}

	// Convert to response format
	responses := make([]models.PropertyResponse, len(properties))
	for i, property := range properties {
		responses[i] = *property.ToResponse()
	}

	totalPages := int((total + int64(size) - 1) / int64(size))

	return &types.PropertyListResponse{
		Content: responses,
		Pageable: types.PageInfo{
			PageNumber: page,
			PageSize:   size,
		},
		TotalElements: total,
		TotalPages:    totalPages,
	}, nil
}

func (s *propertyStrategy) checkPropertyLimit(userID uint) (bool, error) {
	// Get active membership
	membership, err := s.userMembershipRepo.GetActiveByUserID(userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return false, errors.New("no active membership found")
		}
		return false, fmt.Errorf("failed to get active membership: %w", err)
	}

	// Check if membership is still valid
	if membership.EndDate.Before(time.Now()) {
		return false, errors.New("membership has expired")
	}

	// Check property limit
	if membership.PropertiesUsed >= membership.Membership.MaxProperties {
		return false, nil
	}

	return true, nil
}

func (s *propertyStrategy) updateMembershipUsage(userID uint) error {
	// Get active membership
	membership, err := s.userMembershipRepo.GetActiveByUserID(userID)
	if err != nil {
		return err
	}

	// Count current properties
	propertyCount, err := s.propertyRepo.CountByUserID(userID)
	if err != nil {
		return err
	}

	// Update usage
	membership.PropertiesUsed = int(propertyCount)
	return s.userMembershipRepo.Update(membership)
}
