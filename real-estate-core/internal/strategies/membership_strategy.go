package strategies

import (
	"errors"
	"fmt"
	"real-estate-backend/internal/config"
	"real-estate-backend/internal/models"
	"real-estate-backend/internal/repositories"
	"time"

	"gorm.io/gorm"
)

type membershipStrategy struct {
	membershipRepo     repositories.MembershipRepository
	userMembershipRepo repositories.UserMembershipRepository
	propertyRepo       repositories.PropertyRepository
}

func NewMembershipStrategy(repos *repositories.Repositories, cfg *config.Config) MembershipStrategy {
	return &membershipStrategy{
		membershipRepo:     repos.Membership,
		userMembershipRepo: repos.UserMembership,
		propertyRepo:       repos.Property,
	}
}

func (s *membershipStrategy) GetActiveMembership(userID uint) (*models.UserMembership, error) {
	membership, err := s.userMembershipRepo.GetActiveByUserID(userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("no active membership found")
		}
		return nil, fmt.Errorf("failed to get active membership: %w", err)
	}
	return membership, nil
}

func (s *membershipStrategy) CreateMembership(userID uint, membershipID uint) (*models.UserMembership, error) {
	// Get membership details
	membership, err := s.membershipRepo.GetByID(membershipID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("membership not found")
		}
		return nil, fmt.Errorf("failed to get membership: %w", err)
	}

	// Create user membership
	userMembership := &models.UserMembership{
		UserID:       userID,
		MembershipID: membershipID,
		StartDate:    time.Now(),
		EndDate:      time.Now().AddDate(0, membership.DurationMonths, 0),
		Status:       models.MembershipStatusActive,
		AutoRenewal:  false,
	}

	if err := s.userMembershipRepo.Create(userMembership); err != nil {
		return nil, fmt.Errorf("failed to create user membership: %w", err)
	}

	return userMembership, nil
}

func (s *membershipStrategy) CheckPropertyLimit(userID uint) (bool, error) {
	// Get active membership
	membership, err := s.userMembershipRepo.GetActiveByUserID(userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return false, errors.New("no active membership found")
		}
		return false, fmt.Errorf("failed to get active membership: %w", err)
	}

	// Check if membership is still valid
	if membership.EndDate.Before(time.Now()) {
		return false, errors.New("membership has expired")
	}

	// Check property limit
	if membership.PropertiesUsed >= membership.Membership.MaxProperties {
		return false, nil
	}

	return true, nil
}

func (s *membershipStrategy) GetMemberships() ([]models.Membership, error) {
	return s.membershipRepo.GetActiveList()
}

func (s *membershipStrategy) GetDashboardData(userID uint) (*models.DashboardResponse, error) {
	// Get active membership
	membership, err := s.userMembershipRepo.GetActiveByUserID(userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// Return default data for users without membership
			return &models.DashboardResponse{
				Membership: models.MembershipInfo{
					PlanName:            "No Active Plan",
					PlanType:            "NONE",
					MaxProperties:       0,
					PropertiesUsed:      0,
					PropertiesRemaining: 0,
					HasAiGeneration:     false,
					PushTopLimit:        0,
					DaysRemaining:       0,
					HasActiveMembership: false,
				},
				Properties: models.PropertyStats{},
				MonthlyUsage: models.MonthlyUsage{
					Month: time.Now().Format("2006-01"),
				},
			}, nil
		}
		return nil, fmt.Errorf("failed to get active membership: %w", err)
	}

	// Calculate days remaining
	daysRemaining := int(time.Until(membership.EndDate).Hours() / 24)
	if daysRemaining < 0 {
		daysRemaining = 0
	}

	// Get property statistics
	totalProperties, _ := s.propertyRepo.CountByUserID(userID)
	approvedProperties, _ := s.propertyRepo.CountByStatus(userID, models.PropertyStatusApproved)
	pendingProperties, _ := s.propertyRepo.CountByStatus(userID, models.PropertyStatusPending)
	rejectedProperties, _ := s.propertyRepo.CountByStatus(userID, models.PropertyStatusRejected)

	return &models.DashboardResponse{
		Membership: models.MembershipInfo{
			PlanName:            membership.Membership.Name,
			PlanType:            string(membership.Membership.Type),
			MaxProperties:       membership.Membership.MaxProperties,
			PropertiesUsed:      membership.PropertiesUsed,
			PropertiesRemaining: membership.Membership.MaxProperties - membership.PropertiesUsed,
			HasAiGeneration:     membership.Membership.AIContentGeneration,
			PushTopLimit:        membership.Membership.PushTopLimit,
			DaysRemaining:       daysRemaining,
			HasActiveMembership: true,
		},
		Properties: models.PropertyStats{
			TotalProperties:    int(totalProperties),
			ApprovedProperties: int(approvedProperties),
			PendingProperties:  int(pendingProperties),
			RejectedProperties: int(rejectedProperties),
			TotalViews:         0, // Simplified
			TotalContacts:      0, // Simplified
		},
		MonthlyUsage: models.MonthlyUsage{
			Month:         time.Now().Format("2006-01"),
			PushTopUsed:   membership.PushTopUsed,
			PushTopLimit:  membership.Membership.PushTopLimit,
			AIContentUsed: 0, // Simplified
		},
	}, nil
}
