package strategies

import (
	"errors"
	"fmt"
	"real-estate-core/internal/config"
	"real-estate-core/internal/models"
	"real-estate-core/internal/repositories"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

type authStrategy struct {
	userRepo           repositories.UserRepository
	userMembershipRepo repositories.UserMembershipRepository
	membershipRepo     repositories.MembershipRepository
	jwtSecret          string
	jwtExpiration      time.Duration
}

func NewAuthStrategy(repos *repositories.Repositories, cfg *config.Config) AuthStrategy {
	return &authStrategy{
		userRepo:           repos.User,
		userMembershipRepo: repos.UserMembership,
		membershipRepo:     repos.Membership,
		jwtSecret:          cfg.JWT.Secret,
		jwtExpiration:      cfg.JWT.Expiration,
	}
}

func (s *authStrategy) Register(req *models.RegisterRequest) (*models.AuthResponse, error) {
	// Check if username already exists
	if _, err := s.userRepo.GetByUsername(req.Username); err == nil {
		return nil, errors.New("username already exists")
	}

	// Check if email already exists
	if _, err := s.userRepo.GetByEmail(req.Email); err == nil {
		return nil, errors.New("email already exists")
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	// Create user
	user := &models.User{
		Username:      req.Username,
		Email:         req.Email,
		Password:      string(hashedPassword),
		FirstName:     req.FirstName,
		LastName:      req.LastName,
		PhoneNumber:   &req.PhoneNumber,
		Role:          models.UserRoleUser,
		Status:        models.UserStatusActive,
		EmailVerified: false,
		OAuthProvider: "LOCAL",
	}

	if err := s.userRepo.Create(user); err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	// Create basic membership for new user (10 free posts)
	if err := s.createBasicMembership(user.ID); err != nil {
		// Log error but don't fail registration
		fmt.Printf("Warning: Failed to create basic membership for user %d: %v\n", user.ID, err)
	}

	// Generate JWT token
	token, err := s.GenerateToken(user)
	if err != nil {
		return nil, fmt.Errorf("failed to generate token: %w", err)
	}

	return &models.AuthResponse{
		Token:    token,
		Type:     "Bearer",
		Username: user.Username,
		Email:    user.Email,
		Role:     user.Role,
		Message:  "Tài khoản đã được tạo và đăng nhập thành công!",
	}, nil
}

func (s *authStrategy) Login(req *models.LoginRequest) (*models.AuthResponse, error) {
	// Get user by username
	user, err := s.userRepo.GetByUsername(req.Username)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("invalid credentials")
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// Check if user is active
	if user.Status != models.UserStatusActive {
		return nil, errors.New("account is not active")
	}

	// Verify password
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.Password)); err != nil {
		return nil, errors.New("invalid credentials")
	}

	// Generate JWT token
	token, err := s.GenerateToken(user)
	if err != nil {
		return nil, fmt.Errorf("failed to generate token: %w", err)
	}

	return &models.AuthResponse{
		Token:    token,
		Type:     "Bearer",
		Username: user.Username,
		Email:    user.Email,
		Role:     user.Role,
		Message:  "Đăng nhập thành công!",
	}, nil
}

func (s *authStrategy) ValidateToken(tokenString string) (*models.User, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(s.jwtSecret), nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	if !token.Valid {
		return nil, errors.New("invalid token")
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, errors.New("invalid token claims")
	}

	userID, ok := claims["user_id"].(float64)
	if !ok {
		return nil, errors.New("invalid user ID in token")
	}

	user, err := s.userRepo.GetByID(uint(userID))
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	if user.Status != models.UserStatusActive {
		return nil, errors.New("user account is not active")
	}

	return user, nil
}

func (s *authStrategy) GenerateToken(user *models.User) (string, error) {
	claims := jwt.MapClaims{
		"user_id":  user.ID,
		"username": user.Username,
		"email":    user.Email,
		"role":     user.Role,
		"exp":      time.Now().Add(s.jwtExpiration).Unix(),
		"iat":      time.Now().Unix(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(s.jwtSecret))
	if err != nil {
		return "", fmt.Errorf("failed to sign token: %w", err)
	}

	return tokenString, nil
}

func (s *authStrategy) createBasicMembership(userID uint) error {
	// Get BASIC membership
	memberships, err := s.membershipRepo.List()
	if err != nil {
		return err
	}

	var basicMembership *models.Membership
	for _, membership := range memberships {
		if membership.Name == "BASIC" {
			basicMembership = &membership
			break
		}
	}

	if basicMembership == nil {
		return errors.New("BASIC membership not found")
	}

	// Create user membership with 10 free posts
	userMembership := &models.UserMembership{
		UserID:         userID,
		MembershipID:   basicMembership.ID,
		StartDate:      time.Now(),
		EndDate:        time.Now().AddDate(0, basicMembership.DurationMonths, 0),
		Status:         models.MembershipStatusActive,
		AutoRenewal:    false,
		PropertiesUsed: 0, // Start with 0 used, can post up to 10
	}

	return s.userMembershipRepo.Create(userMembership)
}
