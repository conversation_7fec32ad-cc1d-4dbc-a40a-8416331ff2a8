package strategies

import (
	"real-estate-core/internal/models"
	"real-estate-core/internal/types"
)

// AuthStrategy defines authentication strategies
type AuthStrategy interface {
	Register(req *models.RegisterRequest) (*models.AuthResponse, error)
	Login(req *models.LoginRequest) (*models.AuthResponse, error)
	ValidateToken(token string) (*models.User, error)
	GenerateToken(user *models.User) (string, error)
}

// PropertyStrategy defines property management strategies
type PropertyStrategy interface {
	CreateProperty(userID uint, req *models.CreatePropertyRequest) (*models.Property, error)
	GetProperties(filters types.PropertyFilters) (*types.PropertyListResponse, error)
	GetPropertyByID(id uint) (*models.Property, error)
	UpdateProperty(id uint, userID uint, req *models.CreatePropertyRequest) (*models.Property, error)
	DeleteProperty(id uint, userID uint) error
	GetUserProperties(userID uint, page, size int) (*types.PropertyListResponse, error)
	BoostProperty(propertyID, userID uint) error
}

// AdminStrategy defines admin-specific strategies
type AdminStrategy interface {
	GetPendingProperties(page, size int) (*types.PropertyListResponse, error)
	ApproveProperty(id uint, isFeatured bool, adminNote string) error
	RejectProperty(id uint, reason string) error
	GetUsers(page, size int) (*types.UserListResponse, error)
	BanUser(userID uint) error
	UnbanUser(userID uint) error
	GetDashboardStats() (*types.AdminDashboardStats, error)
}

// PaymentStrategy defines payment processing strategies
type PaymentStrategy interface {
	CreateCheckoutSession(userID uint, membershipID uint, successURL, cancelURL string) (*types.CheckoutSessionResponse, error)
	ProcessWebhook(payload []byte, signature string) error
	GetUserPayments(userID uint) ([]models.Payment, error)
}

// MembershipStrategy defines membership management strategies
type MembershipStrategy interface {
	GetActiveMembership(userID uint) (*models.UserMembership, error)
	CreateMembership(userID uint, membershipID uint) (*models.UserMembership, error)
	CheckPropertyLimit(userID uint) (bool, error)
	GetMemberships() ([]models.Membership, error)
	GetDashboardData(userID uint) (*models.DashboardResponse, error)
}

// SearchStrategy defines search and filtering strategies
type SearchStrategy interface {
	SearchProperties(query string, filters types.PropertyFilters) (*types.PropertyListResponse, error)
	GetSimilarProperties(propertyID uint, limit int) ([]models.Property, error)
	GetFeaturedProperties(limit int) ([]models.Property, error)
}

// NotificationStrategy defines notification strategies
type NotificationStrategy interface {
	SendPropertyApprovalNotification(userID uint, propertyID uint, approved bool) error
	SendMembershipExpiryNotification(userID uint, daysRemaining int) error
	SendWelcomeNotification(userID uint) error
}

// FileUploadStrategy defines file upload strategies
type FileUploadStrategy interface {
	UploadPropertyImages(propertyID uint, files []types.FileUpload) ([]models.PropertyImage, error)
	DeletePropertyImage(imageID uint) error
	GetSignedURL(key string) (string, error)
}

// ChatbotStrategy defines AI chatbot strategies
type ChatbotStrategy interface {
	ProcessMessage(userID uint, message string, conversationID string) (*types.ChatbotResponse, error)
	GetPropertyRecommendations(userID uint, preferences types.UserPreferences) ([]models.Property, error)
}
