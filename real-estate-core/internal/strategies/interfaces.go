package strategies

import (
	"real-estate-core/internal/models"
)

// AuthStrategy defines authentication strategies
type AuthStrategy interface {
	Register(req *models.RegisterRequest) (*models.AuthResponse, error)
	Login(req *models.LoginRequest) (*models.AuthResponse, error)
	ValidateToken(token string) (*models.User, error)
	GenerateToken(user *models.User) (string, error)
}

// PropertyStrategy defines property management strategies
type PropertyStrategy interface {
	CreateProperty(userID uint, req *models.CreatePropertyRequest) (*models.Property, error)
	GetProperties(filters PropertyFilters) (*PropertyListResponse, error)
	GetPropertyByID(id uint) (*models.Property, error)
	UpdateProperty(id uint, userID uint, req *models.CreatePropertyRequest) (*models.Property, error)
	DeleteProperty(id uint, userID uint) error
	GetUserProperties(userID uint, page, size int) (*PropertyListResponse, error)
}

// AdminStrategy defines admin-specific strategies
type AdminStrategy interface {
	GetPendingProperties(page, size int) (*PropertyListResponse, error)
	ApproveProperty(id uint, isFeatured bool, adminNote string) error
	RejectProperty(id uint, reason string) error
	GetUsers(page, size int) (*UserListResponse, error)
	BanUser(userID uint) error
	UnbanUser(userID uint) error
	GetDashboardStats() (*AdminDashboardStats, error)
}

// PaymentStrategy defines payment processing strategies
type PaymentStrategy interface {
	CreateCheckoutSession(userID uint, membershipID uint, successURL, cancelURL string) (*CheckoutSessionResponse, error)
	ProcessWebhook(payload []byte, signature string) error
	GetUserPayments(userID uint) ([]models.Payment, error)
}

// MembershipStrategy defines membership management strategies
type MembershipStrategy interface {
	GetActiveMembership(userID uint) (*models.UserMembership, error)
	CreateMembership(userID uint, membershipID uint) (*models.UserMembership, error)
	CheckPropertyLimit(userID uint) (bool, error)
	GetMemberships() ([]models.Membership, error)
	GetDashboardData(userID uint) (*models.DashboardResponse, error)
}

// SearchStrategy defines search and filtering strategies
type SearchStrategy interface {
	SearchProperties(query string, filters PropertyFilters) (*PropertyListResponse, error)
	GetSimilarProperties(propertyID uint, limit int) ([]models.Property, error)
	GetFeaturedProperties(limit int) ([]models.Property, error)
}

// NotificationStrategy defines notification strategies
type NotificationStrategy interface {
	SendPropertyApprovalNotification(userID uint, propertyID uint, approved bool) error
	SendMembershipExpiryNotification(userID uint, daysRemaining int) error
	SendWelcomeNotification(userID uint) error
}

// FileUploadStrategy defines file upload strategies
type FileUploadStrategy interface {
	UploadPropertyImages(propertyID uint, files []FileUpload) ([]models.PropertyImage, error)
	DeletePropertyImage(imageID uint) error
	GetSignedURL(key string) (string, error)
}

// ChatbotStrategy defines AI chatbot strategies
type ChatbotStrategy interface {
	ProcessMessage(userID uint, message string, conversationID string) (*ChatbotResponse, error)
	GetPropertyRecommendations(userID uint, preferences UserPreferences) ([]models.Property, error)
}

// Common types used across strategies
type PropertyFilters struct {
	City         string
	District     string
	PropertyType models.PropertyType
	ListingType  models.ListingType
	MinPrice     *float64
	MaxPrice     *float64
	Bedrooms     *int
	CategoryID   *uint
	Page         int
	Size         int
	SortBy       string
	SortDir      string
}

type PropertyListResponse struct {
	Content       []models.PropertyResponse `json:"content"`
	Pageable      PageInfo                  `json:"pageable"`
	TotalElements int64                     `json:"total_elements"`
	TotalPages    int                       `json:"total_pages"`
}

type UserListResponse struct {
	Content       []models.UserProfile `json:"content"`
	Pageable      PageInfo             `json:"pageable"`
	TotalElements int64                `json:"total_elements"`
	TotalPages    int                  `json:"total_pages"`
}

type PageInfo struct {
	PageNumber int `json:"page_number"`
	PageSize   int `json:"page_size"`
}

type CheckoutSessionResponse struct {
	SessionID string `json:"sessionId"`
	URL       string `json:"url"`
}

type AdminDashboardStats struct {
	Overview   OverviewStats `json:"overview"`
	Users      UserStats     `json:"users"`
	Properties PropertyStats `json:"properties"`
}

type OverviewStats struct {
	TotalUsers       int     `json:"total_users"`
	TotalProperties  int     `json:"total_properties"`
	PendingApprovals int     `json:"pending_approvals"`
	TotalRevenue     float64 `json:"total_revenue"`
}

type UserStats struct {
	TotalUsers    int `json:"total_users"`
	ActiveUsers   int `json:"active_users"`
	NewUsersToday int `json:"new_users_today"`
}

type PropertyStats struct {
	TotalProperties    int `json:"total_properties"`
	ApprovedProperties int `json:"approved_properties"`
	PendingProperties  int `json:"pending_properties"`
	RejectedProperties int `json:"rejected_properties"`
}

type FileUpload struct {
	Filename string
	Content  []byte
	Size     int64
}

type ChatbotResponse struct {
	Response       string                    `json:"response"`
	ConversationID string                    `json:"conversationId"`
	Properties     []models.PropertyResponse `json:"properties,omitempty"`
}

type UserPreferences struct {
	City         string
	District     string
	PropertyType models.PropertyType
	ListingType  models.ListingType
	MinPrice     *float64
	MaxPrice     *float64
	Bedrooms     *int
}
