package strategies

import (
	"real-estate-core/internal/config"
	"real-estate-core/internal/models"
	"real-estate-core/internal/repositories"
	"real-estate-core/internal/types"
)

type paymentStrategy struct {
	paymentRepo    repositories.PaymentRepository
	membershipRepo repositories.MembershipRepository
	stripeConfig   config.StripeConfig
}

func NewPaymentStrategy(repos *repositories.Repositories, cfg *config.Config) PaymentStrategy {
	return &paymentStrategy{
		paymentRepo:    repos.Payment,
		membershipRepo: repos.Membership,
		stripeConfig:   cfg.Stripe,
	}
}

func (s *paymentStrategy) CreateCheckoutSession(userID uint, membershipID uint, successURL, cancelURL string) (*types.CheckoutSessionResponse, error) {
	// Simplified implementation - would integrate with Stripe
	return &types.CheckoutSessionResponse{
		SessionID: "cs_test_session_id",
		URL:       "https://checkout.stripe.com/pay/cs_test_session_id",
	}, nil
}

func (s *paymentStrategy) ProcessWebhook(payload []byte, signature string) error {
	// Simplified implementation - would process Stripe webhooks
	return nil
}

func (s *paymentStrategy) GetUserPayments(userID uint) ([]models.Payment, error) {
	return s.paymentRepo.GetByUserID(userID)
}

type searchStrategy struct {
	propertyRepo repositories.PropertyRepository
}

func NewSearchStrategy(repos *repositories.Repositories, cfg *config.Config) SearchStrategy {
	return &searchStrategy{
		propertyRepo: repos.Property,
	}
}

func (s *searchStrategy) SearchProperties(query string, filters types.PropertyFilters) (*types.PropertyListResponse, error) {
	properties, total, err := s.propertyRepo.Search(query, filters)
	if err != nil {
		return nil, err
	}

	// Convert to response format
	responses := make([]models.PropertyResponse, len(properties))
	for i, property := range properties {
		responses[i] = *property.ToResponse()
	}

	totalPages := int((total + int64(filters.Size) - 1) / int64(filters.Size))

	return &types.PropertyListResponse{
		Content: responses,
		Pageable: types.PageInfo{
			PageNumber: filters.Page,
			PageSize:   filters.Size,
		},
		TotalElements: total,
		TotalPages:    totalPages,
	}, nil
}

func (s *searchStrategy) GetSimilarProperties(propertyID uint, limit int) ([]models.Property, error) {
	return s.propertyRepo.GetSimilar(propertyID, limit)
}

func (s *searchStrategy) GetFeaturedProperties(limit int) ([]models.Property, error) {
	return s.propertyRepo.GetFeatured(limit)
}

type chatbotStrategy struct {
	propertyRepo repositories.PropertyRepository
	openAIConfig config.OpenAIConfig
}

func NewChatbotStrategy(repos *repositories.Repositories, cfg *config.Config) ChatbotStrategy {
	return &chatbotStrategy{
		propertyRepo: repos.Property,
		openAIConfig: cfg.OpenAI,
	}
}

func (s *chatbotStrategy) ProcessMessage(userID uint, message string, conversationID string) (*types.ChatbotResponse, error) {
	// Simplified implementation - would integrate with OpenAI
	return &types.ChatbotResponse{
		Response:       "I'm a real estate chatbot. How can I help you find properties?",
		ConversationID: conversationID,
		Properties:     []models.PropertyResponse{},
	}, nil
}

func (s *chatbotStrategy) GetPropertyRecommendations(userID uint, preferences types.UserPreferences) ([]models.Property, error) {
	// Simplified implementation - would use AI to recommend properties
	return []models.Property{}, nil
}
