.PHONY: build run dev test clean deps fmt lint help test-api

# Build the application
build:
	@echo "Building real-estate-core..."
	@mkdir -p bin
	@go build -o bin/real-estate-core ./cmd/main.go
	@echo "Build complete!"

# Run the application (build + start - no migration)
run: build
	@echo "Starting real-estate-core server..."
	@echo "Database schema is stable - no migration needed"
	@echo "Access API at: http://localhost:8080/api/v1"
	@echo "Health check: http://localhost:8080/health"
	@echo "=================================="
	@./bin/real-estate-core

# Run with migration (if needed)
run-migrate: build
	@echo "Starting real-estate-core server with migration..."
	@echo "Will add missing columns but preserve existing data"
	@echo "Access API at: http://localhost:8080/api/v1"
	@echo "Health check: http://localhost:8080/health"
	@echo "=================================="
	@sed 's|// if err := database.RunMigrations|if err := database.RunMigrations|g; s|// }||}|g' cmd/main.go > cmd/main_migrate.go
	@go build -o bin/real-estate-core-migrate ./cmd/main_migrate.go
	@./bin/real-estate-core-migrate
	@rm cmd/main_migrate.go

# Run in development mode with hot reload (requires air)
dev:
	@echo "Starting development server with hot reload..."
	@air

# Run tests
test:
	@echo "Running tests..."
	@go test -v ./...

# Test API endpoints
test-api:
	@echo "Testing API endpoints..."
	@chmod +x test_api.sh
	@./test_api.sh

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	@rm -rf bin/
	@echo "Clean complete!"

# Install dependencies
deps:
	@echo "Installing dependencies..."
	@go mod download
	@go mod tidy
	@echo "Dependencies installed!"

# Format code
fmt:
	@echo "Formatting code..."
	@go fmt ./...
	@echo "Code formatted!"

# Lint code (requires golangci-lint)
lint:
	@echo "Linting code..."
	@golangci-lint run
	@echo "Linting complete!"

# Install development tools
install-tools:
	@echo "Installing development tools..."
	@go install github.com/cosmtrek/air@latest
	@go install github.com/golangci-lint/golangci-lint/cmd/golangci-lint@latest
	@echo "Development tools installed!"

# Help
help:
	@echo "Real Estate Core Backend"
	@echo "========================"
	@echo "Available commands:"
	@echo "  make run         - Build and run server (fast startup)"
	@echo "  make run-migrate - Build and run server with migration"
	@echo "  make dev         - Run with hot reload (requires air)"
	@echo "  make test        - Run unit tests"
	@echo "  make test-api    - Test API endpoints"
	@echo "  make build       - Build binary only"
	@echo "  make clean       - Clean build artifacts"
	@echo "  make deps        - Install dependencies"
	@echo "  make fmt         - Format code"
	@echo "  make lint        - Lint code"
	@echo ""
	@echo "Quick start:"
	@echo "  1. make deps     # Install dependencies"
	@echo "  2. make run      # Start server (fast - no migration)"
	@echo "  3. make test-api # Test endpoints"
	@echo ""
	@echo "Database:"
	@echo "  - Schema is stable, no migration needed"
	@echo "  - Use 'make run-migrate' only if schema changes"
	@echo "  - Payment test card: ****************"

# Default target
.DEFAULT_GOAL := help
