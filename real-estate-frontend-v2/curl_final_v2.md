# 🚀 Real Estate Membership API - Final Test Commands v2

## 📋 **OVERVIEW**
Complete curl commands to test all features including new Push Top, AI Content Generation, and Dashboard functionality.

---

## 🔧 **SETUP**
```bash
# Start application
./mvnw spring-boot:run

# Base URL
BASE_URL="http://localhost:8080/api/v1"
```

---

## 👤 **1. AUTHENTICATION**

### **Register New User**
```bash
curl -X POST $BASE_URL/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser2",
    "email": "<EMAIL>", 
    "password": "password123",
    "firstName": "Test",
    "lastName": "User",
    "phoneNumber": "0123456789"
  }' | jq .
```

### **Login**
```bash
curl -X POST $BASE_URL/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser2",
    "password": "password123"
  }' | jq .
```

### **Save Token (Replace with actual token)**
```bash
TOKEN="eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ0ZXN0dXNlcjIiLCJpYXQiOjE3NTE5MDYzNzMsImV4cCI6MTc1MTk5Mjc3M30.70A04YoGmVuku1LEVrtxB7HJmg-dBYVoY2wmeHsyzJM"
```

---

## 💎 **2. MEMBERSHIPS**

### **Get All Memberships**
```bash
curl -s $BASE_URL/memberships | jq .
```

### **Get Membership Details**
```bash
curl -s $BASE_URL/memberships/1 | jq .
```

### **Purchase Membership (Advanced)**
```bash
curl -X POST $BASE_URL/memberships/purchase \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "membershipId": 2,
    "paymentMethod": "STRIPE"
  }' | jq .
```

### **Get My Current Membership**
```bash
curl -X GET $BASE_URL/memberships/my-membership \
  -H "Authorization: Bearer $TOKEN" | jq .
```

---

## 📊 **3. USER DASHBOARD**

### **Get User Dashboard**
```bash
curl -X GET $BASE_URL/dashboard \
  -H "Authorization: Bearer $TOKEN" | jq .
```

### **Get Property SEO Score**
```bash
curl -X GET $BASE_URL/dashboard/seo/score/1 \
  -H "Authorization: Bearer $TOKEN" | jq .
```

---

## 🏠 **4. PROPERTIES**

### **Get All Properties**
```bash
curl -s $BASE_URL/properties | jq .
```

### **Create Property**
```bash
curl -X POST $BASE_URL/properties \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "title": "Căn hộ cao cấp Quận 1",
    "description": "Căn hộ 2 phòng ngủ tại quận 1, gần chợ Bến Thành, view đẹp",
    "price": 5000000000,
    "propertyArea": 80.5,
    "bedrooms": 2,
    "bathrooms": 2,
    "address": "123 Nguyễn Huệ",
    "city": "Hồ Chí Minh",
    "district": "Quận 1",
    "ward": "Phường Bến Nghé",
    "categoryId": 1,
    "propertyType": "APARTMENT",
    "listingType": "SALE"
  }' | jq .
```

---

## 🚀 **5. PUSH TOP FEATURE**

### **Check Push Top Status**
```bash
curl -X GET $BASE_URL/properties/boost/status \
  -H "Authorization: Bearer $TOKEN" | jq .
```

### **Push Property to Top**
```bash
curl -X POST $BASE_URL/properties/boost/1/push-top \
  -H "Authorization: Bearer $TOKEN" | jq .
```

### **Get Boost History**
```bash
curl -X GET "$BASE_URL/properties/boost/history?page=0&size=10" \
  -H "Authorization: Bearer $TOKEN" | jq .
```

### **Get Active Boosts**
```bash
curl -X GET $BASE_URL/properties/boost/active \
  -H "Authorization: Bearer $TOKEN" | jq .
```

---

## 🤖 **6. AI CONTENT GENERATION**

### **Generate AI Content**
```bash
curl -X POST $BASE_URL/ai-content/generate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "description": "Căn hộ 2 phòng ngủ tại quận 1, gần chợ Bến Thành, view đẹp",
    "imageDescription": "Phòng khách rộng rãi, view thành phố tuyệt đẹp"
  }' | jq .
```

---

## 🔍 **7. SEARCH & FILTER**

### **Search Properties**
```bash
curl -X GET "$BASE_URL/properties/search?keyword=căn hộ&city=Hồ Chí Minh&minPrice=1000000&maxPrice=10000000000" | jq .
```

### **Filter by Category**
```bash
curl -X GET "$BASE_URL/properties?categoryId=1&page=0&size=10" | jq .
```

---

## 👨‍💼 **8. ADMIN ENDPOINTS**

### **Admin Login**
```bash
curl -X POST $BASE_URL/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }' | jq .

# Save admin token
ADMIN_TOKEN="your_admin_token_here"
```

### **Admin Dashboard**
```bash
curl -X GET $BASE_URL/admin/stats/dashboard \
  -H "Authorization: Bearer $ADMIN_TOKEN" | jq .
```

### **Approve Property**
```bash
curl -X POST $BASE_URL/admin/properties/1/approve \
  -H "Authorization: Bearer $ADMIN_TOKEN" | jq .
```

### **Reject Property**
```bash
curl -X POST $BASE_URL/admin/properties/1/reject \
  -H "Authorization: Bearer $ADMIN_TOKEN" | jq .
```

---

## 🧪 **9. TESTING SCENARIOS**

### **Scenario 1: Complete User Journey**
```bash
# 1. Register
curl -X POST $BASE_URL/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username": "newuser", "email": "<EMAIL>", "password": "pass123", "firstName": "New", "lastName": "User", "phoneNumber": "0987654321"}'

# 2. Login and get token
TOKEN=$(curl -X POST $BASE_URL/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "newuser", "password": "pass123"}' | jq -r '.token')

# 3. Check dashboard (no membership)
curl -X GET $BASE_URL/dashboard -H "Authorization: Bearer $TOKEN"

# 4. Purchase Advanced membership
curl -X POST $BASE_URL/memberships/purchase \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"membershipId": 2, "paymentMethod": "STRIPE"}'

# 5. Create property
curl -X POST $BASE_URL/properties \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"title": "Test Property", "description": "Test description", "price": 2000000000, "propertyArea": 50, "bedrooms": 1, "bathrooms": 1, "address": "Test Address", "city": "Hồ Chí Minh", "district": "Quận 1", "ward": "Phường 1", "categoryId": 1, "propertyType": "APARTMENT", "listingType": "SALE"}'

# 6. Generate AI content
curl -X POST $BASE_URL/ai-content/generate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"description": "Căn hộ mini tiện nghi", "imageDescription": "Phòng sáng sủa"}'

# 7. Push property to top
curl -X POST $BASE_URL/properties/boost/1/push-top \
  -H "Authorization: Bearer $TOKEN"
```

### **Scenario 2: Error Testing**
```bash
# Test without authentication
curl -X GET $BASE_URL/dashboard

# Test AI generation without membership
curl -X POST $BASE_URL/ai-content/generate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"description": "test"}'

# Test push top without membership
curl -X POST $BASE_URL/properties/boost/1/push-top \
  -H "Authorization: Bearer $TOKEN"
```

---

## 📚 **10. API DOCUMENTATION**

### **Swagger UI**
```bash
# Open in browser
open http://localhost:8080/swagger-ui/index.html

# Get API docs JSON
curl -s http://localhost:8080/api-docs | jq .
```

### **Health Check**
```bash
curl -s http://localhost:8080/actuator/health | jq .
```

---

## 🎯 **EXPECTED RESPONSES**

### **Successful Registration**
```json
{
  "token": "eyJhbGciOiJIUzI1NiJ9...",
  "type": "Bearer",
  "username": "testuser2",
  "email": "<EMAIL>",
  "role": "USER",
  "message": "Tài khoản đã được tạo và đăng nhập thành công!"
}
```

### **Dashboard Response**
```json
{
  "properties": {
    "totalProperties": 0,
    "approvedProperties": 0,
    "pendingProperties": 0,
    "rejectedProperties": 0,
    "totalViews": 0,
    "totalContacts": 0,
    "activeBoostedProperties": 0
  },
  "membership": {
    "hasActiveMembership": false,
    "message": "No active membership found"
  },
  "monthlyUsage": {
    "pushTopUsed": 0,
    "aiContentUsed": 0,
    "month": "2025-07",
    "resetDate": [2025, 8, 1]
  },
  "suggestions": [
    {
      "type": "membership_required",
      "title": "Đăng ký gói thành viên",
      "message": "Đăng ký gói thành viên để đăng bài và sử dụng các tính năng cao cấp",
      "action": "upgrade",
      "priority": "high"
    }
  ]
}
```

### **AI Content Generation Response**
```json
{
  "success": true,
  "content": {
    "seoOptimizedDescription": "Căn hộ 2 phòng ngủ cao cấp tại trung tâm Quận 1...",
    "titleSuggestions": ["Căn hộ cao cấp Q1 view đẹp", "Apartment 2PN trung tâm Q1"],
    "metaDescription": "Căn hộ 2PN Q1 gần chợ Bến Thành, view đẹp, giá tốt...",
    "seoScore": 85,
    "keywords": ["căn hộ", "quận 1", "gần chợ", "view đẹp"]
  },
  "usage": {
    "aiContentUsed": 1,
    "month": "2025-07"
  }
}
```

---

## ✅ **TESTING CHECKLIST**

- [ ] User registration and login
- [ ] JWT authentication working
- [ ] Membership system functional
- [ ] Property CRUD operations
- [ ] Push top feature (requires Advanced membership)
- [ ] AI content generation (requires Advanced membership)
- [ ] User dashboard with statistics
- [ ] Admin dashboard and property approval
- [ ] Monthly usage tracking
- [ ] Error handling for insufficient permissions
- [ ] Swagger documentation accessible

---

**🎉 All features implemented and ready for testing!**
