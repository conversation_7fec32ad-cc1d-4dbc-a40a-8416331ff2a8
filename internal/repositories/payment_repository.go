package repositories

import (
	"real-estate-backend/internal/models"

	"gorm.io/gorm"
)

type paymentRepository struct {
	db *gorm.DB
}

func NewPaymentRepository(db interface{}) PaymentRepository {
	return &paymentRepository{
		db: db.(*gorm.DB),
	}
}

func (r *paymentRepository) Create(payment *models.Payment) error {
	return r.db.Create(payment).Error
}

func (r *paymentRepository) GetByID(id uint) (*models.Payment, error) {
	var payment models.Payment
	err := r.db.Preload("User").Preload("Membership").First(&payment, id).Error
	if err != nil {
		return nil, err
	}
	return &payment, nil
}

func (r *paymentRepository) GetByUserID(userID uint) ([]models.Payment, error) {
	var payments []models.Payment
	err := r.db.Preload("User").Preload("Membership").
		Where("user_id = ?", userID).
		Order("created_at DESC").
		Find(&payments).Error
	return payments, err
}

func (r *paymentRepository) GetByStripeSessionID(sessionID string) (*models.Payment, error) {
	var payment models.Payment
	err := r.db.Preload("User").Preload("Membership").
		Where("stripe_session_id = ?", sessionID).
		First(&payment).Error
	if err != nil {
		return nil, err
	}
	return &payment, nil
}

func (r *paymentRepository) Update(payment *models.Payment) error {
	return r.db.Save(payment).Error
}

func (r *paymentRepository) Delete(id uint) error {
	return r.db.Delete(&models.Payment{}, id).Error
}

func (r *paymentRepository) GetTotalRevenue() (float64, error) {
	var total float64
	err := r.db.Model(&models.Payment{}).
		Where("status = ?", "COMPLETED").
		Select("COALESCE(SUM(amount), 0)").
		Scan(&total).Error
	return total, err
}

type propertyImageRepository struct {
	db *gorm.DB
}

func NewPropertyImageRepository(db interface{}) PropertyImageRepository {
	return &propertyImageRepository{
		db: db.(*gorm.DB),
	}
}

func (r *propertyImageRepository) Create(image *models.PropertyImage) error {
	return r.db.Create(image).Error
}

func (r *propertyImageRepository) GetByID(id uint) (*models.PropertyImage, error) {
	var image models.PropertyImage
	err := r.db.First(&image, id).Error
	if err != nil {
		return nil, err
	}
	return &image, nil
}

func (r *propertyImageRepository) GetByPropertyID(propertyID uint) ([]models.PropertyImage, error) {
	var images []models.PropertyImage
	err := r.db.Where("property_id = ?", propertyID).
		Order("is_primary DESC, display_order ASC").
		Find(&images).Error
	return images, err
}

func (r *propertyImageRepository) Update(image *models.PropertyImage) error {
	return r.db.Save(image).Error
}

func (r *propertyImageRepository) Delete(id uint) error {
	return r.db.Delete(&models.PropertyImage{}, id).Error
}

func (r *propertyImageRepository) DeleteByPropertyID(propertyID uint) error {
	return r.db.Where("property_id = ?", propertyID).Delete(&models.PropertyImage{}).Error
}

func (r *propertyImageRepository) SetPrimary(imageID uint, propertyID uint) error {
	// First, unset all primary images for this property
	if err := r.db.Model(&models.PropertyImage{}).
		Where("property_id = ?", propertyID).
		Update("is_primary", false).Error; err != nil {
		return err
	}

	// Then set the specified image as primary
	return r.db.Model(&models.PropertyImage{}).
		Where("id = ? AND property_id = ?", imageID, propertyID).
		Update("is_primary", true).Error
}

type propertyBoostRepository struct {
	db *gorm.DB
}

func NewPropertyBoostRepository(db interface{}) PropertyBoostRepository {
	return &propertyBoostRepository{
		db: db.(*gorm.DB),
	}
}

func (r *propertyBoostRepository) Create(boost *models.PropertyBoost) error {
	return r.db.Create(boost).Error
}

func (r *propertyBoostRepository) GetByID(id uint) (*models.PropertyBoost, error) {
	var boost models.PropertyBoost
	err := r.db.First(&boost, id).Error
	if err != nil {
		return nil, err
	}
	return &boost, nil
}

func (r *propertyBoostRepository) GetByPropertyID(propertyID uint) ([]models.PropertyBoost, error) {
	var boosts []models.PropertyBoost
	err := r.db.Where("property_id = ?", propertyID).
		Order("created_at DESC").
		Find(&boosts).Error
	return boosts, err
}

func (r *propertyBoostRepository) GetActiveByPropertyID(propertyID uint) (*models.PropertyBoost, error) {
	var boost models.PropertyBoost
	err := r.db.Where("property_id = ? AND is_active = ? AND start_time <= NOW() AND end_time > NOW()", 
		propertyID, true).
		First(&boost).Error
	if err != nil {
		return nil, err
	}
	return &boost, nil
}

func (r *propertyBoostRepository) Update(boost *models.PropertyBoost) error {
	return r.db.Save(boost).Error
}

func (r *propertyBoostRepository) Delete(id uint) error {
	return r.db.Delete(&models.PropertyBoost{}, id).Error
}

func (r *propertyBoostRepository) GetActiveBoosts() ([]models.PropertyBoost, error) {
	var boosts []models.PropertyBoost
	err := r.db.Where("is_active = ? AND start_time <= NOW() AND end_time > NOW()", true).
		Find(&boosts).Error
	return boosts, err
}
