package repositories

import (
	"real-estate-backend/internal/models"
	"real-estate-backend/internal/strategies"
)

type UserRepository interface {
	Create(user *models.User) error
	GetByID(id uint) (*models.User, error)
	GetByUsername(username string) (*models.User, error)
	GetByEmail(email string) (*models.User, error)
	Update(user *models.User) error
	Delete(id uint) error
	List(page, size int) ([]models.User, int64, error)
	UpdateStatus(id uint, status models.UserStatus) error
}

type PropertyRepository interface {
	Create(property *models.Property) error
	GetByID(id uint) (*models.Property, error)
	GetByUserID(userID uint, page, size int) ([]models.Property, int64, error)
	Update(property *models.Property) error
	Delete(id uint) error
	List(filters strategies.PropertyFilters) ([]models.Property, int64, error)
	GetPublicProperties(filters strategies.PropertyFilters) ([]models.Property, int64, error)
	GetPendingProperties(page, size int) ([]models.Property, int64, error)
	UpdateStatus(id uint, status models.PropertyStatus) error
	IncrementViewCount(id uint) error
	IncrementContactCount(id uint) error
	Search(query string, filters strategies.PropertyFilters) ([]models.Property, int64, error)
	GetFeatured(limit int) ([]models.Property, error)
	GetSimilar(propertyID uint, limit int) ([]models.Property, error)
	CountByUserID(userID uint) (int64, error)
	CountByStatus(userID uint, status models.PropertyStatus) (int64, error)
}

type CategoryRepository interface {
	Create(category *models.Category) error
	GetByID(id uint) (*models.Category, error)
	List() ([]models.Category, error)
	Update(category *models.Category) error
	Delete(id uint) error
}

type MembershipRepository interface {
	Create(membership *models.Membership) error
	GetByID(id uint) (*models.Membership, error)
	List() ([]models.Membership, error)
	Update(membership *models.Membership) error
	Delete(id uint) error
	GetActiveList() ([]models.Membership, error)
}

type UserMembershipRepository interface {
	Create(userMembership *models.UserMembership) error
	GetByID(id uint) (*models.UserMembership, error)
	GetActiveByUserID(userID uint) (*models.UserMembership, error)
	GetByUserID(userID uint) ([]models.UserMembership, error)
	Update(userMembership *models.UserMembership) error
	Delete(id uint) error
	UpdateUsage(id uint, propertiesUsed, pushTopUsed int) error
}

type PaymentRepository interface {
	Create(payment *models.Payment) error
	GetByID(id uint) (*models.Payment, error)
	GetByUserID(userID uint) ([]models.Payment, error)
	GetByStripeSessionID(sessionID string) (*models.Payment, error)
	Update(payment *models.Payment) error
	Delete(id uint) error
	GetTotalRevenue() (float64, error)
}

type PropertyImageRepository interface {
	Create(image *models.PropertyImage) error
	GetByID(id uint) (*models.PropertyImage, error)
	GetByPropertyID(propertyID uint) ([]models.PropertyImage, error)
	Update(image *models.PropertyImage) error
	Delete(id uint) error
	DeleteByPropertyID(propertyID uint) error
	SetPrimary(imageID uint, propertyID uint) error
}

type PropertyBoostRepository interface {
	Create(boost *models.PropertyBoost) error
	GetByID(id uint) (*models.PropertyBoost, error)
	GetByPropertyID(propertyID uint) ([]models.PropertyBoost, error)
	GetActiveByPropertyID(propertyID uint) (*models.PropertyBoost, error)
	Update(boost *models.PropertyBoost) error
	Delete(id uint) error
	GetActiveBoosts() ([]models.PropertyBoost, error)
}

type Repositories struct {
	User             UserRepository
	Property         PropertyRepository
	Category         CategoryRepository
	Membership       MembershipRepository
	UserMembership   UserMembershipRepository
	Payment          PaymentRepository
	PropertyImage    PropertyImageRepository
	PropertyBoost    PropertyBoostRepository
}

func NewRepositories(db interface{}) *Repositories {
	// This will be implemented with actual GORM repositories
	return &Repositories{
		User:           NewUserRepository(db),
		Property:       NewPropertyRepository(db),
		Category:       NewCategoryRepository(db),
		Membership:     NewMembershipRepository(db),
		UserMembership: NewUserMembershipRepository(db),
		Payment:        NewPaymentRepository(db),
		PropertyImage:  NewPropertyImageRepository(db),
		PropertyBoost:  NewPropertyBoostRepository(db),
	}
}
