package database

import (
	"fmt"
	"real-estate-backend/internal/config"
	"real-estate-backend/internal/models"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func Initialize(cfg config.DatabaseConfig) (*gorm.DB, error) {
	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=%s",
		cfg.Host, cfg.User, cfg.Password, cfg.DBName, cfg.Port, cfg.SSLMode)

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	return db, nil
}

func RunMigrations(db *gorm.DB) error {
	err := db.AutoMigrate(
		&models.User{},
		&models.Category{},
		&models.Membership{},
		&models.UserMembership{},
		&models.Property{},
		&models.PropertyImage{},
		&models.PropertyBoost{},
		&models.Payment{},
	)
	if err != nil {
		return fmt.Errorf("failed to run migrations: %w", err)
	}

	// Create indexes
	if err := createIndexes(db); err != nil {
		return fmt.Errorf("failed to create indexes: %w", err)
	}

	// Seed default data
	if err := seedDefaultData(db); err != nil {
		return fmt.Errorf("failed to seed default data: %w", err)
	}

	return nil
}

func createIndexes(db *gorm.DB) error {
	// User indexes
	db.Exec("CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)")

	// Property indexes
	db.Exec("CREATE INDEX IF NOT EXISTS idx_properties_user_id ON properties(user_id)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_properties_status ON properties(status)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_properties_city_district ON properties(city, district)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_properties_price ON properties(price)")

	// User membership indexes
	db.Exec("CREATE INDEX IF NOT EXISTS idx_user_memberships_user_id ON user_memberships(user_id)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_user_memberships_status ON user_memberships(status)")

	// Payment indexes
	db.Exec("CREATE INDEX IF NOT EXISTS idx_payments_user_id ON payments(user_id)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status)")

	// Property boost indexes
	db.Exec("CREATE INDEX IF NOT EXISTS idx_property_boosts_property_id ON property_boosts(property_id)")
	db.Exec("CREATE INDEX IF NOT EXISTS idx_property_boosts_active ON property_boosts(is_active, start_time, end_time)")

	return nil
}

func seedDefaultData(db *gorm.DB) error {
	// Seed categories
	categories := []models.Category{
		{Name: "Apartment", Description: stringPtr("Apartment and condominium listings"), Icon: stringPtr("apartment")},
		{Name: "House", Description: stringPtr("Single-family houses and townhouses"), Icon: stringPtr("house")},
		{Name: "Villa", Description: stringPtr("Luxury villas and mansions"), Icon: stringPtr("villa")},
		{Name: "Land", Description: stringPtr("Land plots and development sites"), Icon: stringPtr("land")},
		{Name: "Office", Description: stringPtr("Commercial office spaces"), Icon: stringPtr("office")},
		{Name: "Shop", Description: stringPtr("Retail and commercial shops"), Icon: stringPtr("shop")},
	}

	for _, category := range categories {
		var existingCategory models.Category
		if err := db.Where("name = ?", category.Name).First(&existingCategory).Error; err == gorm.ErrRecordNotFound {
			if err := db.Create(&category).Error; err != nil {
				return err
			}
		}
	}

	// Seed memberships
	memberships := []models.Membership{
		{
			Name:                "FREE",
			Description:         stringPtr("Free membership for new users"),
			Price:               0.00,
			DurationMonths:      1,
			MaxProperties:       10,
			AIContentGeneration: false,
			PushTopLimit:        0,
			Type:                models.MembershipTypeBasic,
		},
		{
			Name:                "BASIC",
			Description:         stringPtr("Basic membership with standard features"),
			Price:               99.00,
			DurationMonths:      1,
			MaxProperties:       10,
			AIContentGeneration: false,
			PushTopLimit:        0,
			Type:                models.MembershipTypeBasic,
		},
		{
			Name:                "PREMIUM",
			Description:         stringPtr("Premium membership with AI and advanced features"),
			Price:               299.00,
			DurationMonths:      1,
			MaxProperties:       50,
			AIContentGeneration: true,
			PushTopLimit:        10,
			Type:                models.MembershipTypePremium,
		},
	}

	for _, membership := range memberships {
		var existingMembership models.Membership
		if err := db.Where("name = ?", membership.Name).First(&existingMembership).Error; err == gorm.ErrRecordNotFound {
			if err := db.Create(&membership).Error; err != nil {
				return err
			}
		}
	}

	return nil
}

func stringPtr(s string) *string {
	return &s
}
