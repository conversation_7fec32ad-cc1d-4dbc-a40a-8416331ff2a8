package strategies

import (
	"errors"
	"fmt"
	"real-estate-backend/internal/config"
	"real-estate-backend/internal/models"
	"real-estate-backend/internal/repositories"
	"time"

	"gorm.io/gorm"
)

type adminStrategy struct {
	propertyRepo repositories.PropertyRepository
	userRepo     repositories.UserRepository
	paymentRepo  repositories.PaymentRepository
}

func NewAdminStrategy(repos *repositories.Repositories, cfg *config.Config) AdminStrategy {
	return &adminStrategy{
		propertyRepo: repos.Property,
		userRepo:     repos.User,
		paymentRepo:  repos.Payment,
	}
}

func (s *adminStrategy) GetPendingProperties(page, size int) (*PropertyListResponse, error) {
	properties, total, err := s.propertyRepo.GetPendingProperties(page, size)
	if err != nil {
		return nil, fmt.Errorf("failed to get pending properties: %w", err)
	}

	// Convert to response format
	responses := make([]models.PropertyResponse, len(properties))
	for i, property := range properties {
		responses[i] = *property.ToResponse()
	}

	totalPages := int((total + int64(size) - 1) / int64(size))

	return &PropertyListResponse{
		Content: responses,
		Pageable: PageInfo{
			PageNumber: page,
			PageSize:   size,
		},
		TotalElements: total,
		TotalPages:    totalPages,
	}, nil
}

func (s *adminStrategy) ApproveProperty(id uint, isFeatured bool, adminNote string) error {
	property, err := s.propertyRepo.GetByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("property not found")
		}
		return fmt.Errorf("failed to get property: %w", err)
	}

	property.Status = models.PropertyStatusApproved
	property.IsFeatured = isFeatured
	property.PublishedAt = &time.Time{}
	*property.PublishedAt = time.Now()

	return s.propertyRepo.Update(property)
}

func (s *adminStrategy) RejectProperty(id uint, reason string) error {
	property, err := s.propertyRepo.GetByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("property not found")
		}
		return fmt.Errorf("failed to get property: %w", err)
	}

	property.Status = models.PropertyStatusRejected
	return s.propertyRepo.Update(property)
}

func (s *adminStrategy) GetUsers(page, size int) (*UserListResponse, error) {
	users, total, err := s.userRepo.List(page, size)
	if err != nil {
		return nil, fmt.Errorf("failed to get users: %w", err)
	}

	// Convert to profile format
	profiles := make([]models.UserProfile, len(users))
	for i, user := range users {
		profiles[i] = *user.ToProfile()
	}

	totalPages := int((total + int64(size) - 1) / int64(size))

	return &UserListResponse{
		Content: profiles,
		Pageable: PageInfo{
			PageNumber: page,
			PageSize:   size,
		},
		TotalElements: total,
		TotalPages:    totalPages,
	}, nil
}

func (s *adminStrategy) BanUser(userID uint) error {
	return s.userRepo.UpdateStatus(userID, models.UserStatusBanned)
}

func (s *adminStrategy) UnbanUser(userID uint) error {
	return s.userRepo.UpdateStatus(userID, models.UserStatusActive)
}

func (s *adminStrategy) GetDashboardStats() (*AdminDashboardStats, error) {
	// Get total users
	users, totalUsers, err := s.userRepo.List(0, 1)
	if err != nil {
		return nil, fmt.Errorf("failed to get user count: %w", err)
	}

	// Get total properties
	filters := PropertyFilters{Page: 0, Size: 1}
	_, totalProperties, err := s.propertyRepo.List(filters)
	if err != nil {
		return nil, fmt.Errorf("failed to get property count: %w", err)
	}

	// Get pending properties
	_, pendingCount, err := s.propertyRepo.GetPendingProperties(0, 1)
	if err != nil {
		return nil, fmt.Errorf("failed to get pending count: %w", err)
	}

	// Get total revenue
	totalRevenue, err := s.paymentRepo.GetTotalRevenue()
	if err != nil {
		return nil, fmt.Errorf("failed to get total revenue: %w", err)
	}

	return &AdminDashboardStats{
		Overview: OverviewStats{
			TotalUsers:       int(totalUsers),
			TotalProperties:  int(totalProperties),
			PendingApprovals: int(pendingCount),
			TotalRevenue:     totalRevenue,
		},
		Users: UserStats{
			TotalUsers:    int(totalUsers),
			ActiveUsers:   int(totalUsers), // Simplified
			NewUsersToday: 0,               // Simplified
		},
		Properties: PropertyStats{
			TotalProperties:    int(totalProperties),
			ApprovedProperties: 0, // Simplified
			PendingProperties:  int(pendingCount),
			RejectedProperties: 0, // Simplified
		},
	}, nil
}
