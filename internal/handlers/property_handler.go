package handlers

import (
	"net/http"
	"real-estate-backend/internal/models"
	"real-estate-backend/internal/services"
	"real-estate-backend/internal/strategies"
	"strconv"

	"github.com/gin-gonic/gin"
)

type PropertyHandler struct {
	services *services.Services
}

func NewPropertyHandler(services *services.Services) *PropertyHandler {
	return &PropertyHandler{
		services: services,
	}
}

func (h *PropertyHandler) GetPublicProperties(c *gin.Context) {
	// Parse query parameters
	page, _ := strconv.Atoi(c.<PERSON>("page", "0"))
	size, _ := strconv.Atoi(c.Default<PERSON>y("size", "10"))
	sortBy := c.Default<PERSON>y("sortBy", "created_at")
	sortDir := c.<PERSON>ult<PERSON>("sortDir", "desc")

	filters := strategies.PropertyFilters{
		City:         c.Query("city"),
		District:     c.Query("district"),
		PropertyType: models.PropertyType(c.Query("propertyType")),
		ListingType:  models.ListingType(c.Query("listingType")),
		Page:         page,
		Size:         size,
		SortBy:       sortBy,
		SortDir:      sortDir,
	}

	// Parse price filters
	if minPrice := c.Query("minPrice"); minPrice != "" {
		if price, err := strconv.ParseFloat(minPrice, 64); err == nil {
			filters.MinPrice = &price
		}
	}
	if maxPrice := c.Query("maxPrice"); maxPrice != "" {
		if price, err := strconv.ParseFloat(maxPrice, 64); err == nil {
			filters.MaxPrice = &price
		}
	}

	// Parse bedrooms filter
	if bedrooms := c.Query("bedrooms"); bedrooms != "" {
		if br, err := strconv.Atoi(bedrooms); err == nil {
			filters.Bedrooms = &br
		}
	}

	// Parse category filter
	if categoryID := c.Query("categoryId"); categoryID != "" {
		if catID, err := strconv.ParseUint(categoryID, 10, 32); err == nil {
			catIDUint := uint(catID)
			filters.CategoryID = &catIDUint
		}
	}

	response, err := h.services.Property.GetProperties(filters)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Internal Server Error",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

func (h *PropertyHandler) GetPropertyByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Bad Request",
			"message": "Invalid property ID",
		})
		return
	}

	property, err := h.services.Property.GetPropertyByID(uint(id))
	if err != nil {
		if err.Error() == "property not found" {
			c.JSON(http.StatusNotFound, gin.H{
				"error":   "Not Found",
				"message": "Property not found",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Internal Server Error",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, property.ToResponse())
}

func (h *PropertyHandler) CreateProperty(c *gin.Context) {
	// Set auth strategy in context for middleware
	c.Set("authStrategy", h.services.Auth)

	var req models.CreatePropertyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Bad Request",
			"message": "Invalid request data",
			"details": err.Error(),
		})
		return
	}

	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "Unauthorized",
			"message": "User ID not found",
		})
		return
	}

	property, err := h.services.Property.CreateProperty(userID.(uint), &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Bad Request",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"id":         property.ID,
		"title":      property.Title,
		"status":     property.Status,
		"created_at": property.CreatedAt,
		"owner_name": property.User.FirstName + " " + property.User.LastName,
	})
}

func (h *PropertyHandler) UpdateProperty(c *gin.Context) {
	// Set auth strategy in context for middleware
	c.Set("authStrategy", h.services.Auth)

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Bad Request",
			"message": "Invalid property ID",
		})
		return
	}

	var req models.CreatePropertyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Bad Request",
			"message": "Invalid request data",
			"details": err.Error(),
		})
		return
	}

	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "Unauthorized",
			"message": "User ID not found",
		})
		return
	}

	property, err := h.services.Property.UpdateProperty(uint(id), userID.(uint), &req)
	if err != nil {
		if err.Error() == "property not found" || err.Error() == "unauthorized to update this property" {
			c.JSON(http.StatusNotFound, gin.H{
				"error":   "Not Found",
				"message": err.Error(),
			})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Bad Request",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, property.ToResponse())
}

func (h *PropertyHandler) DeleteProperty(c *gin.Context) {
	// Set auth strategy in context for middleware
	c.Set("authStrategy", h.services.Auth)

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Bad Request",
			"message": "Invalid property ID",
		})
		return
	}

	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "Unauthorized",
			"message": "User ID not found",
		})
		return
	}

	err = h.services.Property.DeleteProperty(uint(id), userID.(uint))
	if err != nil {
		if err.Error() == "property not found" || err.Error() == "unauthorized to delete this property" {
			c.JSON(http.StatusNotFound, gin.H{
				"error":   "Not Found",
				"message": err.Error(),
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Internal Server Error",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Property deleted successfully",
	})
}

func (h *PropertyHandler) GetUserProperties(c *gin.Context) {
	// Set auth strategy in context for middleware
	c.Set("authStrategy", h.services.Auth)

	page, _ := strconv.Atoi(c.DefaultQuery("page", "0"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "10"))

	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "Unauthorized",
			"message": "User ID not found",
		})
		return
	}

	response, err := h.services.Property.GetUserProperties(userID.(uint), page, size)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Internal Server Error",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

func (h *PropertyHandler) UploadImages(c *gin.Context) {
	// Set auth strategy in context for middleware
	c.Set("authStrategy", h.services.Auth)

	// Simplified implementation
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Image upload functionality not implemented yet",
		"images":  []interface{}{},
	})
}
