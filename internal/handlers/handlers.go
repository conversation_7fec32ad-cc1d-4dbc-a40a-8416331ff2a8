package handlers

import (
	"real-estate-backend/internal/services"
)

type Handlers struct {
	Auth       *AuthHandler
	Property   *PropertyHandler
	Admin      *AdminHandler
	Payment    *PaymentHandler
	Membership *MembershipHandler
	Category   *CategoryHandler
	Dashboard  *DashboardHandler
	Chatbot    *ChatbotHandler
}

func NewHandlers(services *services.Services) *Handlers {
	return &Handlers{
		Auth:       NewAuthHandler(services),
		Property:   NewPropertyHandler(services),
		Admin:      NewAdminHandler(services),
		Payment:    NewPaymentHandler(services),
		Membership: NewMembershipHandler(services),
		Category:   NewCategoryHandler(services),
		Dashboard:  NewDashboardHandler(services),
		Chatbot:    NewChatbotHandler(services),
	}
}
