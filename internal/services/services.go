package services

import (
	"real-estate-backend/internal/config"
	"real-estate-backend/internal/repositories"
	"real-estate-backend/internal/strategies"
)

type Services struct {
	Auth       strategies.AuthStrategy
	Property   strategies.PropertyStrategy
	Admin      strategies.AdminStrategy
	Payment    strategies.PaymentStrategy
	Membership strategies.MembershipStrategy
	Search     strategies.SearchStrategy
	Chatbot    strategies.ChatbotStrategy
}

func NewServices(repos *repositories.Repositories, cfg *config.Config) *Services {
	return &Services{
		Auth:       strategies.NewAuthStrategy(repos, cfg),
		Property:   strategies.NewPropertyStrategy(repos, cfg),
		Admin:      strategies.NewAdminStrategy(repos, cfg),
		Payment:    strategies.NewPaymentStrategy(repos, cfg),
		Membership: strategies.NewMembershipStrategy(repos, cfg),
		Search:     strategies.NewSearchStrategy(repos, cfg),
		Chatbot:    strategies.NewChatbotStrategy(repos, cfg),
	}
}
