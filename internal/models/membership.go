package models

import (
	"time"

	"gorm.io/gorm"
)

type MembershipType string
type MembershipStatus string

const (
	MembershipTypeBasic   MembershipType = "BASIC"
	MembershipTypePremium MembershipType = "PREMIUM"

	MembershipStatusActive    MembershipStatus = "ACTIVE"
	MembershipStatusExpired   MembershipStatus = "EXPIRED"
	MembershipStatusCancelled MembershipStatus = "CANCELLED"
	MembershipStatusSuspended MembershipStatus = "SUSPENDED"
)

type Membership struct {
	ID                   uint           `json:"id" gorm:"primaryKey"`
	Name                 string         `json:"name" gorm:"not null"`
	Description          *string        `json:"description"`
	Price                float64        `json:"price" gorm:"not null"`
	DurationMonths       int            `json:"duration_months" gorm:"not null"`
	MaxProperties        int            `json:"max_properties" gorm:"not null"`
	FeaturedProperties   int            `json:"featured_properties" gorm:"default:0"`
	PrioritySupport      bool           `json:"priority_support" gorm:"default:false"`
	AnalyticsAccess      bool           `json:"analytics_access" gorm:"default:false"`
	MultipleImages       bool           `json:"multiple_images" gorm:"default:false"`
	ContactInfoDisplay   bool           `json:"contact_info_display" gorm:"default:true"`
	AIContentGeneration  bool           `json:"ai_content_generation" gorm:"default:false"`
	PushTopLimit         int            `json:"push_top_limit" gorm:"default:0"`
	Type                 MembershipType `json:"type" gorm:"default:BASIC"`
	IsActive             bool           `json:"is_active" gorm:"default:true"`
	CreatedAt            time.Time      `json:"created_at"`
	UpdatedAt            time.Time      `json:"updated_at"`
	DeletedAt            gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	UserMemberships []UserMembership `json:"user_memberships,omitempty" gorm:"foreignKey:MembershipID"`
	Payments        []Payment        `json:"payments,omitempty" gorm:"foreignKey:MembershipID"`
}

type UserMembership struct {
	ID           uint             `json:"id" gorm:"primaryKey"`
	UserID       uint             `json:"user_id" gorm:"not null"`
	MembershipID uint             `json:"membership_id" gorm:"not null"`
	StartDate    time.Time        `json:"start_date" gorm:"not null"`
	EndDate      time.Time        `json:"end_date" gorm:"not null"`
	Status       MembershipStatus `json:"status" gorm:"default:ACTIVE"`
	AutoRenewal  bool             `json:"auto_renewal" gorm:"default:false"`
	PropertiesUsed int            `json:"properties_used" gorm:"default:0"`
	PushTopUsed    int            `json:"push_top_used" gorm:"default:0"`
	CreatedAt    time.Time        `json:"created_at"`
	UpdatedAt    time.Time        `json:"updated_at"`
	DeletedAt    gorm.DeletedAt   `json:"-" gorm:"index"`

	// Relationships
	User       User       `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Membership Membership `json:"membership,omitempty" gorm:"foreignKey:MembershipID"`
}

type Category struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	Name        string         `json:"name" gorm:"not null"`
	Description *string        `json:"description"`
	Icon        *string        `json:"icon"`
	IsActive    bool           `json:"is_active" gorm:"default:true"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	Properties []Property `json:"properties,omitempty" gorm:"foreignKey:CategoryID"`
}

type Payment struct {
	ID                     uint      `json:"id" gorm:"primaryKey"`
	UserID                 uint      `json:"user_id" gorm:"not null"`
	MembershipID           uint      `json:"membership_id" gorm:"not null"`
	StripePaymentIntentID  *string   `json:"stripe_payment_intent_id"`
	StripeSessionID        *string   `json:"stripe_session_id"`
	Amount                 float64   `json:"amount" gorm:"not null"`
	Currency               string    `json:"currency" gorm:"default:USD"`
	Status                 string    `json:"status" gorm:"default:PENDING"`
	PaymentMethod          *string   `json:"payment_method"`
	CreatedAt              time.Time `json:"created_at"`
	UpdatedAt              time.Time `json:"updated_at"`

	// Relationships
	User       User       `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Membership Membership `json:"membership,omitempty" gorm:"foreignKey:MembershipID"`
}

type DashboardResponse struct {
	Membership   MembershipInfo `json:"membership"`
	Properties   PropertyStats  `json:"properties"`
	MonthlyUsage MonthlyUsage   `json:"monthlyUsage"`
}

type MembershipInfo struct {
	PlanName             string `json:"planName"`
	PlanType             string `json:"planType"`
	MaxProperties        int    `json:"maxProperties"`
	PropertiesUsed       int    `json:"propertiesUsed"`
	PropertiesRemaining  int    `json:"propertiesRemaining"`
	HasAiGeneration      bool   `json:"hasAiGeneration"`
	PushTopLimit         int    `json:"pushTopLimit"`
	DaysRemaining        int    `json:"daysRemaining"`
	HasActiveMembership  bool   `json:"hasActiveMembership"`
}

type PropertyStats struct {
	TotalProperties    int `json:"totalProperties"`
	ApprovedProperties int `json:"approvedProperties"`
	PendingProperties  int `json:"pendingProperties"`
	RejectedProperties int `json:"rejectedProperties"`
	TotalViews         int `json:"totalViews"`
	TotalContacts      int `json:"totalContacts"`
}

type MonthlyUsage struct {
	Month         string `json:"month"`
	PushTopUsed   int    `json:"pushTopUsed"`
	PushTopLimit  int    `json:"pushTopLimit"`
	AIContentUsed int    `json:"aiContentUsed"`
}
