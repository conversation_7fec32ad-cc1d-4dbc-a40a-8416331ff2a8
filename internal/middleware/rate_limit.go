package middleware

import (
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
)

type RateLimiter struct {
	visitors map[string]*Visitor
	mu       sync.RWMutex
}

type Visitor struct {
	limiter  *TokenBucket
	lastSeen time.Time
}

type TokenBucket struct {
	tokens   int
	capacity int
	refill   time.Duration
	lastRefill time.Time
	mu       sync.Mutex
}

func NewTokenBucket(capacity int, refillDuration time.Duration) *TokenBucket {
	return &TokenBucket{
		tokens:     capacity,
		capacity:   capacity,
		refill:     refillDuration,
		lastRefill: time.Now(),
	}
}

func (tb *TokenBucket) Allow() bool {
	tb.mu.Lock()
	defer tb.mu.Unlock()

	now := time.Now()
	elapsed := now.Sub(tb.lastRefill)
	
	// Refill tokens based on elapsed time
	if elapsed >= tb.refill {
		tokensToAdd := int(elapsed / tb.refill)
		tb.tokens = min(tb.capacity, tb.tokens+tokensToAdd)
		tb.lastRefill = now
	}

	if tb.tokens > 0 {
		tb.tokens--
		return true
	}

	return false
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

var rateLimiter = &RateLimiter{
	visitors: make(map[string]*Visitor),
}

func RateLimit() gin.HandlerFunc {
	return func(c *gin.Context) {
		ip := c.ClientIP()
		
		rateLimiter.mu.Lock()
		visitor, exists := rateLimiter.visitors[ip]
		if !exists {
			visitor = &Visitor{
				limiter:  NewTokenBucket(100, time.Minute), // 100 requests per minute
				lastSeen: time.Now(),
			}
			rateLimiter.visitors[ip] = visitor
		}
		visitor.lastSeen = time.Now()
		rateLimiter.mu.Unlock()

		if !visitor.limiter.Allow() {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error":   "Too Many Requests",
				"message": "Rate limit exceeded",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// Cleanup old visitors periodically
func init() {
	go func() {
		ticker := time.NewTicker(time.Hour)
		defer ticker.Stop()
		
		for range ticker.C {
			rateLimiter.mu.Lock()
			now := time.Now()
			for ip, visitor := range rateLimiter.visitors {
				if now.Sub(visitor.lastSeen) > time.Hour {
					delete(rateLimiter.visitors, ip)
				}
			}
			rateLimiter.mu.Unlock()
		}
	}()
}
