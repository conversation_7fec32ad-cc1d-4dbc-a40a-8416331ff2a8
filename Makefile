.PHONY: build run dev test clean docker-build docker-run

# Build the application
build:
	go build -o bin/real-estate-backend cmd/main.go

# Run the application
run: build
	./bin/real-estate-backend

# Run in development mode with hot reload (requires air)
dev:
	air

# Run tests
test:
	go test -v ./...

# Clean build artifacts
clean:
	rm -rf bin/

# Install dependencies
deps:
	go mod download
	go mod tidy

# Format code
fmt:
	go fmt ./...

# Lint code (requires golangci-lint)
lint:
	golangci-lint run

# Install development tools
install-tools:
	go install github.com/cosmtrek/air@latest
	go install github.com/golangci-lint/golangci-lint/cmd/golangci-lint@latest

# Database operations
db-create:
	createdb real_estate

db-drop:
	dropdb real_estate

# Docker operations
docker-build:
	docker build -t real-estate-backend .

docker-run:
	docker run -p 8080:8080 --env-file .env real-estate-backend

# Help
help:
	@echo "Available commands:"
	@echo "  build         - Build the application"
	@echo "  run           - Build and run the application"
	@echo "  dev           - Run in development mode with hot reload"
	@echo "  test          - Run tests"
	@echo "  clean         - Clean build artifacts"
	@echo "  deps          - Install dependencies"
	@echo "  fmt           - Format code"
	@echo "  lint          - Lint code"
	@echo "  install-tools - Install development tools"
	@echo "  db-create     - Create database"
	@echo "  db-drop       - Drop database"
	@echo "  docker-build  - Build Docker image"
	@echo "  docker-run    - Run Docker container"
	@echo "  help          - Show this help message"
