#!/bin/bash

echo "🔧 Setting up admin user for testing..."

# Admin credentials
ADMIN_USERNAME="admin"
ADMIN_PASSWORD="admin123"

echo "👤 Creating admin user..."
REGISTER_RESPONSE=$(curl -s -X POST "http://localhost:8080/api/v1/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "'$ADMIN_USERNAME'",
    "password": "'$ADMIN_PASSWORD'",
    "email": "<EMAIL>",
    "fullName": "System Administrator",
    "phone": "0123456789"
  }')

echo "Register response: $REGISTER_RESPONSE"

echo "🔑 Logging in as admin..."
LOGIN_RESPONSE=$(curl -s -X POST "http://localhost:8080/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "'$ADMIN_USERNAME'",
    "password": "'$ADMIN_PASSWORD'"
  }')

echo "Login response: $LOGIN_RESPONSE"

# Extract token
TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"token":"[^"]*' | cut -d'"' -f4)

if [ -n "$TOKEN" ]; then
  echo "✅ Admin login successful! Token: ${TOKEN:0:20}..."
  
  # Test admin endpoints
  echo "🧪 Testing admin endpoints..."
  
  echo "📊 Getting admin stats..."
  STATS_RESPONSE=$(curl -s -X GET "http://localhost:8080/api/v1/admin/stats" \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json")
  
  echo "Stats response: $STATS_RESPONSE"
  
  echo "📋 Getting properties for review..."
  PROPERTIES_RESPONSE=$(curl -s -X GET "http://localhost:8080/api/v1/admin/properties" \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json")
  
  echo "Properties response: $PROPERTIES_RESPONSE"
  
  echo "👥 Getting users list..."
  USERS_RESPONSE=$(curl -s -X GET "http://localhost:8080/api/v1/admin/users" \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json")
  
  echo "Users response: $USERS_RESPONSE"
  
  echo "💳 Getting payments list..."
  PAYMENTS_RESPONSE=$(curl -s -X GET "http://localhost:8080/api/v1/admin/payments" \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json")
  
  echo "Payments response: $PAYMENTS_RESPONSE"
  
else
  echo "❌ Admin login failed"
fi

echo ""
echo "🎯 Admin Panel Test Instructions:"
echo "1. Open Flutter app"
echo "2. Login with admin/admin123"
echo "3. Go to Profile → Should see 'ADMIN' badge"
echo "4. Tap 'Admin Dashboard' → Access admin features"
echo "5. Test admin functions:"
echo "   - Duyệt bài đăng (Property Review)"
echo "   - Quản lý người dùng (User Management)"
echo "   - Quản lý thanh toán (Payment Management)"

echo ""
echo "🔗 Admin API Endpoints:"
echo "- Stats: GET /api/v1/admin/stats"
echo "- Properties: GET /api/v1/admin/properties"
echo "- Users: GET /api/v1/admin/users"
echo "- Payments: GET /api/v1/admin/payments"
echo "- Review Property: POST /api/v1/admin/properties/{id}/review"
echo "- Manage User: POST /api/v1/admin/users/{id}/manage"

echo ""
echo "🎊 Admin Panel is ready for testing!"
