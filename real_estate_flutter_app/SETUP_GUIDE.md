# 🚀 Real Estate Flutter App - Setup Guide

## Quick Start

### 1. Prerequisites Check
Make sure you have:
- ✅ Flutter SDK 3.8+ installed
- ✅ Backend API running on localhost:8080
- ✅ Android Studio or VS Code with Flutter extensions

### 2. Installation Steps

```bash
# Navigate to the Flutter app directory
cd /Volumes/0xbojissd/school/exam/real_estate_flutter_app

# Install dependencies
flutter pub get

# Generate JSON serialization code
flutter packages pub run build_runner build

# Test API connection
dart test_api.dart

# Run the app
flutter run
```

### 3. Test the Integration

1. **Start your backend server** on localhost:8080
2. **Run the API test** to verify connection:
   ```bash
   dart test_api.dart
   ```
   You should see: `✅ API connection successful!`

3. **Launch the Flutter app**:
   ```bash
   flutter run
   ```

4. **Test with demo accounts**:
   - **Admin**: username: `admin`, password: `admin123`
   - **User**: username: `testuser2`, password: `password123`

## 📱 App Features Overview

### 🔐 Authentication
- Login/Register screens with form validation
- JWT token storage and automatic authentication
- Role-based access (Admin/User)

### 🏠 Property Management
- **Browse Properties**: Paginated list with search functionality
- **Property Details**: Full property information with image gallery
- **Create Property**: Form to add new property listings
- **My Properties**: Manage user's own property listings

### 👤 User Features
- **Profile Management**: View and edit user information
- **Notifications**: System notifications and alerts
- **Navigation**: Bottom tab navigation with 4 main sections

### 🎨 UI/UX
- Material Design 3 with green theme for real estate
- Vietnamese language support
- Responsive design for different screen sizes
- Loading states and error handling
- Toast notifications for user feedback

## 🔧 API Integration Details

The app integrates with your backend API using these endpoints:

### Public Endpoints (No Authentication)
- `GET /api/v1/properties` - Get all properties
- `GET /api/v1/categories` - Get property categories
- `GET /api/v1/memberships` - Get membership plans

### Authentication Endpoints
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/register` - User registration

### Authenticated Endpoints (Requires JWT Token)
- `GET /api/v1/users/profile` - Get user profile
- `GET /api/v1/properties/my-properties` - Get user's properties
- `POST /api/v1/properties` - Create new property
- `GET /api/v1/notifications` - Get user notifications

## 🛠️ Technical Architecture

### State Management
- **Provider Pattern**: Used for state management across the app
- **AuthProvider**: Manages authentication state and user data
- **PropertyProvider**: Manages property data and operations

### Data Models
- **User**: User authentication and profile data
- **Property**: Property information with JSON serialization
- **Category**: Property categories
- **Membership**: Membership plans and user memberships
- **Notification**: System notifications

### Services
- **ApiService**: HTTP client for API communication
- **AuthService**: Authentication and token management

## 📂 Project Structure

```
lib/
├── constants/          # API constants and configuration
├── models/            # Data models with JSON serialization
├── services/          # API and authentication services
├── providers/         # State management providers
├── screens/           # UI screens organized by feature
├── widgets/           # Reusable UI components
└── main.dart         # App entry point
```

## 🔍 Testing the App

### 1. Authentication Flow
1. Launch app → Splash screen (2 seconds)
2. Login screen appears
3. Use demo credentials to login
4. Navigate to home screen with bottom tabs

### 2. Property Features
1. **Browse**: View properties on home screen and search tab
2. **Details**: Tap any property to view full details
3. **Create**: Use "+" button to create new property (requires login)
4. **Manage**: View "My Properties" tab to see user's listings

### 3. User Profile
1. Navigate to Profile tab
2. View user information and menu options
3. Test logout functionality

## 🐛 Common Issues & Solutions

### API Connection Issues
```bash
# Test API connection
dart test_api.dart

# If failed, check:
# 1. Backend server is running on localhost:8080
# 2. No firewall blocking the connection
# 3. API endpoints are accessible
```

### Build Issues
```bash
# Clean and rebuild
flutter clean
flutter pub get
flutter packages pub run build_runner build --delete-conflicting-outputs
```

### Authentication Issues
- Clear app data and try demo accounts
- Check if JWT tokens are being stored correctly
- Verify API responses in debug mode

## 🎯 Next Steps

After successful setup, you can:

1. **Customize the UI**: Modify colors, fonts, and layouts
2. **Add Features**: Implement additional screens and functionality
3. **Enhance API Integration**: Add more endpoints and features
4. **Deploy**: Build for production and deploy to app stores

## 📞 Support

If you encounter any issues:

1. Check the console output for error messages
2. Verify API connectivity with the test script
3. Ensure all dependencies are properly installed
4. Review the README.md for detailed documentation

---

**Success Indicators:**
- ✅ API test returns successful connection
- ✅ App launches without errors
- ✅ Login works with demo accounts
- ✅ Properties load and display correctly
- ✅ Navigation between screens works smoothly

The Flutter app is now ready to use with your backend API! 🎉
