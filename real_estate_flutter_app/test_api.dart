import 'dart:convert';
import 'package:http/http.dart' as http;

void main() async {
  print('Testing API connection to localhost:8080...');
  
  try {
    // Test public endpoint
    final response = await http.get(
      Uri.parse('http://localhost:8080/api/v1/properties'),
      headers: {'Content-Type': 'application/json'},
    );
    
    print('Status Code: ${response.statusCode}');
    print('Response Body: ${response.body}');
    
    if (response.statusCode == 200) {
      print('✅ API connection successful!');
      final data = jsonDecode(response.body);
      if (data is List) {
        print('Found ${data.length} properties');
      }
    } else {
      print('❌ API connection failed with status: ${response.statusCode}');
    }
  } catch (e) {
    print('❌ Error connecting to API: $e');
    print('Make sure your backend is running on localhost:8080');
  }
}
