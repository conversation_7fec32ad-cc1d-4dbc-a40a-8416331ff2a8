import 'package:flutter/material.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import '../models/payment.dart' as payment_models;

class StripeService {
  static final StripeService _instance = StripeService._internal();
  factory StripeService() => _instance;
  StripeService._internal();

  bool _isInitialized = false;

  Future<void> initialize(String publishableKey) async {
    if (_isInitialized) return;

    try {
      Stripe.publishableKey = publishableKey;
      await Stripe.instance.applySettings();
      _isInitialized = true;
    } catch (e) {
      throw Exception('Failed to initialize Stripe: $e');
    }
  }

  Future<bool> processPayment({
    required String clientSecret,
    required BuildContext context,
    String? customerId,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      // Create payment intent
      await Stripe.instance.initPaymentSheet(
        paymentSheetParameters: SetupPaymentSheetParameters(
          paymentIntentClientSecret: clientSecret,
          merchantDisplayName: 'Real Estate App',
          customerId: customerId,
          style: ThemeMode.system,
          billingDetails: const BillingDetails(
            name: 'Real Estate Customer',
            email: '<EMAIL>',
          ),
        ),
      );

      // Present payment sheet
      await Stripe.instance.presentPaymentSheet();
      
      return true;
    } on StripeException catch (e) {
      debugPrint('Stripe error: ${e.error.localizedMessage}');
      
      // Handle specific error types
      switch (e.error.code) {
        case FailureCode.Canceled:
          throw Exception('Payment was cancelled');
        case FailureCode.Failed:
          throw Exception('Payment failed: ${e.error.localizedMessage}');
        default:
          throw Exception('Payment error: ${e.error.localizedMessage}');
      }
    } catch (e) {
      debugPrint('Payment error: $e');
      throw Exception('Payment failed: $e');
    }
  }

  Future<bool> processSetupIntent({
    required String clientSecret,
    required BuildContext context,
    String? customerId,
  }) async {
    try {
      // Initialize setup intent for saving payment methods
      await Stripe.instance.initPaymentSheet(
        paymentSheetParameters: SetupPaymentSheetParameters(
          setupIntentClientSecret: clientSecret,
          merchantDisplayName: 'Real Estate App',
          customerId: customerId,
          style: ThemeMode.system,
        ),
      );

      // Present payment sheet
      await Stripe.instance.presentPaymentSheet();
      
      return true;
    } on StripeException catch (e) {
      debugPrint('Stripe setup error: ${e.error.localizedMessage}');
      throw Exception('Setup failed: ${e.error.localizedMessage}');
    } catch (e) {
      debugPrint('Setup error: $e');
      throw Exception('Setup failed: $e');
    }
  }

  Future<payment_models.PaymentMethod?> createPaymentMethod({
    required String cardNumber,
    required String expiryMonth,
    required String expiryYear,
    required String cvc,
    BillingDetails? billingDetails,
  }) async {
    try {
      final paymentMethod = await Stripe.instance.createPaymentMethod(
        params: PaymentMethodParams.card(
          paymentMethodData: PaymentMethodData(
            billingDetails: billingDetails ?? const BillingDetails(),
          ),
        ),
      );

      return payment_models.PaymentMethod(
        id: paymentMethod.id,
        name: 'Card ending in ${cardNumber.substring(cardNumber.length - 4)}',
        type: 'STRIPE',
        description: 'Stripe payment method',
        isActive: true,
      );
    } on StripeException catch (e) {
      debugPrint('Create payment method error: ${e.error.localizedMessage}');
      throw Exception('Failed to create payment method: ${e.error.localizedMessage}');
    } catch (e) {
      debugPrint('Create payment method error: $e');
      throw Exception('Failed to create payment method: $e');
    }
  }

  Future<void> confirmPayment({
    required String clientSecret,
    required String paymentMethodId,
  }) async {
    try {
      await Stripe.instance.confirmPayment(
        paymentIntentClientSecret: clientSecret,
        data: PaymentMethodParams.cardFromMethodId(
          paymentMethodData: PaymentMethodDataCardFromMethod(
            paymentMethodId: paymentMethodId,
          ),
        ),
      );
    } on StripeException catch (e) {
      debugPrint('Confirm payment error: ${e.error.localizedMessage}');
      throw Exception('Payment confirmation failed: ${e.error.localizedMessage}');
    } catch (e) {
      debugPrint('Confirm payment error: $e');
      throw Exception('Payment confirmation failed: $e');
    }
  }

  bool get isInitialized => _isInitialized;

  void dispose() {
    _isInitialized = false;
  }
}

// Helper class for card brand names
class CardBrandHelper {
  static String getDisplayName(String brand) {
    switch (brand.toLowerCase()) {
      case 'visa':
        return 'Visa';
      case 'mastercard':
        return 'Mastercard';
      case 'amex':
        return 'American Express';
      case 'discover':
        return 'Discover';
      case 'jcb':
        return 'JCB';
      case 'diners':
        return 'Diners Club';
      case 'unionpay':
        return 'UnionPay';
      default:
        return 'Unknown';
    }
  }
}

// Helper class for payment method icons
class PaymentMethodIcons {
  static const Map<String, IconData> _icons = {
    'STRIPE': Icons.credit_card,
    'PAYPAL': Icons.payment,
    'BANK_TRANSFER': Icons.account_balance,
    'MOMO': Icons.phone_android,
    'ZALOPAY': Icons.phone_android,
    'VNPAY': Icons.account_balance_wallet,
  };

  static IconData getIcon(String paymentMethodType) {
    return _icons[paymentMethodType.toUpperCase()] ?? Icons.payment;
  }

  static Color getColor(String paymentMethodType) {
    switch (paymentMethodType.toUpperCase()) {
      case 'STRIPE':
        return const Color(0xFF635BFF);
      case 'PAYPAL':
        return const Color(0xFF0070BA);
      case 'BANK_TRANSFER':
        return Colors.blue;
      case 'MOMO':
        return const Color(0xFFD82D8B);
      case 'ZALOPAY':
        return const Color(0xFF0068FF);
      case 'VNPAY':
        return const Color(0xFF1976D2);
      default:
        return Colors.grey;
    }
  }
}
