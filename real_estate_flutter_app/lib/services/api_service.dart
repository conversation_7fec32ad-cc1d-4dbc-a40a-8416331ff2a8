import 'dart:convert';
import 'package:http/http.dart' as http;
import '../constants/api_constants.dart';
import '../models/user.dart';
import '../models/property.dart';
import '../models/category.dart';
import '../models/membership.dart';
import '../models/notification.dart';
import '../models/dashboard.dart';
import '../models/ai_content.dart';
import '../models/boost.dart';
import '../models/payment.dart';

class ApiService {
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal();

  String? _token;

  void setToken(String token) {
    _token = token;
  }

  void clearToken() {
    _token = null;
  }

  Map<String, String> get _headers {
    if (_token != null) {
      return ApiConstants.headersWithAuth(_token!);
    }
    return ApiConstants.headers;
  }

  // Authentication APIs
  Future<AuthResponse> login(LoginRequest request) async {
    final response = await http.post(
      Uri.parse(ApiConstants.login),
      headers: ApiConstants.headers,
      body: jsonEncode(request.toJson()),
    );

    if (response.statusCode == 200) {
      final authResponse = AuthResponse.fromJson(jsonDecode(response.body));
      setToken(authResponse.token);
      return authResponse;
    } else {
      throw Exception('Login failed: ${response.body}');
    }
  }

  Future<void> register(RegisterRequest request) async {
    final response = await http.post(
      Uri.parse(ApiConstants.register),
      headers: ApiConstants.headers,
      body: jsonEncode(request.toJson()),
    );

    if (response.statusCode != 200 && response.statusCode != 201) {
      throw Exception('Registration failed: ${response.body}');
    }
  }

  Future<void> forgotPassword(String email) async {
    final response = await http.post(
      Uri.parse(ApiConstants.forgotPassword),
      headers: ApiConstants.headers,
      body: jsonEncode({'email': email}),
    );

    if (response.statusCode != 200) {
      throw Exception('Forgot password failed: ${response.body}');
    }
  }

  // Public APIs
  Future<List<Property>> getProperties({int page = 0, int size = 10}) async {
    final response = await http.get(
      Uri.parse('${ApiConstants.properties}?page=$page&size=$size&sort=createdAt,desc'),
      headers: ApiConstants.headers,
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      if (data is List) {
        return data.map((json) => Property.fromJson(json)).toList();
      } else if (data['content'] != null) {
        return (data['content'] as List).map((json) => Property.fromJson(json)).toList();
      }
      return [];
    } else {
      throw Exception('Failed to load properties: ${response.body}');
    }
  }

  Future<Property> getPropertyById(int id) async {
    final response = await http.get(
      Uri.parse('${ApiConstants.properties}/$id'),
      headers: ApiConstants.headers,
    );

    if (response.statusCode == 200) {
      return Property.fromJson(jsonDecode(response.body));
    } else {
      throw Exception('Failed to load property: ${response.body}');
    }
  }

  Future<List<Property>> searchProperties(PropertySearchRequest request) async {
    final response = await http.post(
      Uri.parse(ApiConstants.propertiesSearch),
      headers: ApiConstants.headers,
      body: jsonEncode(request.toJson()),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      if (data is List) {
        return data.map((json) => Property.fromJson(json)).toList();
      }
      return [];
    } else {
      throw Exception('Failed to search properties: ${response.body}');
    }
  }

  Future<List<Category>> getCategories() async {
    final response = await http.get(
      Uri.parse(ApiConstants.categories),
      headers: ApiConstants.headers,
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      if (data is List) {
        return data.map((json) => Category.fromJson(json)).toList();
      }
      return [];
    } else {
      throw Exception('Failed to load categories: ${response.body}');
    }
  }

  Future<List<Membership>> getMemberships() async {
    final response = await http.get(
      Uri.parse(ApiConstants.memberships),
      headers: ApiConstants.headers,
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      if (data is List) {
        return data.map((json) => Membership.fromJson(json)).toList();
      }
      return [];
    } else {
      throw Exception('Failed to load memberships: ${response.body}');
    }
  }

  // User authenticated APIs
  Future<User> getUserProfile() async {
    final response = await http.get(
      Uri.parse(ApiConstants.userProfile),
      headers: _headers,
    );

    if (response.statusCode == 200) {
      return User.fromJson(jsonDecode(response.body));
    } else {
      throw Exception('Failed to load user profile: ${response.body}');
    }
  }

  Future<List<AppNotification>> getNotifications() async {
    final response = await http.get(
      Uri.parse(ApiConstants.notifications),
      headers: _headers,
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      if (data is List) {
        return data.map((json) => AppNotification.fromJson(json)).toList();
      }
      return [];
    } else {
      throw Exception('Failed to load notifications: ${response.body}');
    }
  }

  Future<NotificationCount> getUnreadNotificationCount() async {
    final response = await http.get(
      Uri.parse(ApiConstants.notificationsUnreadCount),
      headers: _headers,
    );

    if (response.statusCode == 200) {
      return NotificationCount.fromJson(jsonDecode(response.body));
    } else {
      throw Exception('Failed to load notification count: ${response.body}');
    }
  }

  Future<List<Property>> getUserProperties() async {
    final response = await http.get(
      Uri.parse(ApiConstants.userProperties),
      headers: _headers,
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      if (data is List) {
        return data.map((json) => Property.fromJson(json)).toList();
      }
      return [];
    } else {
      throw Exception('Failed to load user properties: ${response.body}');
    }
  }

  Future<Property> createProperty(PropertyCreateRequest request) async {
    final response = await http.post(
      Uri.parse(ApiConstants.properties),
      headers: _headers,
      body: jsonEncode(request.toJson()),
    );

    if (response.statusCode == 200 || response.statusCode == 201) {
      return Property.fromJson(jsonDecode(response.body));
    } else {
      throw Exception('Failed to create property: ${response.body}');
    }
  }

  // Dashboard APIs
  Future<UserDashboard> getUserDashboard() async {
    final response = await http.get(
      Uri.parse(ApiConstants.userDashboard),
      headers: _headers,
    );

    if (response.statusCode == 200) {
      return UserDashboard.fromJson(jsonDecode(response.body));
    } else {
      throw Exception('Failed to load dashboard: ${response.body}');
    }
  }

  Future<SeoScore> getSeoScore(int propertyId) async {
    final response = await http.get(
      Uri.parse('${ApiConstants.seoScore}/$propertyId'),
      headers: _headers,
    );

    if (response.statusCode == 200) {
      return SeoScore.fromJson(jsonDecode(response.body));
    } else {
      throw Exception('Failed to load SEO score: ${response.body}');
    }
  }

  // Payment APIs
  Future<List<PaymentMethod>> getPaymentMethods() async {
    final response = await http.get(
      Uri.parse(ApiConstants.paymentMethods),
      headers: ApiConstants.headers,
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      if (data is List) {
        return data.map((json) => PaymentMethod.fromJson(json)).toList();
      }
      return [];
    } else {
      throw Exception('Failed to load payment methods: ${response.body}');
    }
  }

  Future<ExchangeRate> getExchangeRate(String fromCurrency, String toCurrency) async {
    final response = await http.get(
      Uri.parse('${ApiConstants.exchangeRate}?from=$fromCurrency&to=$toCurrency'),
      headers: ApiConstants.headers,
    );

    if (response.statusCode == 200) {
      return ExchangeRate.fromJson(jsonDecode(response.body));
    } else {
      throw Exception('Failed to load exchange rate: ${response.body}');
    }
  }

  Future<PaymentResponse> createPayment(PaymentRequest request) async {
    final response = await http.post(
      Uri.parse(ApiConstants.createPayment),
      headers: _headers,
      body: jsonEncode(request.toJson()),
    );

    if (response.statusCode == 200 || response.statusCode == 201) {
      return PaymentResponse.fromJson(jsonDecode(response.body));
    } else {
      throw Exception('Failed to create payment: ${response.body}');
    }
  }

  Future<PaymentHistory> getPaymentHistory({int page = 0, int size = 10}) async {
    final response = await http.get(
      Uri.parse('${ApiConstants.paymentHistory}?page=$page&size=$size'),
      headers: _headers,
    );

    if (response.statusCode == 200) {
      return PaymentHistory.fromJson(jsonDecode(response.body));
    } else {
      throw Exception('Failed to load payment history: ${response.body}');
    }
  }

  // Membership APIs
  Future<PaymentResponse> purchaseMembership(PaymentRequest request) async {
    final response = await http.post(
      Uri.parse(ApiConstants.purchaseMembership),
      headers: _headers,
      body: jsonEncode(request.toJson()),
    );

    if (response.statusCode == 200 || response.statusCode == 201) {
      return PaymentResponse.fromJson(jsonDecode(response.body));
    } else {
      throw Exception('Failed to purchase membership: ${response.body}');
    }
  }

  // Push Top / Boost APIs
  Future<BoostStatus> getBoostStatus() async {
    final response = await http.get(
      Uri.parse(ApiConstants.boostStatus),
      headers: _headers,
    );

    if (response.statusCode == 200) {
      return BoostStatus.fromJson(jsonDecode(response.body));
    } else {
      throw Exception('Failed to load boost status: ${response.body}');
    }
  }

  Future<BoostResponse> pushPropertyToTop(int propertyId) async {
    final response = await http.post(
      Uri.parse('${ApiConstants.pushTop}/$propertyId/push-top'),
      headers: _headers,
    );

    if (response.statusCode == 200) {
      return BoostResponse.fromJson(jsonDecode(response.body));
    } else {
      throw Exception('Failed to push property to top: ${response.body}');
    }
  }

  Future<BoostHistory> getBoostHistory({int page = 0, int size = 10}) async {
    final response = await http.get(
      Uri.parse('${ApiConstants.boostHistory}?page=$page&size=$size'),
      headers: _headers,
    );

    if (response.statusCode == 200) {
      return BoostHistory.fromJson(jsonDecode(response.body));
    } else {
      throw Exception('Failed to load boost history: ${response.body}');
    }
  }

  Future<List<BoostHistoryItem>> getActiveBoosts() async {
    final response = await http.get(
      Uri.parse(ApiConstants.activeBoosts),
      headers: _headers,
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      if (data is List) {
        return data.map((json) => BoostHistoryItem.fromJson(json)).toList();
      }
      return [];
    } else {
      throw Exception('Failed to load active boosts: ${response.body}');
    }
  }

  // AI Content Generation
  Future<AiContentResponse> generateAiContent(AiContentRequest request) async {
    final response = await http.post(
      Uri.parse(ApiConstants.aiContentGenerate),
      headers: _headers,
      body: jsonEncode(request.toJson()),
    );

    if (response.statusCode == 200) {
      return AiContentResponse.fromJson(jsonDecode(response.body));
    } else {
      throw Exception('Failed to generate AI content: ${response.body}');
    }
  }
}
