import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import 'api_service.dart';

class AuthService {
  static const String _tokenKey = 'auth_token';
  static const String _userKey = 'user_data';
  static const String _usernameKey = 'username';
  static const String _emailKey = 'email';
  static const String _roleKey = 'role';

  final ApiService _apiService = ApiService();

  Future<AuthResponse> login(String username, String password) async {
    try {
      final loginRequest = LoginRequest(username: username, password: password);
      final authResponse = await _apiService.login(loginRequest);
      
      // Save auth data to local storage
      await _saveAuthData(authResponse);
      
      return authResponse;
    } catch (e) {
      throw Exception('Login failed: $e');
    }
  }

  Future<void> register(String username, String email, String password, String firstName, String lastName) async {
    try {
      final registerRequest = RegisterRequest(
        username: username,
        email: email,
        password: password,
        firstName: firstName,
        lastName: lastName,
      );
      await _apiService.register(registerRequest);
    } catch (e) {
      throw Exception('Registration failed: $e');
    }
  }

  Future<void> forgotPassword(String email) async {
    try {
      await _apiService.forgotPassword(email);
    } catch (e) {
      throw Exception('Forgot password failed: $e');
    }
  }

  Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_tokenKey);
    await prefs.remove(_userKey);
    await prefs.remove(_usernameKey);
    await prefs.remove(_emailKey);
    await prefs.remove(_roleKey);
    
    _apiService.clearToken();
  }

  Future<bool> isLoggedIn() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString(_tokenKey);
    
    if (token != null) {
      _apiService.setToken(token);
      return true;
    }
    return false;
  }

  Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_tokenKey);
  }

  Future<String?> getUsername() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_usernameKey);
  }

  Future<String?> getEmail() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_emailKey);
  }

  Future<String?> getRole() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_roleKey);
  }

  Future<bool> isAdmin() async {
    final role = await getRole();
    return role == 'ADMIN';
  }

  Future<void> _saveAuthData(AuthResponse authResponse) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_tokenKey, authResponse.token);
    await prefs.setString(_usernameKey, authResponse.username);
    await prefs.setString(_emailKey, authResponse.email);
    await prefs.setString(_roleKey, authResponse.role);
  }
}
