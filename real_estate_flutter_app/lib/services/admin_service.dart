import 'dart:convert';
import 'package:http/http.dart' as http;
import '../constants/api_constants.dart';
import '../models/admin_models.dart';
import '../services/auth_service.dart';

class AdminService {
  final AuthService _authService = AuthService();

  Future<Map<String, String>> _getHeaders() async {
    final token = await _authService.getToken();
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
    };
  }

  // Get admin dashboard stats
  Future<AdminStats?> getAdminStats() async {
    try {
      final headers = await _getHeaders();
      final response = await http.get(
        Uri.parse('${ApiConstants.baseUrl}/admin/stats'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return AdminStats.fromJson(data);
      }
      return null;
    } catch (e) {
      print('Error getting admin stats: $e');
      return null;
    }
  }

  // Get all properties for admin review
  Future<List<AdminProperty>> getPropertiesForReview({String? status}) async {
    try {
      final headers = await _getHeaders();
      String url = '${ApiConstants.baseUrl}/admin/properties';
      if (status != null) {
        url += '?status=$status';
      }
      
      final response = await http.get(
        Uri.parse(url),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((json) => AdminProperty.fromJson(json)).toList();
      }
      return [];
    } catch (e) {
      print('Error getting properties for review: $e');
      return [];
    }
  }

  // Approve or reject property
  Future<bool> reviewProperty(int propertyId, PropertyApprovalRequest request) async {
    try {
      final headers = await _getHeaders();
      final response = await http.post(
        Uri.parse('${ApiConstants.baseUrl}/admin/properties/$propertyId/review'),
        headers: headers,
        body: json.encode(request.toJson()),
      );

      return response.statusCode == 200;
    } catch (e) {
      print('Error reviewing property: $e');
      return false;
    }
  }

  // Get all users for admin management
  Future<List<AdminUser>> getAllUsers({String? status}) async {
    try {
      final headers = await _getHeaders();
      String url = '${ApiConstants.baseUrl}/admin/users';
      if (status != null) {
        url += '?status=$status';
      }
      
      final response = await http.get(
        Uri.parse(url),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((json) => AdminUser.fromJson(json)).toList();
      }
      return [];
    } catch (e) {
      print('Error getting users: $e');
      return [];
    }
  }

  // Ban or unban user
  Future<bool> manageUser(int userId, UserBanRequest request) async {
    try {
      final headers = await _getHeaders();
      final response = await http.post(
        Uri.parse('${ApiConstants.baseUrl}/admin/users/$userId/manage'),
        headers: headers,
        body: json.encode(request.toJson()),
      );

      return response.statusCode == 200;
    } catch (e) {
      print('Error managing user: $e');
      return false;
    }
  }

  // Get all payments for admin review
  Future<List<AdminPayment>> getAllPayments({String? status}) async {
    try {
      final headers = await _getHeaders();
      String url = '${ApiConstants.baseUrl}/admin/payments';
      if (status != null) {
        url += '?status=$status';
      }
      
      final response = await http.get(
        Uri.parse(url),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((json) => AdminPayment.fromJson(json)).toList();
      }
      return [];
    } catch (e) {
      print('Error getting payments: $e');
      return [];
    }
  }

  // Delete property (admin only)
  Future<bool> deleteProperty(int propertyId) async {
    try {
      final headers = await _getHeaders();
      final response = await http.delete(
        Uri.parse('${ApiConstants.baseUrl}/admin/properties/$propertyId'),
        headers: headers,
      );

      return response.statusCode == 200;
    } catch (e) {
      print('Error deleting property: $e');
      return false;
    }
  }

  // Get property details for admin
  Future<AdminProperty?> getPropertyDetails(int propertyId) async {
    try {
      final headers = await _getHeaders();
      final response = await http.get(
        Uri.parse('${ApiConstants.baseUrl}/admin/properties/$propertyId'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return AdminProperty.fromJson(data);
      }
      return null;
    } catch (e) {
      print('Error getting property details: $e');
      return null;
    }
  }

  // Update payment status (admin only)
  Future<bool> updatePaymentStatus(int paymentId, String status) async {
    try {
      final headers = await _getHeaders();
      final response = await http.patch(
        Uri.parse('${ApiConstants.baseUrl}/admin/payments/$paymentId/status'),
        headers: headers,
        body: json.encode({'status': status}),
      );

      return response.statusCode == 200;
    } catch (e) {
      print('Error updating payment status: $e');
      return false;
    }
  }
}
