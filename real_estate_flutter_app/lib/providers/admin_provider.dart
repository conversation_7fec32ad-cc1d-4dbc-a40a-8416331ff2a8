import 'package:flutter/foundation.dart';
import '../models/admin_models.dart';
import '../services/admin_service.dart';

class AdminProvider with ChangeNotifier {
  final AdminService _adminService = AdminService();

  // State
  bool _isLoading = false;
  String? _errorMessage;
  
  // Data
  AdminStats? _stats;
  List<AdminProperty> _properties = [];
  List<AdminUser> _users = [];
  List<AdminPayment> _payments = [];

  // Getters
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  AdminStats? get stats => _stats;
  List<AdminProperty> get properties => _properties;
  List<AdminUser> get users => _users;
  List<AdminPayment> get payments => _payments;

  // Get pending properties count
  int get pendingPropertiesCount => _properties.where((p) => p.status == 'PENDING').length;
  
  // Get active users count
  int get activeUsersCount => _users.where((u) => u.status == 'ACTIVE').length;
  
  // Get banned users count
  int get bannedUsersCount => _users.where((u) => u.status == 'BANNED').length;

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _errorMessage = error;
    notifyListeners();
  }

  // Load admin dashboard stats
  Future<void> loadStats() async {
    _setLoading(true);
    _setError(null);

    try {
      _stats = await _adminService.getAdminStats();
      notifyListeners();
    } catch (e) {
      _setError('Không thể tải thống kê: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Load properties for review
  Future<void> loadProperties({String? status}) async {
    _setLoading(true);
    _setError(null);

    try {
      _properties = await _adminService.getPropertiesForReview(status: status);
      notifyListeners();
    } catch (e) {
      _setError('Không thể tải danh sách bài đăng: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Review property (approve/reject)
  Future<bool> reviewProperty(int propertyId, String action, {String? rejectionReason}) async {
    _setLoading(true);
    _setError(null);

    try {
      final request = PropertyApprovalRequest(
        action: action,
        rejectionReason: rejectionReason,
      );

      final success = await _adminService.reviewProperty(propertyId, request);
      
      if (success) {
        // Update local property status
        final propertyIndex = _properties.indexWhere((p) => p.id == propertyId);
        if (propertyIndex != -1) {
          final updatedProperty = AdminProperty(
            id: _properties[propertyIndex].id,
            title: _properties[propertyIndex].title,
            description: _properties[propertyIndex].description,
            price: _properties[propertyIndex].price,
            address: _properties[propertyIndex].address,
            city: _properties[propertyIndex].city,
            district: _properties[propertyIndex].district,
            ward: _properties[propertyIndex].ward,
            propertyType: _properties[propertyIndex].propertyType,
            listingType: _properties[propertyIndex].listingType,
            bedrooms: _properties[propertyIndex].bedrooms,
            bathrooms: _properties[propertyIndex].bathrooms,
            propertyArea: _properties[propertyIndex].propertyArea,
            status: action == 'APPROVE' ? 'APPROVED' : 'REJECTED',
            ownerName: _properties[propertyIndex].ownerName,
            ownerEmail: _properties[propertyIndex].ownerEmail,
            createdAt: _properties[propertyIndex].createdAt,
            updatedAt: DateTime.now(),
            rejectionReason: rejectionReason,
          );
          _properties[propertyIndex] = updatedProperty;
          notifyListeners();
        }
      } else {
        _setError('Không thể cập nhật trạng thái bài đăng');
      }

      return success;
    } catch (e) {
      _setError('Lỗi khi duyệt bài: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Load users
  Future<void> loadUsers({String? status}) async {
    _setLoading(true);
    _setError(null);

    try {
      _users = await _adminService.getAllUsers(status: status);
      notifyListeners();
    } catch (e) {
      _setError('Không thể tải danh sách người dùng: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Ban/Unban user
  Future<bool> manageUser(int userId, String action, {String? reason}) async {
    _setLoading(true);
    _setError(null);

    try {
      final request = UserBanRequest(action: action, reason: reason);
      final success = await _adminService.manageUser(userId, request);
      
      if (success) {
        // Update local user status
        final userIndex = _users.indexWhere((u) => u.id == userId);
        if (userIndex != -1) {
          final updatedUser = AdminUser(
            id: _users[userIndex].id,
            username: _users[userIndex].username,
            email: _users[userIndex].email,
            fullName: _users[userIndex].fullName,
            phone: _users[userIndex].phone,
            status: action == 'BAN' ? 'BANNED' : 'ACTIVE',
            createdAt: _users[userIndex].createdAt,
            lastLogin: _users[userIndex].lastLogin,
            totalProperties: _users[userIndex].totalProperties,
            membershipType: _users[userIndex].membershipType,
          );
          _users[userIndex] = updatedUser;
          notifyListeners();
        }
      } else {
        _setError('Không thể cập nhật trạng thái người dùng');
      }

      return success;
    } catch (e) {
      _setError('Lỗi khi quản lý người dùng: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Load payments
  Future<void> loadPayments({String? status}) async {
    _setLoading(true);
    _setError(null);

    try {
      _payments = await _adminService.getAllPayments(status: status);
      notifyListeners();
    } catch (e) {
      _setError('Không thể tải danh sách thanh toán: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Delete property
  Future<bool> deleteProperty(int propertyId) async {
    _setLoading(true);
    _setError(null);

    try {
      final success = await _adminService.deleteProperty(propertyId);
      
      if (success) {
        _properties.removeWhere((p) => p.id == propertyId);
        notifyListeners();
      } else {
        _setError('Không thể xóa bài đăng');
      }

      return success;
    } catch (e) {
      _setError('Lỗi khi xóa bài đăng: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update payment status
  Future<bool> updatePaymentStatus(int paymentId, String status) async {
    _setLoading(true);
    _setError(null);

    try {
      final success = await _adminService.updatePaymentStatus(paymentId, status);
      
      if (success) {
        // Update local payment status
        final paymentIndex = _payments.indexWhere((p) => p.id == paymentId);
        if (paymentIndex != -1) {
          final updatedPayment = AdminPayment(
            id: _payments[paymentIndex].id,
            orderId: _payments[paymentIndex].orderId,
            username: _payments[paymentIndex].username,
            membershipName: _payments[paymentIndex].membershipName,
            amount: _payments[paymentIndex].amount,
            currency: _payments[paymentIndex].currency,
            status: status,
            paymentMethod: _payments[paymentIndex].paymentMethod,
            createdAt: _payments[paymentIndex].createdAt,
            completedAt: status == 'COMPLETED' ? DateTime.now() : _payments[paymentIndex].completedAt,
          );
          _payments[paymentIndex] = updatedPayment;
          notifyListeners();
        }
      } else {
        _setError('Không thể cập nhật trạng thái thanh toán');
      }

      return success;
    } catch (e) {
      _setError('Lỗi khi cập nhật thanh toán: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Clear error
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
