import 'package:flutter/foundation.dart';
import '../models/property.dart';
import '../models/category.dart' as models;
import '../models/membership.dart';
import '../services/api_service.dart';

class PropertyProvider with ChangeNotifier {
  final ApiService _apiService = ApiService();
  
  List<Property> _properties = [];
  List<Property> _userProperties = [];
  List<models.Category> _categories = [];
  List<Membership> _memberships = [];
  Property? _selectedProperty;
  bool _isLoading = false;
  String? _errorMessage;
  int _currentPage = 0;
  bool _hasMoreData = true;

  List<Property> get properties => _properties;
  List<Property> get userProperties => _userProperties;
  List<models.Category> get categories => _categories;
  List<Membership> get memberships => _memberships;
  Property? get selectedProperty => _selectedProperty;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get hasMoreData => _hasMoreData;

  Future<void> loadProperties({bool refresh = false}) async {
    if (refresh) {
      _currentPage = 0;
      _properties.clear();
      _hasMoreData = true;
    }

    if (_isLoading || !_hasMoreData) return;

    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      final newProperties = await _apiService.getProperties(page: _currentPage, size: 10);
      
      if (newProperties.isEmpty) {
        _hasMoreData = false;
      } else {
        if (refresh) {
          _properties = newProperties;
        } else {
          _properties.addAll(newProperties);
        }
        _currentPage++;
      }
    } catch (e) {
      _errorMessage = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> loadPropertyById(int id) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      _selectedProperty = await _apiService.getPropertyById(id);
    } catch (e) {
      _errorMessage = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> searchProperties(PropertySearchRequest searchRequest) async {
    _isLoading = true;
    _errorMessage = null;
    _properties.clear();
    notifyListeners();

    try {
      _properties = await _apiService.searchProperties(searchRequest);
    } catch (e) {
      _errorMessage = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> loadCategories() async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      _categories = await _apiService.getCategories();
    } catch (e) {
      _errorMessage = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> loadMemberships() async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      _memberships = await _apiService.getMemberships();
    } catch (e) {
      _errorMessage = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> loadUserProperties() async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      _userProperties = await _apiService.getUserProperties();
    } catch (e) {
      _errorMessage = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> createProperty(PropertyCreateRequest request) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      final newProperty = await _apiService.createProperty(request);
      _userProperties.insert(0, newProperty);
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _errorMessage = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  void clearSelectedProperty() {
    _selectedProperty = null;
    notifyListeners();
  }
}
