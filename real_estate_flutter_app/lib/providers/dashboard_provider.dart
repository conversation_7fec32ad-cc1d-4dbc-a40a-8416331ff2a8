import 'package:flutter/foundation.dart';
import '../models/dashboard.dart';
import '../models/ai_content.dart';
import '../models/boost.dart';
import '../services/api_service.dart';

class DashboardProvider with ChangeNotifier {
  final ApiService _apiService = ApiService();
  
  UserDashboard? _dashboard;
  BoostStatus? _boostStatus;
  BoostHistory? _boostHistory;
  List<BoostHistoryItem> _activeBoosts = [];
  bool _isLoading = false;
  String? _errorMessage;

  UserDashboard? get dashboard => _dashboard;
  BoostStatus? get boostStatus => _boostStatus;
  BoostHistory? get boostHistory => _boostHistory;
  List<BoostHistoryItem> get activeBoosts => _activeBoosts;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  Future<void> loadDashboard() async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      _dashboard = await _apiService.getUserDashboard();
    } catch (e) {
      _errorMessage = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> loadBoostStatus() async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      _boostStatus = await _apiService.getBoostStatus();
    } catch (e) {
      _errorMessage = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> pushPropertyToTop(int propertyId) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      final response = await _apiService.pushPropertyToTop(propertyId);
      if (response.success) {
        // Refresh boost status and active boosts
        await loadBoostStatus();
        await loadActiveBoosts();
        return true;
      } else {
        _errorMessage = response.message;
        return false;
      }
    } catch (e) {
      _errorMessage = e.toString();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> loadBoostHistory({int page = 0, int size = 10}) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      _boostHistory = await _apiService.getBoostHistory(page: page, size: size);
    } catch (e) {
      _errorMessage = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> loadActiveBoosts() async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      _activeBoosts = await _apiService.getActiveBoosts();
    } catch (e) {
      _errorMessage = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<AiContentResponse?> generateAiContent(String description, {String? imageDescription}) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      final request = AiContentRequest(
        description: description,
        imageDescription: imageDescription,
      );
      final response = await _apiService.generateAiContent(request);
      
      // Refresh dashboard to update usage
      await loadDashboard();
      
      return response;
    } catch (e) {
      _errorMessage = e.toString();
      return null;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Note: purchaseMembership is now handled by PaymentProvider

  Future<SeoScore?> getSeoScore(int propertyId) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      final seoScore = await _apiService.getSeoScore(propertyId);
      return seoScore;
    } catch (e) {
      _errorMessage = e.toString();
      return null;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  void clearData() {
    _dashboard = null;
    _boostStatus = null;
    _boostHistory = null;
    _activeBoosts.clear();
    _errorMessage = null;
    notifyListeners();
  }
}
