import 'package:flutter/foundation.dart';
import '../models/payment.dart';
import '../models/membership.dart';
import '../services/api_service.dart';

class PaymentProvider with ChangeNotifier {
  final ApiService _apiService = ApiService();
  
  List<PaymentMethod> _paymentMethods = [];
  PaymentHistory? _paymentHistory;
  ExchangeRate? _exchangeRate;
  bool _isLoading = false;
  String? _errorMessage;

  List<PaymentMethod> get paymentMethods => _paymentMethods;
  PaymentHistory? get paymentHistory => _paymentHistory;
  ExchangeRate? get exchangeRate => _exchangeRate;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  Future<void> loadPaymentMethods() async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      _paymentMethods = await _apiService.getPaymentMethods();
    } catch (e) {
      _errorMessage = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> loadExchangeRate(String fromCurrency, String toCurrency) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      _exchangeRate = await _apiService.getExchangeRate(fromCurrency, toCurrency);
    } catch (e) {
      _errorMessage = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> loadPaymentHistory({int page = 0, int size = 10}) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      _paymentHistory = await _apiService.getPaymentHistory(page: page, size: size);
    } catch (e) {
      _errorMessage = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<PaymentResponse?> createPayment(PaymentRequest request) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      final response = await _apiService.createPayment(request);
      return response;
    } catch (e) {
      _errorMessage = e.toString();
      return null;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<PaymentResponse?> purchaseMembership(
    Membership membership,
    String paymentMethodId, {
    String currency = 'VND',
    Map<String, dynamic>? metadata,
  }) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      final request = PaymentRequest(
        membershipId: membership.id ?? 0,
        paymentMethod: paymentMethodId,
        currency: currency,
        metadata: {
          'membershipName': membership.name ?? '',
          'membershipType': membership.type ?? '',
          'durationMonths': membership.durationMonths ?? 1,
          ...?metadata,
        },
      );

      final response = await _apiService.purchaseMembership(request);
      
      // Refresh payment history after successful purchase
      if (response.success) {
        await loadPaymentHistory();
      }
      
      return response;
    } catch (e) {
      _errorMessage = e.toString();
      return null;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  double convertPrice(double price, String fromCurrency, String toCurrency) {
    if (fromCurrency == toCurrency) return price;
    
    if (_exchangeRate != null && 
        _exchangeRate!.fromCurrency == fromCurrency && 
        _exchangeRate!.toCurrency == toCurrency) {
      return price * _exchangeRate!.rate;
    }
    
    return price; // Return original price if no exchange rate available
  }

  String formatPrice(double price, String currency) {
    switch (currency.toUpperCase()) {
      case 'VND':
        if (price >= 1000000) {
          return '${(price / 1000000).toStringAsFixed(1)} triệu VND';
        } else {
          return '${price.toStringAsFixed(0)} VND';
        }
      case 'USD':
        return '\$${price.toStringAsFixed(2)}';
      case 'EUR':
        return '€${price.toStringAsFixed(2)}';
      default:
        return '${price.toStringAsFixed(2)} $currency';
    }
  }

  PaymentMethod? getPaymentMethodById(String id) {
    try {
      return _paymentMethods.firstWhere((method) => method.id == id);
    } catch (e) {
      return null;
    }
  }

  List<PaymentMethod> getActivePaymentMethods() {
    return _paymentMethods.where((method) => method.isActive).toList();
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  void clearData() {
    _paymentMethods.clear();
    _paymentHistory = null;
    _exchangeRate = null;
    _errorMessage = null;
    notifyListeners();
  }
}
