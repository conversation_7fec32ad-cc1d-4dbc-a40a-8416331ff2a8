// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'property.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Property _$PropertyFromJson(Map<String, dynamic> json) => Property(
  id: (json['id'] as num?)?.toInt(),
  title: json['title'] as String?,
  description: json['description'] as String?,
  price: (json['price'] as num?)?.toDouble(),
  address: json['address'] as String?,
  city: json['city'] as String?,
  district: json['district'] as String?,
  ward: json['ward'] as String?,
  propertyType: json['propertyType'] as String?,
  listingType: json['listingType'] as String?,
  bedrooms: (json['bedrooms'] as num?)?.toInt(),
  bathrooms: (json['bathrooms'] as num?)?.toInt(),
  propertyArea: (json['propertyArea'] as num?)?.toDouble(),
  status: json['status'] as String?,
  categoryId: (json['categoryId'] as num?)?.toInt(),
  ownerId: (json['ownerId'] as num?)?.toInt(),
  imageUrls: (json['imageUrls'] as List<dynamic>?)
      ?.map((e) => e as String)
      .toList(),
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$PropertyToJson(Property instance) => <String, dynamic>{
  'id': instance.id,
  'title': instance.title,
  'description': instance.description,
  'price': instance.price,
  'address': instance.address,
  'city': instance.city,
  'district': instance.district,
  'ward': instance.ward,
  'propertyType': instance.propertyType,
  'listingType': instance.listingType,
  'bedrooms': instance.bedrooms,
  'bathrooms': instance.bathrooms,
  'propertyArea': instance.propertyArea,
  'status': instance.status,
  'categoryId': instance.categoryId,
  'ownerId': instance.ownerId,
  'imageUrls': instance.imageUrls,
  'createdAt': instance.createdAt?.toIso8601String(),
  'updatedAt': instance.updatedAt?.toIso8601String(),
};

PropertyCreateRequest _$PropertyCreateRequestFromJson(
  Map<String, dynamic> json,
) => PropertyCreateRequest(
  title: json['title'] as String,
  description: json['description'] as String,
  price: (json['price'] as num).toDouble(),
  address: json['address'] as String,
  city: json['city'] as String,
  district: json['district'] as String,
  ward: json['ward'] as String,
  propertyType: json['propertyType'] as String,
  listingType: json['listingType'] as String,
  bedrooms: (json['bedrooms'] as num).toInt(),
  bathrooms: (json['bathrooms'] as num).toInt(),
  propertyArea: (json['propertyArea'] as num).toDouble(),
  categoryId: (json['categoryId'] as num).toInt(),
);

Map<String, dynamic> _$PropertyCreateRequestToJson(
  PropertyCreateRequest instance,
) => <String, dynamic>{
  'title': instance.title,
  'description': instance.description,
  'price': instance.price,
  'address': instance.address,
  'city': instance.city,
  'district': instance.district,
  'ward': instance.ward,
  'propertyType': instance.propertyType,
  'listingType': instance.listingType,
  'bedrooms': instance.bedrooms,
  'bathrooms': instance.bathrooms,
  'propertyArea': instance.propertyArea,
  'categoryId': instance.categoryId,
};

PropertySearchRequest _$PropertySearchRequestFromJson(
  Map<String, dynamic> json,
) => PropertySearchRequest(
  keyword: json['keyword'] as String?,
  city: json['city'] as String?,
  district: json['district'] as String?,
  propertyType: json['propertyType'] as String?,
  listingType: json['listingType'] as String?,
  minPrice: (json['minPrice'] as num?)?.toDouble(),
  maxPrice: (json['maxPrice'] as num?)?.toDouble(),
  minArea: (json['minArea'] as num?)?.toDouble(),
  maxArea: (json['maxArea'] as num?)?.toDouble(),
  bedrooms: (json['bedrooms'] as num?)?.toInt(),
  bathrooms: (json['bathrooms'] as num?)?.toInt(),
);

Map<String, dynamic> _$PropertySearchRequestToJson(
  PropertySearchRequest instance,
) => <String, dynamic>{
  'keyword': instance.keyword,
  'city': instance.city,
  'district': instance.district,
  'propertyType': instance.propertyType,
  'listingType': instance.listingType,
  'minPrice': instance.minPrice,
  'maxPrice': instance.maxPrice,
  'minArea': instance.minArea,
  'maxArea': instance.maxArea,
  'bedrooms': instance.bedrooms,
  'bathrooms': instance.bathrooms,
};
