// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dashboard.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserDashboard _$UserDashboardFromJson(Map<String, dynamic> json) =>
    UserDashboard(
      properties: PropertyStats.fromJson(
        json['properties'] as Map<String, dynamic>,
      ),
      membership: MembershipInfo.fromJson(
        json['membership'] as Map<String, dynamic>,
      ),
      monthlyUsage: MonthlyUsage.fromJson(
        json['monthlyUsage'] as Map<String, dynamic>,
      ),
      suggestions: (json['suggestions'] as List<dynamic>)
          .map((e) => Suggestion.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$UserDashboardToJson(UserDashboard instance) =>
    <String, dynamic>{
      'properties': instance.properties,
      'membership': instance.membership,
      'monthlyUsage': instance.monthlyUsage,
      'suggestions': instance.suggestions,
    };

PropertyStats _$PropertyStatsFromJson(Map<String, dynamic> json) =>
    PropertyStats(
      totalProperties: (json['totalProperties'] as num).toInt(),
      approvedProperties: (json['approvedProperties'] as num).toInt(),
      pendingProperties: (json['pendingProperties'] as num).toInt(),
      rejectedProperties: (json['rejectedProperties'] as num).toInt(),
      totalViews: (json['totalViews'] as num).toInt(),
      totalContacts: (json['totalContacts'] as num).toInt(),
      activeBoostedProperties: (json['activeBoostedProperties'] as num).toInt(),
    );

Map<String, dynamic> _$PropertyStatsToJson(PropertyStats instance) =>
    <String, dynamic>{
      'totalProperties': instance.totalProperties,
      'approvedProperties': instance.approvedProperties,
      'pendingProperties': instance.pendingProperties,
      'rejectedProperties': instance.rejectedProperties,
      'totalViews': instance.totalViews,
      'totalContacts': instance.totalContacts,
      'activeBoostedProperties': instance.activeBoostedProperties,
    };

MembershipInfo _$MembershipInfoFromJson(Map<String, dynamic> json) =>
    MembershipInfo(
      hasActiveMembership: json['hasActiveMembership'] as bool,
      message: json['message'] as String,
      membershipType: json['membershipType'] as String?,
      expiryDate: json['expiryDate'] == null
          ? null
          : DateTime.parse(json['expiryDate'] as String),
    );

Map<String, dynamic> _$MembershipInfoToJson(MembershipInfo instance) =>
    <String, dynamic>{
      'hasActiveMembership': instance.hasActiveMembership,
      'message': instance.message,
      'membershipType': instance.membershipType,
      'expiryDate': instance.expiryDate?.toIso8601String(),
    };

MonthlyUsage _$MonthlyUsageFromJson(Map<String, dynamic> json) => MonthlyUsage(
  pushTopUsed: (json['pushTopUsed'] as num).toInt(),
  aiContentUsed: (json['aiContentUsed'] as num).toInt(),
  month: json['month'] as String,
  resetDate: (json['resetDate'] as List<dynamic>)
      .map((e) => (e as num).toInt())
      .toList(),
);

Map<String, dynamic> _$MonthlyUsageToJson(MonthlyUsage instance) =>
    <String, dynamic>{
      'pushTopUsed': instance.pushTopUsed,
      'aiContentUsed': instance.aiContentUsed,
      'month': instance.month,
      'resetDate': instance.resetDate,
    };

Suggestion _$SuggestionFromJson(Map<String, dynamic> json) => Suggestion(
  type: json['type'] as String,
  title: json['title'] as String,
  message: json['message'] as String,
  action: json['action'] as String,
  priority: json['priority'] as String,
);

Map<String, dynamic> _$SuggestionToJson(Suggestion instance) =>
    <String, dynamic>{
      'type': instance.type,
      'title': instance.title,
      'message': instance.message,
      'action': instance.action,
      'priority': instance.priority,
    };

SeoScore _$SeoScoreFromJson(Map<String, dynamic> json) => SeoScore(
  score: (json['score'] as num).toInt(),
  suggestions: (json['suggestions'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  details: json['details'] as Map<String, dynamic>,
);

Map<String, dynamic> _$SeoScoreToJson(SeoScore instance) => <String, dynamic>{
  'score': instance.score,
  'suggestions': instance.suggestions,
  'details': instance.details,
};
