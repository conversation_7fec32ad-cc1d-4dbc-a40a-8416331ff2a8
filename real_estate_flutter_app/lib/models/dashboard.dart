import 'package:json_annotation/json_annotation.dart';

part 'dashboard.g.dart';

@JsonSerializable()
class UserDashboard {
  final PropertyStats properties;
  final MembershipInfo membership;
  final MonthlyUsage monthlyUsage;
  final List<Suggestion> suggestions;

  UserDashboard({
    required this.properties,
    required this.membership,
    required this.monthlyUsage,
    required this.suggestions,
  });

  factory UserDashboard.fromJson(Map<String, dynamic> json) => _$UserDashboardFromJson(json);
  Map<String, dynamic> toJson() => _$UserDashboardToJson(this);
}

@JsonSerializable()
class PropertyStats {
  final int totalProperties;
  final int approvedProperties;
  final int pendingProperties;
  final int rejectedProperties;
  final int totalViews;
  final int totalContacts;
  final int activeBoostedProperties;

  PropertyStats({
    required this.totalProperties,
    required this.approvedProperties,
    required this.pendingProperties,
    required this.rejectedProperties,
    required this.totalViews,
    required this.totalContacts,
    required this.activeBoostedProperties,
  });

  factory PropertyStats.fromJson(Map<String, dynamic> json) => _$PropertyStatsFromJson(json);
  Map<String, dynamic> toJson() => _$PropertyStatsToJson(this);
}

@JsonSerializable()
class MembershipInfo {
  final bool hasActiveMembership;
  final String message;
  final String? membershipType;
  final DateTime? expiryDate;

  MembershipInfo({
    required this.hasActiveMembership,
    required this.message,
    this.membershipType,
    this.expiryDate,
  });

  factory MembershipInfo.fromJson(Map<String, dynamic> json) => _$MembershipInfoFromJson(json);
  Map<String, dynamic> toJson() => _$MembershipInfoToJson(this);
}

@JsonSerializable()
class MonthlyUsage {
  final int pushTopUsed;
  final int aiContentUsed;
  final String month;
  final List<int> resetDate;

  MonthlyUsage({
    required this.pushTopUsed,
    required this.aiContentUsed,
    required this.month,
    required this.resetDate,
  });

  factory MonthlyUsage.fromJson(Map<String, dynamic> json) => _$MonthlyUsageFromJson(json);
  Map<String, dynamic> toJson() => _$MonthlyUsageToJson(this);

  DateTime get resetDateTime {
    if (resetDate.length >= 3) {
      return DateTime(resetDate[0], resetDate[1], resetDate[2]);
    }
    return DateTime.now();
  }
}

@JsonSerializable()
class Suggestion {
  final String type;
  final String title;
  final String message;
  final String action;
  final String priority;

  Suggestion({
    required this.type,
    required this.title,
    required this.message,
    required this.action,
    required this.priority,
  });

  factory Suggestion.fromJson(Map<String, dynamic> json) => _$SuggestionFromJson(json);
  Map<String, dynamic> toJson() => _$SuggestionToJson(this);
}

@JsonSerializable()
class SeoScore {
  final int score;
  final List<String> suggestions;
  final Map<String, dynamic> details;

  SeoScore({
    required this.score,
    required this.suggestions,
    required this.details,
  });

  factory SeoScore.fromJson(Map<String, dynamic> json) => _$SeoScoreFromJson(json);
  Map<String, dynamic> toJson() => _$SeoScoreToJson(this);
}
