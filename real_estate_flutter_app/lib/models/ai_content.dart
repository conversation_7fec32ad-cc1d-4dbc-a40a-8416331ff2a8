import 'package:json_annotation/json_annotation.dart';

part 'ai_content.g.dart';

@JsonSerializable()
class AiContentRequest {
  final String description;
  final String? imageDescription;

  AiContentRequest({
    required this.description,
    this.imageDescription,
  });

  factory AiContentRequest.fromJson(Map<String, dynamic> json) => _$AiContentRequestFromJson(json);
  Map<String, dynamic> toJson() => _$AiContentRequestToJson(this);
}

@JsonSerializable()
class AiContentResponse {
  final bool success;
  final AiContent content;
  final UsageInfo usage;

  AiContentResponse({
    required this.success,
    required this.content,
    required this.usage,
  });

  factory AiContentResponse.fromJson(Map<String, dynamic> json) => _$AiContentResponseFromJson(json);
  Map<String, dynamic> toJson() => _$AiContentResponseToJson(this);
}

@JsonSerializable()
class AiContent {
  final String seoOptimizedDescription;
  final List<String> titleSuggestions;
  final String metaDescription;
  final int seoScore;
  final List<String> keywords;

  AiContent({
    required this.seoOptimizedDescription,
    required this.titleSuggestions,
    required this.metaDescription,
    required this.seoScore,
    required this.keywords,
  });

  factory AiContent.fromJson(Map<String, dynamic> json) => _$AiContentFromJson(json);
  Map<String, dynamic> toJson() => _$AiContentToJson(this);
}

@JsonSerializable()
class UsageInfo {
  final int aiContentUsed;
  final String month;

  UsageInfo({
    required this.aiContentUsed,
    required this.month,
  });

  factory UsageInfo.fromJson(Map<String, dynamic> json) => _$UsageInfoFromJson(json);
  Map<String, dynamic> toJson() => _$UsageInfoToJson(this);
}
