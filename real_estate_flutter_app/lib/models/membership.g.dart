// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'membership.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Membership _$MembershipFromJson(Map<String, dynamic> json) => Membership(
  id: (json['id'] as num?)?.toInt(),
  name: json['name'] as String?,
  description: json['description'] as String?,
  type: json['type'] as String?,
  price: (json['price'] as num?)?.toDouble(),
  durationMonths: (json['durationMonths'] as num?)?.toInt(),
  maxProperties: (json['maxProperties'] as num?)?.toInt(),
  featuredProperties: (json['featuredProperties'] as num?)?.toInt(),
  multipleImages: json['multipleImages'] as bool?,
  contactInfoDisplay: json['contactInfoDisplay'] as bool?,
  prioritySupport: json['prioritySupport'] as bool?,
  analyticsAccess: json['analyticsAccess'] as bool?,
  isActive: json['isActive'] as bool?,
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$MembershipToJson(Membership instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'type': instance.type,
      'price': instance.price,
      'durationMonths': instance.durationMonths,
      'maxProperties': instance.maxProperties,
      'featuredProperties': instance.featuredProperties,
      'multipleImages': instance.multipleImages,
      'contactInfoDisplay': instance.contactInfoDisplay,
      'prioritySupport': instance.prioritySupport,
      'analyticsAccess': instance.analyticsAccess,
      'isActive': instance.isActive,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

UserMembership _$UserMembershipFromJson(Map<String, dynamic> json) =>
    UserMembership(
      id: (json['id'] as num).toInt(),
      userId: (json['userId'] as num).toInt(),
      membershipId: (json['membershipId'] as num).toInt(),
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      status: json['status'] as String,
      membership: Membership.fromJson(
        json['membership'] as Map<String, dynamic>,
      ),
    );

Map<String, dynamic> _$UserMembershipToJson(UserMembership instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'membershipId': instance.membershipId,
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate.toIso8601String(),
      'status': instance.status,
      'membership': instance.membership,
    };
