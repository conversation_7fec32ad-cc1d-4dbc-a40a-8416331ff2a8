// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'boost.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BoostStatus _$BoostStatusFromJson(Map<String, dynamic> json) => BoostStatus(
  canBoost: json['canBoost'] as bool,
  message: json['message'] as String,
  remainingBoosts: (json['remainingBoosts'] as num).toInt(),
  nextResetDate: json['nextResetDate'] == null
      ? null
      : DateTime.parse(json['nextResetDate'] as String),
);

Map<String, dynamic> _$BoostStatusToJson(BoostStatus instance) =>
    <String, dynamic>{
      'canBoost': instance.canBoost,
      'message': instance.message,
      'remainingBoosts': instance.remainingBoosts,
      'nextResetDate': instance.nextResetDate?.toIso8601String(),
    };

BoostResponse _$BoostResponseFromJson(Map<String, dynamic> json) =>
    BoostResponse(
      success: json['success'] as bool,
      message: json['message'] as String,
      boostInfo: json['boostInfo'] == null
          ? null
          : BoostInfo.fromJson(json['boostInfo'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$BoostResponseToJson(BoostResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'boostInfo': instance.boostInfo,
    };

BoostInfo _$BoostInfoFromJson(Map<String, dynamic> json) => BoostInfo(
  propertyId: (json['propertyId'] as num).toInt(),
  boostedAt: DateTime.parse(json['boostedAt'] as String),
  expiresAt: DateTime.parse(json['expiresAt'] as String),
  status: json['status'] as String,
);

Map<String, dynamic> _$BoostInfoToJson(BoostInfo instance) => <String, dynamic>{
  'propertyId': instance.propertyId,
  'boostedAt': instance.boostedAt.toIso8601String(),
  'expiresAt': instance.expiresAt.toIso8601String(),
  'status': instance.status,
};

BoostHistory _$BoostHistoryFromJson(Map<String, dynamic> json) => BoostHistory(
  content: (json['content'] as List<dynamic>)
      .map((e) => BoostHistoryItem.fromJson(e as Map<String, dynamic>))
      .toList(),
  totalElements: (json['totalElements'] as num).toInt(),
  totalPages: (json['totalPages'] as num).toInt(),
  number: (json['number'] as num).toInt(),
  size: (json['size'] as num).toInt(),
  first: json['first'] as bool,
  last: json['last'] as bool,
);

Map<String, dynamic> _$BoostHistoryToJson(BoostHistory instance) =>
    <String, dynamic>{
      'content': instance.content,
      'totalElements': instance.totalElements,
      'totalPages': instance.totalPages,
      'number': instance.number,
      'size': instance.size,
      'first': instance.first,
      'last': instance.last,
    };

BoostHistoryItem _$BoostHistoryItemFromJson(Map<String, dynamic> json) =>
    BoostHistoryItem(
      id: (json['id'] as num).toInt(),
      propertyId: (json['propertyId'] as num).toInt(),
      propertyTitle: json['propertyTitle'] as String,
      boostedAt: DateTime.parse(json['boostedAt'] as String),
      expiresAt: DateTime.parse(json['expiresAt'] as String),
      status: json['status'] as String,
      cost: (json['cost'] as num).toDouble(),
    );

Map<String, dynamic> _$BoostHistoryItemToJson(BoostHistoryItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'propertyId': instance.propertyId,
      'propertyTitle': instance.propertyTitle,
      'boostedAt': instance.boostedAt.toIso8601String(),
      'expiresAt': instance.expiresAt.toIso8601String(),
      'status': instance.status,
      'cost': instance.cost,
    };
