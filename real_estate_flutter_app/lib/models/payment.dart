import 'package:json_annotation/json_annotation.dart';

part 'payment.g.dart';

@JsonSerializable()
class PaymentMethod {
  final String id;
  final String name;
  final String type;
  final String description;
  final bool isActive;
  final String? iconUrl;
  final Map<String, dynamic>? config;

  PaymentMethod({
    required this.id,
    required this.name,
    required this.type,
    required this.description,
    required this.isActive,
    this.iconUrl,
    this.config,
  });

  factory PaymentMethod.fromJson(Map<String, dynamic> json) => _$PaymentMethodFromJson(json);
  Map<String, dynamic> toJson() => _$PaymentMethodToJson(this);
}

@JsonSerializable()
class PaymentRequest {
  final int membershipId;
  final String paymentMethod;
  final String? currency;
  final Map<String, dynamic>? metadata;

  PaymentRequest({
    required this.membershipId,
    required this.paymentMethod,
    this.currency = 'VND',
    this.metadata,
  });

  factory PaymentRequest.fromJson(Map<String, dynamic> json) => _$PaymentRequestFromJson(json);
  Map<String, dynamic> toJson() => _$PaymentRequestToJson(this);
}

@JsonSerializable()
class PaymentResponse {
  final bool success;
  final String message;
  final String? paymentId;
  final String? clientSecret;
  final String? paymentUrl;
  final String? status;
  final PaymentDetails? paymentDetails;

  PaymentResponse({
    required this.success,
    required this.message,
    this.paymentId,
    this.clientSecret,
    this.paymentUrl,
    this.status,
    this.paymentDetails,
  });

  factory PaymentResponse.fromJson(Map<String, dynamic> json) => _$PaymentResponseFromJson(json);
  Map<String, dynamic> toJson() => _$PaymentResponseToJson(this);
}

@JsonSerializable()
class PaymentDetails {
  final String id;
  final double amount;
  final String currency;
  final String status;
  final String paymentMethod;
  final int membershipId;
  final String membershipName;
  final DateTime createdAt;
  final DateTime? paidAt;
  final Map<String, dynamic>? metadata;

  PaymentDetails({
    required this.id,
    required this.amount,
    required this.currency,
    required this.status,
    required this.paymentMethod,
    required this.membershipId,
    required this.membershipName,
    required this.createdAt,
    this.paidAt,
    this.metadata,
  });

  factory PaymentDetails.fromJson(Map<String, dynamic> json) => _$PaymentDetailsFromJson(json);
  Map<String, dynamic> toJson() => _$PaymentDetailsToJson(this);

  String get formattedAmount {
    if (currency == 'VND') {
      if (amount >= 1000000) {
        return '${(amount / 1000000).toStringAsFixed(1)} triệu VND';
      } else {
        return '${amount.toStringAsFixed(0)} VND';
      }
    }
    return '${amount.toStringAsFixed(2)} $currency';
  }

  String get statusText {
    switch (status.toUpperCase()) {
      case 'PENDING':
        return 'Đang xử lý';
      case 'COMPLETED':
        return 'Thành công';
      case 'FAILED':
        return 'Thất bại';
      case 'CANCELLED':
        return 'Đã hủy';
      case 'REFUNDED':
        return 'Đã hoàn tiền';
      default:
        return status;
    }
  }
}

@JsonSerializable()
class PaymentHistory {
  final List<PaymentDetails> content;
  final int totalElements;
  final int totalPages;
  final int number;
  final int size;
  final bool first;
  final bool last;

  PaymentHistory({
    required this.content,
    required this.totalElements,
    required this.totalPages,
    required this.number,
    required this.size,
    required this.first,
    required this.last,
  });

  factory PaymentHistory.fromJson(Map<String, dynamic> json) => _$PaymentHistoryFromJson(json);
  Map<String, dynamic> toJson() => _$PaymentHistoryToJson(this);
}

@JsonSerializable()
class ExchangeRate {
  final String fromCurrency;
  final String toCurrency;
  final double rate;
  final DateTime updatedAt;

  ExchangeRate({
    required this.fromCurrency,
    required this.toCurrency,
    required this.rate,
    required this.updatedAt,
  });

  factory ExchangeRate.fromJson(Map<String, dynamic> json) => _$ExchangeRateFromJson(json);
  Map<String, dynamic> toJson() => _$ExchangeRateToJson(this);
}

@JsonSerializable()
class StripeConfig {
  final String publishableKey;
  final String? merchantId;
  final String? countryCode;

  StripeConfig({
    required this.publishableKey,
    this.merchantId,
    this.countryCode = 'VN',
  });

  factory StripeConfig.fromJson(Map<String, dynamic> json) => _$StripeConfigFromJson(json);
  Map<String, dynamic> toJson() => _$StripeConfigToJson(this);
}
