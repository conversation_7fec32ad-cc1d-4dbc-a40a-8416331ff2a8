import 'package:json_annotation/json_annotation.dart';

part 'boost.g.dart';

@JsonSerializable()
class BoostStatus {
  final bool canBoost;
  final String message;
  final int remainingBoosts;
  final DateTime? nextResetDate;

  BoostStatus({
    required this.canBoost,
    required this.message,
    required this.remainingBoosts,
    this.nextResetDate,
  });

  factory BoostStatus.fromJson(Map<String, dynamic> json) => _$BoostStatusFromJson(json);
  Map<String, dynamic> toJson() => _$BoostStatusToJson(this);
}

@JsonSerializable()
class BoostResponse {
  final bool success;
  final String message;
  final BoostInfo? boostInfo;

  BoostResponse({
    required this.success,
    required this.message,
    this.boostInfo,
  });

  factory BoostResponse.fromJson(Map<String, dynamic> json) => _$BoostResponseFromJson(json);
  Map<String, dynamic> toJson() => _$BoostResponseToJson(this);
}

@JsonSerializable()
class BoostInfo {
  final int propertyId;
  final DateTime boostedAt;
  final DateTime expiresAt;
  final String status;

  BoostInfo({
    required this.propertyId,
    required this.boostedAt,
    required this.expiresAt,
    required this.status,
  });

  factory BoostInfo.fromJson(Map<String, dynamic> json) => _$BoostInfoFromJson(json);
  Map<String, dynamic> toJson() => _$BoostInfoToJson(this);
}

@JsonSerializable()
class BoostHistory {
  final List<BoostHistoryItem> content;
  final int totalElements;
  final int totalPages;
  final int number;
  final int size;
  final bool first;
  final bool last;

  BoostHistory({
    required this.content,
    required this.totalElements,
    required this.totalPages,
    required this.number,
    required this.size,
    required this.first,
    required this.last,
  });

  factory BoostHistory.fromJson(Map<String, dynamic> json) => _$BoostHistoryFromJson(json);
  Map<String, dynamic> toJson() => _$BoostHistoryToJson(this);
}

@JsonSerializable()
class BoostHistoryItem {
  final int id;
  final int propertyId;
  final String propertyTitle;
  final DateTime boostedAt;
  final DateTime expiresAt;
  final String status;
  final double cost;

  BoostHistoryItem({
    required this.id,
    required this.propertyId,
    required this.propertyTitle,
    required this.boostedAt,
    required this.expiresAt,
    required this.status,
    required this.cost,
  });

  factory BoostHistoryItem.fromJson(Map<String, dynamic> json) => _$BoostHistoryItemFromJson(json);
  Map<String, dynamic> toJson() => _$BoostHistoryItemToJson(this);

  bool get isActive {
    final now = DateTime.now();
    return status == 'ACTIVE' && now.isBefore(expiresAt);
  }

  Duration get remainingTime {
    final now = DateTime.now();
    if (now.isBefore(expiresAt)) {
      return expiresAt.difference(now);
    }
    return Duration.zero;
  }
}
