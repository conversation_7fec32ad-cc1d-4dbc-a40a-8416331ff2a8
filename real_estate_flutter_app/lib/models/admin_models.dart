import 'package:json_annotation/json_annotation.dart';

part 'admin_models.g.dart';

@JsonSerializable()
class AdminProperty {
  final int? id;
  final String? title;
  final String? description;
  final double? price;
  final String? address;
  final String? city;
  final String? district;
  final String? ward;
  final String? propertyType;
  final String? listingType;
  final int? bedrooms;
  final int? bathrooms;
  final double? propertyArea;
  final String? status; // PENDING, APPROVED, REJECTED
  final String? ownerName;
  final String? ownerEmail;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? rejectionReason;

  AdminProperty({
    this.id,
    this.title,
    this.description,
    this.price,
    this.address,
    this.city,
    this.district,
    this.ward,
    this.propertyType,
    this.listingType,
    this.bedrooms,
    this.bathrooms,
    this.propertyArea,
    this.status,
    this.ownerName,
    this.ownerEmail,
    this.createdAt,
    this.updatedAt,
    this.rejectionReason,
  });

  factory AdminProperty.fromJson(Map<String, dynamic> json) => _$AdminPropertyFromJson(json);
  Map<String, dynamic> toJson() => _$AdminPropertyToJson(this);

  String get statusDisplay {
    switch (status) {
      case 'PENDING':
        return 'Chờ duyệt';
      case 'APPROVED':
        return 'Đã duyệt';
      case 'REJECTED':
        return 'Từ chối';
      default:
        return 'Không xác định';
    }
  }
}

@JsonSerializable()
class AdminUser {
  final int? id;
  final String? username;
  final String? email;
  final String? fullName;
  final String? phone;
  final String? status; // ACTIVE, BANNED
  final DateTime? createdAt;
  final DateTime? lastLogin;
  final int? totalProperties;
  final String? membershipType;

  AdminUser({
    this.id,
    this.username,
    this.email,
    this.fullName,
    this.phone,
    this.status,
    this.createdAt,
    this.lastLogin,
    this.totalProperties,
    this.membershipType,
  });

  factory AdminUser.fromJson(Map<String, dynamic> json) => _$AdminUserFromJson(json);
  Map<String, dynamic> toJson() => _$AdminUserToJson(this);

  String get statusDisplay {
    switch (status) {
      case 'ACTIVE':
        return 'Hoạt động';
      case 'BANNED':
        return 'Bị cấm';
      default:
        return 'Không xác định';
    }
  }
}

@JsonSerializable()
class AdminPayment {
  final int? id;
  final String? orderId;
  final String? username;
  final String? membershipName;
  final double? amount;
  final String? currency;
  final String? status; // PENDING, COMPLETED, FAILED, CANCELLED
  final String? paymentMethod;
  final DateTime? createdAt;
  final DateTime? completedAt;

  AdminPayment({
    this.id,
    this.orderId,
    this.username,
    this.membershipName,
    this.amount,
    this.currency,
    this.status,
    this.paymentMethod,
    this.createdAt,
    this.completedAt,
  });

  factory AdminPayment.fromJson(Map<String, dynamic> json) => _$AdminPaymentFromJson(json);
  Map<String, dynamic> toJson() => _$AdminPaymentToJson(this);

  String get statusDisplay {
    switch (status) {
      case 'PENDING':
        return 'Chờ xử lý';
      case 'COMPLETED':
        return 'Hoàn thành';
      case 'FAILED':
        return 'Thất bại';
      case 'CANCELLED':
        return 'Đã hủy';
      default:
        return 'Không xác định';
    }
  }

  String get formattedAmount {
    if (amount == null) return 'N/A';
    if (currency == 'VND') {
      return '${amount!.toStringAsFixed(0)} VND';
    } else {
      return '${amount!.toStringAsFixed(2)} ${currency ?? ''}';
    }
  }
}

@JsonSerializable()
class AdminStats {
  final int? totalUsers;
  final int? totalProperties;
  final int? pendingProperties;
  final int? totalPayments;
  final double? totalRevenue;
  final int? activeUsers;
  final int? bannedUsers;

  AdminStats({
    this.totalUsers,
    this.totalProperties,
    this.pendingProperties,
    this.totalPayments,
    this.totalRevenue,
    this.activeUsers,
    this.bannedUsers,
  });

  factory AdminStats.fromJson(Map<String, dynamic> json) => _$AdminStatsFromJson(json);
  Map<String, dynamic> toJson() => _$AdminStatsToJson(this);
}

@JsonSerializable()
class PropertyApprovalRequest {
  final String action; // APPROVE, REJECT
  final String? rejectionReason;

  PropertyApprovalRequest({
    required this.action,
    this.rejectionReason,
  });

  factory PropertyApprovalRequest.fromJson(Map<String, dynamic> json) => _$PropertyApprovalRequestFromJson(json);
  Map<String, dynamic> toJson() => _$PropertyApprovalRequestToJson(this);
}

@JsonSerializable()
class UserBanRequest {
  final String action; // BAN, UNBAN
  final String? reason;

  UserBanRequest({
    required this.action,
    this.reason,
  });

  factory UserBanRequest.fromJson(Map<String, dynamic> json) => _$UserBanRequestFromJson(json);
  Map<String, dynamic> toJson() => _$UserBanRequestToJson(this);
}
