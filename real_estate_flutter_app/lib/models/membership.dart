import 'package:json_annotation/json_annotation.dart';

part 'membership.g.dart';

@JsonSerializable()
class Membership {
  final int? id;
  final String? name;
  final String? description;
  final String? type;
  final double? price;
  final int? durationMonths;
  final int? maxProperties;
  final int? featuredProperties;
  final bool? multipleImages;
  final bool? contactInfoDisplay;
  final bool? prioritySupport;
  final bool? analyticsAccess;
  final bool? isActive;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  Membership({
    this.id,
    this.name,
    this.description,
    this.type,
    this.price,
    this.durationMonths,
    this.maxProperties,
    this.featuredProperties,
    this.multipleImages,
    this.contactInfoDisplay,
    this.prioritySupport,
    this.analyticsAccess,
    this.isActive,
    this.createdAt,
    this.updatedAt,
  });

  factory Membership.fromJson(Map<String, dynamic> json) => _$MembershipFromJson(json);
  Map<String, dynamic> toJson() => _$MembershipToJson(this);

  String get formattedPrice {
    if (price == null) return 'Liên hệ';
    if (price! >= 1000000) {
      return '${(price! / 1000000).toStringAsFixed(1)} triệu VND';
    } else {
      return '${price!.toStringAsFixed(0)} VND';
    }
  }
}

@JsonSerializable()
class UserMembership {
  final int id;
  final int userId;
  final int membershipId;
  final DateTime startDate;
  final DateTime endDate;
  final String status;
  final Membership membership;

  UserMembership({
    required this.id,
    required this.userId,
    required this.membershipId,
    required this.startDate,
    required this.endDate,
    required this.status,
    required this.membership,
  });

  factory UserMembership.fromJson(Map<String, dynamic> json) => _$UserMembershipFromJson(json);
  Map<String, dynamic> toJson() => _$UserMembershipToJson(this);

  bool get isActive {
    final now = DateTime.now();
    return status == 'ACTIVE' && now.isAfter(startDate) && now.isBefore(endDate);
  }

  bool get isExpired {
    final now = DateTime.now();
    return now.isAfter(endDate);
  }
}
