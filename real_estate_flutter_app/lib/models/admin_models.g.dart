// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admin_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AdminProperty _$AdminPropertyFromJson(Map<String, dynamic> json) =>
    AdminProperty(
      id: (json['id'] as num?)?.toInt(),
      title: json['title'] as String?,
      description: json['description'] as String?,
      price: (json['price'] as num?)?.toDouble(),
      address: json['address'] as String?,
      city: json['city'] as String?,
      district: json['district'] as String?,
      ward: json['ward'] as String?,
      propertyType: json['propertyType'] as String?,
      listingType: json['listingType'] as String?,
      bedrooms: (json['bedrooms'] as num?)?.toInt(),
      bathrooms: (json['bathrooms'] as num?)?.toInt(),
      propertyArea: (json['propertyArea'] as num?)?.toDouble(),
      status: json['status'] as String?,
      ownerName: json['ownerName'] as String?,
      ownerEmail: json['ownerEmail'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      rejectionReason: json['rejectionReason'] as String?,
    );

Map<String, dynamic> _$AdminPropertyToJson(AdminProperty instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'price': instance.price,
      'address': instance.address,
      'city': instance.city,
      'district': instance.district,
      'ward': instance.ward,
      'propertyType': instance.propertyType,
      'listingType': instance.listingType,
      'bedrooms': instance.bedrooms,
      'bathrooms': instance.bathrooms,
      'propertyArea': instance.propertyArea,
      'status': instance.status,
      'ownerName': instance.ownerName,
      'ownerEmail': instance.ownerEmail,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'rejectionReason': instance.rejectionReason,
    };

AdminUser _$AdminUserFromJson(Map<String, dynamic> json) => AdminUser(
  id: (json['id'] as num?)?.toInt(),
  username: json['username'] as String?,
  email: json['email'] as String?,
  fullName: json['fullName'] as String?,
  phone: json['phone'] as String?,
  status: json['status'] as String?,
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  lastLogin: json['lastLogin'] == null
      ? null
      : DateTime.parse(json['lastLogin'] as String),
  totalProperties: (json['totalProperties'] as num?)?.toInt(),
  membershipType: json['membershipType'] as String?,
);

Map<String, dynamic> _$AdminUserToJson(AdminUser instance) => <String, dynamic>{
  'id': instance.id,
  'username': instance.username,
  'email': instance.email,
  'fullName': instance.fullName,
  'phone': instance.phone,
  'status': instance.status,
  'createdAt': instance.createdAt?.toIso8601String(),
  'lastLogin': instance.lastLogin?.toIso8601String(),
  'totalProperties': instance.totalProperties,
  'membershipType': instance.membershipType,
};

AdminPayment _$AdminPaymentFromJson(Map<String, dynamic> json) => AdminPayment(
  id: (json['id'] as num?)?.toInt(),
  orderId: json['orderId'] as String?,
  username: json['username'] as String?,
  membershipName: json['membershipName'] as String?,
  amount: (json['amount'] as num?)?.toDouble(),
  currency: json['currency'] as String?,
  status: json['status'] as String?,
  paymentMethod: json['paymentMethod'] as String?,
  createdAt: json['createdAt'] == null
      ? null
      : DateTime.parse(json['createdAt'] as String),
  completedAt: json['completedAt'] == null
      ? null
      : DateTime.parse(json['completedAt'] as String),
);

Map<String, dynamic> _$AdminPaymentToJson(AdminPayment instance) =>
    <String, dynamic>{
      'id': instance.id,
      'orderId': instance.orderId,
      'username': instance.username,
      'membershipName': instance.membershipName,
      'amount': instance.amount,
      'currency': instance.currency,
      'status': instance.status,
      'paymentMethod': instance.paymentMethod,
      'createdAt': instance.createdAt?.toIso8601String(),
      'completedAt': instance.completedAt?.toIso8601String(),
    };

AdminStats _$AdminStatsFromJson(Map<String, dynamic> json) => AdminStats(
  totalUsers: (json['totalUsers'] as num?)?.toInt(),
  totalProperties: (json['totalProperties'] as num?)?.toInt(),
  pendingProperties: (json['pendingProperties'] as num?)?.toInt(),
  totalPayments: (json['totalPayments'] as num?)?.toInt(),
  totalRevenue: (json['totalRevenue'] as num?)?.toDouble(),
  activeUsers: (json['activeUsers'] as num?)?.toInt(),
  bannedUsers: (json['bannedUsers'] as num?)?.toInt(),
);

Map<String, dynamic> _$AdminStatsToJson(AdminStats instance) =>
    <String, dynamic>{
      'totalUsers': instance.totalUsers,
      'totalProperties': instance.totalProperties,
      'pendingProperties': instance.pendingProperties,
      'totalPayments': instance.totalPayments,
      'totalRevenue': instance.totalRevenue,
      'activeUsers': instance.activeUsers,
      'bannedUsers': instance.bannedUsers,
    };

PropertyApprovalRequest _$PropertyApprovalRequestFromJson(
  Map<String, dynamic> json,
) => PropertyApprovalRequest(
  action: json['action'] as String,
  rejectionReason: json['rejectionReason'] as String?,
);

Map<String, dynamic> _$PropertyApprovalRequestToJson(
  PropertyApprovalRequest instance,
) => <String, dynamic>{
  'action': instance.action,
  'rejectionReason': instance.rejectionReason,
};

UserBanRequest _$UserBanRequestFromJson(Map<String, dynamic> json) =>
    UserBanRequest(
      action: json['action'] as String,
      reason: json['reason'] as String?,
    );

Map<String, dynamic> _$UserBanRequestToJson(UserBanRequest instance) =>
    <String, dynamic>{'action': instance.action, 'reason': instance.reason};
