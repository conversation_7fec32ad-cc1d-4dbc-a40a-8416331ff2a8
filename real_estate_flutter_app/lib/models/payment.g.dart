// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PaymentMethod _$PaymentMethodFromJson(Map<String, dynamic> json) =>
    PaymentMethod(
      id: json['id'] as String,
      name: json['name'] as String,
      type: json['type'] as String,
      description: json['description'] as String,
      isActive: json['isActive'] as bool,
      iconUrl: json['iconUrl'] as String?,
      config: json['config'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$PaymentMethodToJson(PaymentMethod instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': instance.type,
      'description': instance.description,
      'isActive': instance.isActive,
      'iconUrl': instance.iconUrl,
      'config': instance.config,
    };

PaymentRequest _$PaymentRequestFromJson(Map<String, dynamic> json) =>
    PaymentRequest(
      membershipId: (json['membershipId'] as num).toInt(),
      paymentMethod: json['paymentMethod'] as String,
      currency: json['currency'] as String? ?? 'VND',
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$PaymentRequestToJson(PaymentRequest instance) =>
    <String, dynamic>{
      'membershipId': instance.membershipId,
      'paymentMethod': instance.paymentMethod,
      'currency': instance.currency,
      'metadata': instance.metadata,
    };

PaymentResponse _$PaymentResponseFromJson(Map<String, dynamic> json) =>
    PaymentResponse(
      success: json['success'] as bool,
      message: json['message'] as String,
      paymentId: json['paymentId'] as String?,
      clientSecret: json['clientSecret'] as String?,
      paymentUrl: json['paymentUrl'] as String?,
      status: json['status'] as String?,
      paymentDetails: json['paymentDetails'] == null
          ? null
          : PaymentDetails.fromJson(
              json['paymentDetails'] as Map<String, dynamic>,
            ),
    );

Map<String, dynamic> _$PaymentResponseToJson(PaymentResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'paymentId': instance.paymentId,
      'clientSecret': instance.clientSecret,
      'paymentUrl': instance.paymentUrl,
      'status': instance.status,
      'paymentDetails': instance.paymentDetails,
    };

PaymentDetails _$PaymentDetailsFromJson(Map<String, dynamic> json) =>
    PaymentDetails(
      id: json['id'] as String,
      amount: (json['amount'] as num).toDouble(),
      currency: json['currency'] as String,
      status: json['status'] as String,
      paymentMethod: json['paymentMethod'] as String,
      membershipId: (json['membershipId'] as num).toInt(),
      membershipName: json['membershipName'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      paidAt: json['paidAt'] == null
          ? null
          : DateTime.parse(json['paidAt'] as String),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$PaymentDetailsToJson(PaymentDetails instance) =>
    <String, dynamic>{
      'id': instance.id,
      'amount': instance.amount,
      'currency': instance.currency,
      'status': instance.status,
      'paymentMethod': instance.paymentMethod,
      'membershipId': instance.membershipId,
      'membershipName': instance.membershipName,
      'createdAt': instance.createdAt.toIso8601String(),
      'paidAt': instance.paidAt?.toIso8601String(),
      'metadata': instance.metadata,
    };

PaymentHistory _$PaymentHistoryFromJson(Map<String, dynamic> json) =>
    PaymentHistory(
      content: (json['content'] as List<dynamic>)
          .map((e) => PaymentDetails.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalElements: (json['totalElements'] as num).toInt(),
      totalPages: (json['totalPages'] as num).toInt(),
      number: (json['number'] as num).toInt(),
      size: (json['size'] as num).toInt(),
      first: json['first'] as bool,
      last: json['last'] as bool,
    );

Map<String, dynamic> _$PaymentHistoryToJson(PaymentHistory instance) =>
    <String, dynamic>{
      'content': instance.content,
      'totalElements': instance.totalElements,
      'totalPages': instance.totalPages,
      'number': instance.number,
      'size': instance.size,
      'first': instance.first,
      'last': instance.last,
    };

ExchangeRate _$ExchangeRateFromJson(Map<String, dynamic> json) => ExchangeRate(
  fromCurrency: json['fromCurrency'] as String,
  toCurrency: json['toCurrency'] as String,
  rate: (json['rate'] as num).toDouble(),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$ExchangeRateToJson(ExchangeRate instance) =>
    <String, dynamic>{
      'fromCurrency': instance.fromCurrency,
      'toCurrency': instance.toCurrency,
      'rate': instance.rate,
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

StripeConfig _$StripeConfigFromJson(Map<String, dynamic> json) => StripeConfig(
  publishableKey: json['publishableKey'] as String,
  merchantId: json['merchantId'] as String?,
  countryCode: json['countryCode'] as String? ?? 'VN',
);

Map<String, dynamic> _$StripeConfigToJson(StripeConfig instance) =>
    <String, dynamic>{
      'publishableKey': instance.publishableKey,
      'merchantId': instance.merchantId,
      'countryCode': instance.countryCode,
    };
