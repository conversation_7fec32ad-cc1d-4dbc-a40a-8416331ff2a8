// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ai_content.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AiContentRequest _$AiContentRequestFromJson(Map<String, dynamic> json) =>
    AiContentRequest(
      description: json['description'] as String,
      imageDescription: json['imageDescription'] as String?,
    );

Map<String, dynamic> _$AiContentRequestToJson(AiContentRequest instance) =>
    <String, dynamic>{
      'description': instance.description,
      'imageDescription': instance.imageDescription,
    };

AiContentResponse _$AiContentResponseFromJson(Map<String, dynamic> json) =>
    AiContentResponse(
      success: json['success'] as bool,
      content: AiContent.fromJson(json['content'] as Map<String, dynamic>),
      usage: UsageInfo.fromJson(json['usage'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$AiContentResponseToJson(AiContentResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'content': instance.content,
      'usage': instance.usage,
    };

AiContent _$AiContentFromJson(Map<String, dynamic> json) => AiContent(
  seoOptimizedDescription: json['seoOptimizedDescription'] as String,
  titleSuggestions: (json['titleSuggestions'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  metaDescription: json['metaDescription'] as String,
  seoScore: (json['seoScore'] as num).toInt(),
  keywords: (json['keywords'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
);

Map<String, dynamic> _$AiContentToJson(AiContent instance) => <String, dynamic>{
  'seoOptimizedDescription': instance.seoOptimizedDescription,
  'titleSuggestions': instance.titleSuggestions,
  'metaDescription': instance.metaDescription,
  'seoScore': instance.seoScore,
  'keywords': instance.keywords,
};

UsageInfo _$UsageInfoFromJson(Map<String, dynamic> json) => UsageInfo(
  aiContentUsed: (json['aiContentUsed'] as num).toInt(),
  month: json['month'] as String,
);

Map<String, dynamic> _$UsageInfoToJson(UsageInfo instance) => <String, dynamic>{
  'aiContentUsed': instance.aiContentUsed,
  'month': instance.month,
};
