import 'package:json_annotation/json_annotation.dart';

part 'property.g.dart';

@JsonSerializable()
class Property {
  final int? id;
  final String? title;
  final String? description;
  final double? price;
  final String? address;
  final String? city;
  final String? district;
  final String? ward;
  final String? propertyType;
  final String? listingType;
  final int? bedrooms;
  final int? bathrooms;
  final double? propertyArea;
  final String? status;
  final int? categoryId;
  final int? ownerId;
  final List<String>? imageUrls;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  Property({
    this.id,
    this.title,
    this.description,
    this.price,
    this.address,
    this.city,
    this.district,
    this.ward,
    this.propertyType,
    this.listingType,
    this.bedrooms,
    this.bathrooms,
    this.propertyArea,
    this.status,
    this.categoryId,
    this.ownerId,
    this.imageUrls,
    this.createdAt,
    this.updatedAt,
  });

  factory Property.fromJson(Map<String, dynamic> json) => _$PropertyFromJson(json);
  Map<String, dynamic> toJson() => _$PropertyToJson(this);

  String get formattedPrice {
    if (price == null) return 'Liên hệ';
    if (price! >= 1000000000) {
      return '${(price! / 1000000000).toStringAsFixed(1)} tỷ VND';
    } else if (price! >= 1000000) {
      return '${(price! / 1000000).toStringAsFixed(1)} triệu VND';
    } else {
      return '${price!.toStringAsFixed(0)} VND';
    }
  }

  String get fullAddress {
    final addressParts = [address, ward, district, city]
        .where((part) => part != null && part.isNotEmpty)
        .join(', ');
    return addressParts.isNotEmpty ? addressParts : 'Địa chỉ không xác định';
  }
}

@JsonSerializable()
class PropertyCreateRequest {
  final String title;
  final String description;
  final double price;
  final String address;
  final String city;
  final String district;
  final String ward;
  final String propertyType;
  final String listingType;
  final int bedrooms;
  final int bathrooms;
  final double propertyArea;
  final int categoryId;

  PropertyCreateRequest({
    required this.title,
    required this.description,
    required this.price,
    required this.address,
    required this.city,
    required this.district,
    required this.ward,
    required this.propertyType,
    required this.listingType,
    required this.bedrooms,
    required this.bathrooms,
    required this.propertyArea,
    required this.categoryId,
  });

  factory PropertyCreateRequest.fromJson(Map<String, dynamic> json) => _$PropertyCreateRequestFromJson(json);
  Map<String, dynamic> toJson() => _$PropertyCreateRequestToJson(this);
}

@JsonSerializable()
class PropertySearchRequest {
  final String? keyword;
  final String? city;
  final String? district;
  final String? propertyType;
  final String? listingType;
  final double? minPrice;
  final double? maxPrice;
  final double? minArea;
  final double? maxArea;
  final int? bedrooms;
  final int? bathrooms;

  PropertySearchRequest({
    this.keyword,
    this.city,
    this.district,
    this.propertyType,
    this.listingType,
    this.minPrice,
    this.maxPrice,
    this.minArea,
    this.maxArea,
    this.bedrooms,
    this.bathrooms,
  });

  factory PropertySearchRequest.fromJson(Map<String, dynamic> json) => _$PropertySearchRequestFromJson(json);
  Map<String, dynamic> toJson() => _$PropertySearchRequestToJson(this);
}
