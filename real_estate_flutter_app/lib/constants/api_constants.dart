class ApiConstants {
  // Base URL for the API
  static const String baseUrl = 'http://localhost:8080/api/v1';
  
  // Authentication endpoints
  static const String login = '$baseUrl/auth/login';
  static const String register = '$baseUrl/auth/register';
  static const String forgotPassword = '$baseUrl/auth/forgot-password';
  static const String resetPassword = '$baseUrl/auth/reset-password';
  
  // Public endpoints
  static const String memberships = '$baseUrl/memberships';
  static const String categories = '$baseUrl/categories';
  static const String properties = '$baseUrl/properties';
  static const String propertiesSearch = '$baseUrl/properties/search';
  static const String paymentMethods = '$baseUrl/payments/methods';
  static const String exchangeRate = '$baseUrl/payments/exchange-rate';
  
  // User authenticated endpoints
  static const String userProfile = '$baseUrl/users/profile';
  static const String notifications = '$baseUrl/notifications';
  static const String notificationsUnreadCount = '$baseUrl/notifications/unread/count';
  static const String notificationsMarkAllRead = '$baseUrl/notifications/mark-all-read';
  static const String userMembership = '$baseUrl/memberships/my-membership';
  static const String oauthUserInfo = '$baseUrl/oauth/user-info';
  static const String userProperties = '$baseUrl/properties/my-properties';
  static const String userFavorites = '$baseUrl/users/favorites';

  // Dashboard endpoints
  static const String userDashboard = '$baseUrl/dashboard';
  static const String seoScore = '$baseUrl/dashboard/seo/score';

  // Membership endpoints
  static const String purchaseMembership = '$baseUrl/memberships/purchase';

  // Push Top / Boost endpoints
  static const String boostStatus = '$baseUrl/properties/boost/status';
  static const String pushTop = '$baseUrl/properties/boost';
  static const String boostHistory = '$baseUrl/properties/boost/history';
  static const String activeBoosts = '$baseUrl/properties/boost/active';

  // AI Content Generation
  static const String aiContentGenerate = '$baseUrl/ai-content/generate';

  // Payment endpoints
  static const String createPayment = '$baseUrl/payments/create';
  static const String paymentHistory = '$baseUrl/payments/my-payments';
  static const String paymentStatus = '$baseUrl/payments/status';

  // Chatbot endpoints
  static const String chatSessions = '$baseUrl/chatbot/sessions';
  
  // Admin endpoints
  static const String adminDashboard = '$baseUrl/admin/stats/dashboard';
  static const String adminUsers = '$baseUrl/admin/users';
  static const String adminProperties = '$baseUrl/admin/properties';
  static const String adminPayments = '$baseUrl/admin/payments';
  static const String adminNotifications = '$baseUrl/admin/notifications';
  static const String adminApproveProperty = '$baseUrl/admin/properties';
  static const String adminRejectProperty = '$baseUrl/admin/properties';
  
  // HTTP Headers
  static const Map<String, String> headers = {
    'Content-Type': 'application/json',
  };
  
  static Map<String, String> headersWithAuth(String token) {
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
    };
  }
}
