import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/property_provider.dart';
import '../../providers/payment_provider.dart';
import '../../models/membership.dart';
import '../payment/payment_screen.dart';

class MembershipScreen extends StatefulWidget {
  const MembershipScreen({super.key});

  @override
  State<MembershipScreen> createState() => _MembershipScreenState();
}

class _MembershipScreenState extends State<MembershipScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<PropertyProvider>(context, listen: false).loadMemberships();
      Provider.of<PaymentProvider>(context, listen: false).loadPaymentMethods();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('<PERSON><PERSON><PERSON> thành viên'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Consumer<PropertyProvider>(
        builder: (context, propertyProvider, child) {
          if (propertyProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (propertyProvider.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Lỗi: ${propertyProvider.errorMessage}',
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      propertyProvider.loadMemberships();
                    },
                    child: const Text('Thử lại'),
                  ),
                ],
              ),
            );
          }

          final memberships = propertyProvider.memberships;
          if (memberships.isEmpty) {
            return const Center(
              child: Text('Không có gói thành viên nào'),
            );
          }

          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: memberships.length,
            itemBuilder: (context, index) {
              final membership = memberships[index];
              return _buildMembershipCard(context, membership);
            },
          );
        },
      ),
    );
  }

  Widget _buildMembershipCard(BuildContext context, Membership membership) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        membership.name ?? 'Gói thành viên',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        membership.formattedPrice,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: _getMembershipColor(membership.type ?? '').withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: _getMembershipColor(membership.type ?? ''),
                    ),
                  ),
                  child: Text(
                    membership.type ?? 'BASIC',
                    style: TextStyle(
                      color: _getMembershipColor(membership.type ?? ''),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // Description
            Text(
              membership.description ?? 'Không có mô tả',
              style: TextStyle(
                color: Colors.grey[600],
                height: 1.4,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Features
            _buildFeaturesList(membership),
            
            const SizedBox(height: 16),
            
            // Purchase Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: (membership.isActive == true)
                    ? () => _purchaseMembership(context, membership)
                    : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _getMembershipColor(membership.type ?? ''),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  (membership.isActive == true) ? 'Mua gói này' : 'Không khả dụng',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeaturesList(Membership membership) {
    final features = [
      'Thời hạn: ${membership.durationMonths ?? 1} tháng',
      'Tối đa ${membership.maxProperties ?? 0} tin đăng',
      'Tin nổi bật: ${membership.featuredProperties ?? 0}',
      if (membership.multipleImages == true) 'Đăng nhiều ảnh',
      if (membership.contactInfoDisplay == true) 'Hiển thị thông tin liên hệ',
      if (membership.prioritySupport == true) 'Hỗ trợ ưu tiên',
      if (membership.analyticsAccess == true) 'Truy cập thống kê',
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Tính năng:',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        ...features.map((feature) => Padding(
          padding: const EdgeInsets.only(bottom: 4),
          child: Row(
            children: [
              Icon(
                Icons.check_circle,
                size: 16,
                color: Colors.green,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  feature,
                  style: const TextStyle(fontSize: 14),
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }

  Color _getMembershipColor(String type) {
    switch (type.toUpperCase()) {
      case 'BASIC':
        return Colors.blue;
      case 'ADVANCED':
        return Colors.orange;
      case 'PREMIUM':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  void _purchaseMembership(BuildContext context, Membership membership) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PaymentScreen(membership: membership),
      ),
    );
  }


}
