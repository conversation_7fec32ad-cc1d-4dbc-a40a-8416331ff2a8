import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../providers/dashboard_provider.dart';
import '../../models/dashboard.dart';
import '../membership/membership_screen.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final dashboardProvider = Provider.of<DashboardProvider>(context, listen: false);
      dashboardProvider.loadDashboard();
      dashboardProvider.loadBoostStatus();
      dashboardProvider.loadActiveBoosts();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('<PERSON>ảng điều khiển'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              final dashboardProvider = Provider.of<DashboardProvider>(context, listen: false);
              dashboardProvider.loadDashboard();
              dashboardProvider.loadBoostStatus();
              dashboardProvider.loadActiveBoosts();
            },
          ),
        ],
      ),
      body: Consumer<DashboardProvider>(
        builder: (context, dashboardProvider, child) {
          if (dashboardProvider.isLoading && dashboardProvider.dashboard == null) {
            return const Center(child: CircularProgressIndicator());
          }

          if (dashboardProvider.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Lỗi: ${dashboardProvider.errorMessage}',
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      dashboardProvider.loadDashboard();
                    },
                    child: const Text('Thử lại'),
                  ),
                ],
              ),
            );
          }

          final dashboard = dashboardProvider.dashboard;
          if (dashboard == null) {
            return const Center(
              child: Text('Không có dữ liệu dashboard'),
            );
          }

          return RefreshIndicator(
            onRefresh: () async {
              await dashboardProvider.loadDashboard();
              await dashboardProvider.loadBoostStatus();
              await dashboardProvider.loadActiveBoosts();
            },
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Membership Status Card
                  _buildMembershipCard(context, dashboard.membership),
                  
                  const SizedBox(height: 16),
                  
                  // Property Statistics
                  _buildPropertyStatsCard(dashboard.properties),
                  
                  const SizedBox(height: 16),
                  
                  // Monthly Usage
                  _buildMonthlyUsageCard(dashboard.monthlyUsage),
                  
                  const SizedBox(height: 16),
                  
                  // Active Boosts
                  _buildActiveBoostsCard(dashboardProvider.activeBoosts),
                  
                  const SizedBox(height: 16),
                  
                  // Suggestions
                  if (dashboard.suggestions.isNotEmpty)
                    _buildSuggestionsCard(context, dashboard.suggestions),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildMembershipCard(BuildContext context, MembershipInfo membership) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  membership.hasActiveMembership ? Icons.verified : Icons.warning,
                  color: membership.hasActiveMembership ? Colors.green : Colors.orange,
                ),
                const SizedBox(width: 8),
                Text(
                  'Trạng thái thành viên',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(membership.message),
            if (membership.membershipType != null) ...[
              const SizedBox(height: 4),
              Text('Loại: ${membership.membershipType}'),
            ],
            if (membership.expiryDate != null) ...[
              const SizedBox(height: 4),
              Text('Hết hạn: ${DateFormat('dd/MM/yyyy').format(membership.expiryDate!)}'),
            ],
            if (!membership.hasActiveMembership) ...[
              const SizedBox(height: 12),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const MembershipScreen(),
                    ),
                  );
                },
                child: const Text('Nâng cấp thành viên'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPropertyStatsCard(PropertyStats stats) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Thống kê bất động sản',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Tổng tin',
                    stats.totalProperties.toString(),
                    Icons.home,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Đã duyệt',
                    stats.approvedProperties.toString(),
                    Icons.check_circle,
                    Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Chờ duyệt',
                    stats.pendingProperties.toString(),
                    Icons.pending,
                    Colors.orange,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Từ chối',
                    stats.rejectedProperties.toString(),
                    Icons.cancel,
                    Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Lượt xem',
                    stats.totalViews.toString(),
                    Icons.visibility,
                    Colors.purple,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Liên hệ',
                    stats.totalContacts.toString(),
                    Icons.contact_phone,
                    Colors.teal,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMonthlyUsageCard(MonthlyUsage usage) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Sử dụng tháng ${usage.month}',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildUsageItem(
                    'Push Top',
                    usage.pushTopUsed.toString(),
                    Icons.trending_up,
                    Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildUsageItem(
                    'AI Content',
                    usage.aiContentUsed.toString(),
                    Icons.auto_awesome,
                    Colors.purple,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Reset: ${DateFormat('dd/MM/yyyy').format(usage.resetDateTime)}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUsageItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 8),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActiveBoostsCard(List<dynamic> activeBoosts) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Tin đang được đẩy',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            if (activeBoosts.isEmpty)
              const Text('Không có tin nào đang được đẩy')
            else
              Text('${activeBoosts.length} tin đang được đẩy'),
          ],
        ),
      ),
    );
  }

  Widget _buildSuggestionsCard(BuildContext context, List<Suggestion> suggestions) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Gợi ý',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            ...suggestions.map((suggestion) => _buildSuggestionItem(context, suggestion)),
          ],
        ),
      ),
    );
  }

  Widget _buildSuggestionItem(BuildContext context, Suggestion suggestion) {
    Color priorityColor = Colors.blue;
    if (suggestion.priority == 'high') priorityColor = Colors.red;
    if (suggestion.priority == 'medium') priorityColor = Colors.orange;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: priorityColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: priorityColor.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            suggestion.title,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: priorityColor,
            ),
          ),
          const SizedBox(height: 4),
          Text(suggestion.message),
          if (suggestion.action == 'upgrade') ...[
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const MembershipScreen(),
                  ),
                );
              },
              child: const Text('Nâng cấp ngay'),
            ),
          ],
        ],
      ),
    );
  }
}
