import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:intl/intl.dart';
import '../../providers/dashboard_provider.dart';
import '../../providers/property_provider.dart';
import '../../models/boost.dart';
import '../../models/property.dart';

class BoostScreen extends StatefulWidget {
  const BoostScreen({super.key});

  @override
  State<BoostScreen> createState() => _BoostScreenState();
}

class _BoostScreenState extends State<BoostScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final dashboardProvider = Provider.of<DashboardProvider>(context, listen: false);
      final propertyProvider = Provider.of<PropertyProvider>(context, listen: false);
      
      dashboardProvider.loadBoostStatus();
      dashboardProvider.loadBoostHistory();
      dashboardProvider.loadActiveBoosts();
      propertyProvider.loadUserProperties();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Push Top - Đẩy tin lên đầu'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Consumer2<DashboardProvider, PropertyProvider>(
        builder: (context, dashboardProvider, propertyProvider, child) {
          if (dashboardProvider.isLoading || propertyProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Boost Status Card
                if (dashboardProvider.boostStatus != null)
                  _buildBoostStatusCard(dashboardProvider.boostStatus!),
                
                const SizedBox(height: 16),
                
                // User Properties
                _buildUserPropertiesSection(
                  propertyProvider.userProperties,
                  dashboardProvider,
                ),
                
                const SizedBox(height: 16),
                
                // Active Boosts
                if (dashboardProvider.activeBoosts.isNotEmpty)
                  _buildActiveBoostsSection(dashboardProvider.activeBoosts),
                
                const SizedBox(height: 16),
                
                // Boost History
                if (dashboardProvider.boostHistory != null)
                  _buildBoostHistorySection(dashboardProvider.boostHistory!),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildBoostStatusCard(BoostStatus status) {
    return Card(
      color: status.canBoost ? Colors.green[50] : Colors.orange[50],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  status.canBoost ? Icons.trending_up : Icons.warning,
                  color: status.canBoost ? Colors.green : Colors.orange,
                ),
                const SizedBox(width: 8),
                Text(
                  'Trạng thái Push Top',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: status.canBoost ? Colors.green[700] : Colors.orange[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(status.message),
            const SizedBox(height: 4),
            Text('Số lần còn lại: ${status.remainingBoosts}'),
            if (status.nextResetDate != null) ...[
              const SizedBox(height: 4),
              Text(
                'Reset vào: ${DateFormat('dd/MM/yyyy').format(status.nextResetDate!)}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildUserPropertiesSection(
    List<Property> properties,
    DashboardProvider dashboardProvider,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Tin đăng của bạn',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            if (properties.isEmpty)
              const Text('Bạn chưa có tin đăng nào')
            else
              ...properties.map((property) => _buildPropertyItem(
                property,
                dashboardProvider,
              )),
          ],
        ),
      ),
    );
  }

  Widget _buildPropertyItem(Property property, DashboardProvider dashboardProvider) {
    final isActive = dashboardProvider.activeBoosts
        .any((boost) => boost.propertyId == property.id);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isActive ? Colors.green[50] : Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isActive ? Colors.green : Colors.grey.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  property.title ?? 'Không có tiêu đề',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              if (isActive)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'ĐANG BOOST',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            property.formattedPrice,
            style: TextStyle(
              color: Theme.of(context).primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: Text(
                  '${property.district}, ${property.city}',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ),
              ElevatedButton.icon(
                onPressed: isActive ? null : () => _pushToTop(property, dashboardProvider),
                icon: const Icon(Icons.trending_up, size: 16),
                label: const Text('Push Top'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  textStyle: const TextStyle(fontSize: 12),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActiveBoostsSection(List<dynamic> activeBoosts) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Tin đang được đẩy (${activeBoosts.length})',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...activeBoosts.map((boost) => _buildActiveBoostItem(boost)),
          ],
        ),
      ),
    );
  }

  Widget _buildActiveBoostItem(dynamic boost) {
    // Handle both BoostHistoryItem and dynamic objects
    final propertyId = boost.propertyId ?? boost['propertyId'];
    final propertyTitle = boost.propertyTitle ?? boost['propertyTitle'] ?? 'Tin đăng #$propertyId';
    final expiresAt = boost.expiresAt ?? DateTime.parse(boost['expiresAt']);
    final remainingTime = expiresAt.difference(DateTime.now());

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.green[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  propertyTitle,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4),
                Text(
                  'Còn lại: ${_formatDuration(remainingTime)}',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.trending_up,
            color: Colors.green,
          ),
        ],
      ),
    );
  }

  Widget _buildBoostHistorySection(BoostHistory history) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Lịch sử Push Top',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            if (history.content.isEmpty)
              const Text('Chưa có lịch sử push top')
            else
              ...history.content.take(5).map((item) => _buildHistoryItem(item)),
            if (history.content.length > 5) ...[
              const SizedBox(height: 8),
              TextButton(
                onPressed: () {
                  // TODO: Navigate to full history
                },
                child: const Text('Xem tất cả'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildHistoryItem(BoostHistoryItem item) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.propertyTitle,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4),
                Text(
                  'Boost: ${DateFormat('dd/MM/yyyy HH:mm').format(item.boostedAt)}',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
                Text(
                  'Chi phí: ${item.cost.toStringAsFixed(0)} VND',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getStatusColor(item.status).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: _getStatusColor(item.status)),
            ),
            child: Text(
              _getStatusText(item.status),
              style: TextStyle(
                color: _getStatusColor(item.status),
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _pushToTop(Property property, DashboardProvider dashboardProvider) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Push Top'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Bạn có muốn đẩy tin "${property.title}" lên đầu danh sách?'),
              const SizedBox(height: 8),
              const Text(
                'Tin sẽ được hiển thị ở vị trí đầu tiên trong 24 giờ.',
                style: TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Hủy'),
            ),
            ElevatedButton(
              onPressed: () => _confirmPushTop(property, dashboardProvider),
              child: const Text('Push Top'),
            ),
          ],
        );
      },
    );
  }

  void _confirmPushTop(Property property, DashboardProvider dashboardProvider) async {
    Navigator.of(context).pop(); // Close dialog
    
    final success = await dashboardProvider.pushPropertyToTop(property.id!);

    if (success) {
      Fluttertoast.showToast(
        msg: "Đã push tin lên đầu thành công!",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
      );
    } else {
      Fluttertoast.showToast(
        msg: dashboardProvider.errorMessage ?? "Push top thất bại",
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
      );
    }
  }

  String _formatDuration(Duration duration) {
    if (duration.inDays > 0) {
      return '${duration.inDays} ngày';
    } else if (duration.inHours > 0) {
      return '${duration.inHours} giờ';
    } else if (duration.inMinutes > 0) {
      return '${duration.inMinutes} phút';
    } else {
      return 'Sắp hết hạn';
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toUpperCase()) {
      case 'ACTIVE':
        return Colors.green;
      case 'EXPIRED':
        return Colors.red;
      case 'CANCELLED':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(String status) {
    switch (status.toUpperCase()) {
      case 'ACTIVE':
        return 'ĐANG HOẠT ĐỘNG';
      case 'EXPIRED':
        return 'HẾT HẠN';
      case 'CANCELLED':
        return 'ĐÃ HỦY';
      default:
        return status;
    }
  }
}
