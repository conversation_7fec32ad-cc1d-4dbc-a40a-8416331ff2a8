import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fluttertoast/fluttertoast.dart';
import '../../providers/payment_provider.dart';
import '../../providers/dashboard_provider.dart';
import '../../models/membership.dart';
import '../../models/payment.dart';
import '../../services/stripe_service.dart';
import 'payment_webview_screen.dart';

class PaymentScreen extends StatefulWidget {
  final Membership membership;

  const PaymentScreen({
    super.key,
    required this.membership,
  });

  @override
  State<PaymentScreen> createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen> {
  String? _selectedPaymentMethod;
  String _selectedCurrency = 'VND';
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final paymentProvider = Provider.of<PaymentProvider>(context, listen: false);
      paymentProvider.loadPaymentMethods();
      paymentProvider.loadExchangeRate('USD', 'VND');
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Thanh toán'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Consumer<PaymentProvider>(
        builder: (context, paymentProvider, child) {
          if (paymentProvider.isLoading && paymentProvider.paymentMethods.isEmpty) {
            return const Center(child: CircularProgressIndicator());
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Membership Summary
                _buildMembershipSummary(),
                
                const SizedBox(height: 24),
                
                // Currency Selection
                _buildCurrencySelection(paymentProvider),
                
                const SizedBox(height: 24),
                
                // Payment Methods
                _buildPaymentMethods(paymentProvider),
                
                const SizedBox(height: 32),
                
                // Payment Button
                _buildPaymentButton(paymentProvider),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildMembershipSummary() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Chi tiết đơn hàng',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.membership.name ?? 'Gói thành viên',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        widget.membership.description ?? 'Không có mô tả',
                        style: TextStyle(
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Thời hạn: ${widget.membership.durationMonths ?? 1} tháng',
                        style: const TextStyle(fontWeight: FontWeight.w500),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: _getMembershipColor(widget.membership.type ?? '').withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(color: _getMembershipColor(widget.membership.type ?? '')),
                  ),
                  child: Text(
                    widget.membership.type ?? 'BASIC',
                    style: TextStyle(
                      color: _getMembershipColor(widget.membership.type ?? ''),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            
            const Divider(height: 24),
            
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Tổng cộng:',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Consumer<PaymentProvider>(
                  builder: (context, paymentProvider, child) {
                    final price = _selectedCurrency == 'VND'
                        ? (widget.membership.price ?? 0.0)
                        : paymentProvider.convertPrice(widget.membership.price ?? 0.0, 'VND', _selectedCurrency);

                    return Text(
                      paymentProvider.formatPrice(price, _selectedCurrency),
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).primaryColor,
                      ),
                    );
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrencySelection(PaymentProvider paymentProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Tiền tệ',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            
            Row(
              children: [
                Expanded(
                  child: RadioListTile<String>(
                    title: const Text('VND'),
                    subtitle: Text(paymentProvider.formatPrice(widget.membership.price ?? 0.0, 'VND')),
                    value: 'VND',
                    groupValue: _selectedCurrency,
                    onChanged: (value) {
                      setState(() {
                        _selectedCurrency = value!;
                      });
                    },
                  ),
                ),
                Expanded(
                  child: RadioListTile<String>(
                    title: const Text('USD'),
                    subtitle: Text(
                      paymentProvider.formatPrice(
                        paymentProvider.convertPrice(widget.membership.price ?? 0.0, 'VND', 'USD'),
                        'USD',
                      ),
                    ),
                    value: 'USD',
                    groupValue: _selectedCurrency,
                    onChanged: (value) {
                      setState(() {
                        _selectedCurrency = value!;
                      });
                    },
                  ),
                ),
              ],
            ),
            
            if (paymentProvider.exchangeRate != null)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text(
                  'Tỷ giá: 1 USD = ${paymentProvider.exchangeRate!.rate.toStringAsFixed(0)} VND',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentMethods(PaymentProvider paymentProvider) {
    final activeMethods = paymentProvider.getActivePaymentMethods();
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Phương thức thanh toán',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            if (activeMethods.isEmpty)
              const Text('Không có phương thức thanh toán nào')
            else
              ...activeMethods.map((method) => _buildPaymentMethodTile(method)),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentMethodTile(PaymentMethod method) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: RadioListTile<String>(
        title: Row(
          children: [
            Icon(
              PaymentMethodIcons.getIcon(method.type),
              color: PaymentMethodIcons.getColor(method.type),
              size: 24,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    method.name,
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                  Text(
                    method.description,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        value: method.id,
        groupValue: _selectedPaymentMethod,
        onChanged: (value) {
          setState(() {
            _selectedPaymentMethod = value;
          });
        },
      ),
    );
  }

  Widget _buildPaymentButton(PaymentProvider paymentProvider) {
    return ElevatedButton(
      onPressed: (_selectedPaymentMethod != null && !_isProcessing) 
          ? () => _processPayment(paymentProvider)
          : null,
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      child: _isProcessing
          ? const SizedBox(
              height: 20,
              width: 20,
              child: CircularProgressIndicator(strokeWidth: 2),
            )
          : Text(
              'Thanh toán ${paymentProvider.formatPrice(
                _selectedCurrency == 'VND'
                    ? (widget.membership.price ?? 0.0)
                    : paymentProvider.convertPrice(widget.membership.price ?? 0.0, 'VND', _selectedCurrency),
                _selectedCurrency,
              )}',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
    );
  }

  Future<void> _processPayment(PaymentProvider paymentProvider) async {
    if (_selectedPaymentMethod == null) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      final paymentMethod = paymentProvider.getPaymentMethodById(_selectedPaymentMethod!);
      if (paymentMethod == null) {
        throw Exception('Payment method not found');
      }

      final price = _selectedCurrency == 'VND'
          ? (widget.membership.price ?? 0.0)
          : paymentProvider.convertPrice(widget.membership.price ?? 0.0, 'VND', _selectedCurrency);

      final response = await paymentProvider.purchaseMembership(
        widget.membership,
        _selectedPaymentMethod!,
        currency: _selectedCurrency,
        metadata: {
          'price': price,
          'originalPrice': widget.membership.price ?? 0.0,
          'currency': _selectedCurrency,
        },
      );

      if (response != null && response.success) {
        if (paymentMethod.type == 'STRIPE' && response.clientSecret != null) {
          // Handle Stripe payment
          await _handleStripePayment(response.clientSecret!);
        } else if (response.paymentUrl != null) {
          // Handle other payment methods with redirect
          await _handleWebPayment(response.paymentUrl!);
        } else {
          // Direct payment success
          _showPaymentSuccess();
        }
      } else {
        throw Exception(response?.message ?? 'Payment failed');
      }
    } catch (e) {
      _showPaymentError(e.toString());
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  Future<void> _handleStripePayment(String clientSecret) async {
    try {
      final stripeService = StripeService();
      
      // Initialize Stripe if not already done
      if (!stripeService.isInitialized) {
        await stripeService.initialize('pk_test_your_stripe_publishable_key');
      }

      final success = await stripeService.processPayment(
        clientSecret: clientSecret,
        context: context,
      );

      if (success) {
        _showPaymentSuccess();
      }
    } catch (e) {
      _showPaymentError('Stripe payment failed: $e');
    }
  }

  Future<void> _handleWebPayment(String paymentUrl) async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PaymentWebViewScreen(
          paymentUrl: paymentUrl,
          title: 'Thanh toán ${widget.membership.name}',
        ),
      ),
    );

    if (result == true) {
      _showPaymentSuccess();
    } else if (result == false) {
      _showPaymentError('Payment was cancelled or failed');
    }
  }

  void _showPaymentSuccess() {
    Fluttertoast.showToast(
      msg: "Thanh toán thành công!",
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.BOTTOM,
    );

    // Refresh dashboard
    Provider.of<DashboardProvider>(context, listen: false).loadDashboard();

    // Go back to previous screen
    Navigator.of(context).pop();
  }

  void _showPaymentError(String error) {
    Fluttertoast.showToast(
      msg: "Thanh toán thất bại: $error",
      toastLength: Toast.LENGTH_LONG,
      gravity: ToastGravity.BOTTOM,
    );
  }

  Color _getMembershipColor(String type) {
    switch (type.toUpperCase()) {
      case 'BASIC':
        return Colors.blue;
      case 'ADVANCED':
        return Colors.orange;
      case 'PREMIUM':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }
}
