import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:fluttertoast/fluttertoast.dart';
import '../../providers/dashboard_provider.dart';
import '../../models/ai_content.dart';

class AiContentScreen extends StatefulWidget {
  const AiContentScreen({super.key});

  @override
  State<AiContentScreen> createState() => _AiContentScreenState();
}

class _AiContentScreenState extends State<AiContentScreen> {
  final _formKey = GlobalKey<FormState>();
  final _descriptionController = TextEditingController();
  final _imageDescriptionController = TextEditingController();
  
  AiContentResponse? _generatedContent;

  @override
  void dispose() {
    _descriptionController.dispose();
    _imageDescriptionController.dispose();
    super.dispose();
  }

  Future<void> _generateContent() async {
    if (_formKey.currentState!.validate()) {
      final dashboardProvider = Provider.of<DashboardProvider>(context, listen: false);
      
      final response = await dashboardProvider.generateAiContent(
        _descriptionController.text.trim(),
        imageDescription: _imageDescriptionController.text.trim().isNotEmpty 
            ? _imageDescriptionController.text.trim() 
            : null,
      );

      if (response != null) {
        setState(() {
          _generatedContent = response;
        });
        
        Fluttertoast.showToast(
          msg: "Tạo nội dung AI thành công!",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
        );
      } else {
        Fluttertoast.showToast(
          msg: dashboardProvider.errorMessage ?? "Tạo nội dung thất bại",
          toastLength: Toast.LENGTH_LONG,
          gravity: ToastGravity.BOTTOM,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tạo nội dung AI'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Info Card
              Card(
                color: Colors.blue[50],
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.auto_awesome, color: Colors.blue),
                          const SizedBox(width: 8),
                          Text(
                            'Tạo nội dung AI',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.blue[700],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'Sử dụng AI để tạo mô tả SEO tối ưu, gợi ý tiêu đề và từ khóa cho bất động sản của bạn.',
                        style: TextStyle(fontSize: 14),
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Description Input
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Mô tả bất động sản *',
                  hintText: 'Ví dụ: Căn hộ 2 phòng ngủ tại quận 1, gần chợ Bến Thành, view đẹp',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Vui lòng nhập mô tả bất động sản';
                  }
                  if (value.length < 20) {
                    return 'Mô tả phải có ít nhất 20 ký tự';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              // Image Description Input
              TextFormField(
                controller: _imageDescriptionController,
                decoration: const InputDecoration(
                  labelText: 'Mô tả hình ảnh (tùy chọn)',
                  hintText: 'Ví dụ: Phòng khách rộng rãi, view thành phố tuyệt đẹp',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
              
              const SizedBox(height: 24),
              
              // Generate Button
              Consumer<DashboardProvider>(
                builder: (context, dashboardProvider, child) {
                  return ElevatedButton.icon(
                    onPressed: dashboardProvider.isLoading ? null : _generateContent,
                    icon: dashboardProvider.isLoading 
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.auto_awesome),
                    label: Text(
                      dashboardProvider.isLoading ? 'Đang tạo...' : 'Tạo nội dung AI',
                    ),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  );
                },
              ),
              
              const SizedBox(height: 24),
              
              // Generated Content
              if (_generatedContent != null) ...[
                const Divider(),
                const SizedBox(height: 16),
                _buildGeneratedContent(_generatedContent!),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGeneratedContent(AiContentResponse response) {
    final content = response.content;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Nội dung được tạo',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        
        const SizedBox(height: 16),
        
        // SEO Score
        Card(
          color: _getSeoScoreColor(content.seoScore).withValues(alpha: 0.1),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(
                  Icons.score,
                  color: _getSeoScoreColor(content.seoScore),
                ),
                const SizedBox(width: 8),
                Text(
                  'Điểm SEO: ${content.seoScore}/100',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _getSeoScoreColor(content.seoScore),
                  ),
                ),
              ],
            ),
          ),
        ),
        
        const SizedBox(height: 16),
        
        // SEO Optimized Description
        _buildContentSection(
          'Mô tả tối ưu SEO',
          content.seoOptimizedDescription,
          Icons.description,
        ),
        
        const SizedBox(height: 16),
        
        // Title Suggestions
        _buildTitleSuggestions(content.titleSuggestions),
        
        const SizedBox(height: 16),
        
        // Meta Description
        _buildContentSection(
          'Meta Description',
          content.metaDescription,
          Icons.tag,
        ),
        
        const SizedBox(height: 16),
        
        // Keywords
        _buildKeywords(content.keywords),
        
        const SizedBox(height: 16),
        
        // Usage Info
        Card(
          color: Colors.grey[100],
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(Icons.info, color: Colors.grey[600]),
                const SizedBox(width: 8),
                Text(
                  'Đã sử dụng ${response.usage.aiContentUsed} lần trong tháng ${response.usage.month}',
                  style: TextStyle(color: Colors.grey[600]),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildContentSection(String title, String content, IconData icon) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, size: 20),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.copy, size: 20),
                  onPressed: () {
                    Clipboard.setData(ClipboardData(text: content));
                    Fluttertoast.showToast(
                      msg: "Đã sao chép vào clipboard",
                      toastLength: Toast.LENGTH_SHORT,
                    );
                  },
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              content,
              style: const TextStyle(height: 1.4),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTitleSuggestions(List<String> titles) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.title, size: 20),
                const SizedBox(width: 8),
                const Text(
                  'Gợi ý tiêu đề',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...titles.asMap().entries.map((entry) {
              final index = entry.key;
              final title = entry.value;
              return Container(
                margin: const EdgeInsets.only(bottom: 8),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    CircleAvatar(
                      radius: 12,
                      backgroundColor: Theme.of(context).primaryColor,
                      child: Text(
                        '${index + 1}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(title),
                    ),
                    IconButton(
                      icon: const Icon(Icons.copy, size: 18),
                      onPressed: () {
                        Clipboard.setData(ClipboardData(text: title));
                        Fluttertoast.showToast(
                          msg: "Đã sao chép tiêu đề",
                          toastLength: Toast.LENGTH_SHORT,
                        );
                      },
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildKeywords(List<String> keywords) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.label, size: 20),
                const SizedBox(width: 8),
                const Text(
                  'Từ khóa',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: keywords.map((keyword) {
                return Chip(
                  label: Text(keyword),
                  backgroundColor: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                  labelStyle: TextStyle(
                    color: Theme.of(context).primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Color _getSeoScoreColor(int score) {
    if (score >= 80) return Colors.green;
    if (score >= 60) return Colors.orange;
    return Colors.red;
  }
}
