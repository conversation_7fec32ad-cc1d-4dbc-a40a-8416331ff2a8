# real_estate_flutter_app

# Real Estate Flutter App

A comprehensive Flutter mobile application for real estate management that integrates with the backend API running on localhost:8080.

## 🚀 Features

### 🔐 Authentication
- User login and registration
- JWT token-based authentication
- Role-based access control (User/Admin)
- Secure token storage using SharedPreferences

### 🏠 Property Management
- Browse all properties with pagination
- Search and filter properties
- View detailed property information
- Create new property listings (authenticated users)
- Manage user's own properties
- Property image gallery
- Contact property owners

### 👤 User Features
- User profile management
- Notifications system
- Favorites/Saved properties
- Payment history
- Membership management

### 📱 UI/UX
- Modern Material Design 3
- Responsive layout
- Vietnamese language support
- Loading states and error handling
- Toast notifications
- Pull-to-refresh functionality

## 🛠️ Tech Stack

- **Framework**: Flutter 3.8+
- **State Management**: Provider
- **HTTP Client**: http package
- **Local Storage**: SharedPreferences
- **Image Caching**: cached_network_image
- **UI Components**: Material Design 3
- **JSON Serialization**: json_annotation + json_serializable

## 📋 Prerequisites

1. **Flutter SDK** (3.8 or higher)
2. **Dart SDK** (3.0 or higher)
3. **Android Studio** or **VS Code** with Flutter extensions
4. **Backend API** running on localhost:8080

## 🔧 Installation

1. **Clone the repository**
   ```bash
   cd /path/to/your/project
   git clone <repository-url>
   cd real_estate_flutter_app
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Generate JSON serialization code**
   ```bash
   flutter packages pub run build_runner build
   ```

4. **Run the app**
   ```bash
   flutter run
   ```

## 🌐 API Integration

The app integrates with the backend API at `http://localhost:8080/api/v1`. Make sure your backend server is running before using the app.

### API Endpoints Used:
- **Authentication**: `/auth/login`, `/auth/register`
- **Properties**: `/properties`, `/properties/search`, `/properties/my-properties`
- **Categories**: `/categories`
- **Memberships**: `/memberships`
- **Notifications**: `/notifications`
- **User Profile**: `/users/profile`

## 🔑 Demo Accounts

The app includes demo account information for testing:

### Admin Account
- **Username**: admin
- **Password**: admin123
- **Email**: <EMAIL>
- **Role**: ADMIN

### Test User Account
- **Username**: testuser2
- **Password**: password123
- **Email**: <EMAIL>
- **Role**: USER

## 🧪 Testing API Connection

Run the included API test script:
```bash
dart test_api.dart
```

This will verify that the backend API is accessible and responding correctly.

## 🔧 Configuration

### API Base URL
Update the base URL in `lib/constants/api_constants.dart` if your backend runs on a different address:
```dart
static const String baseUrl = 'http://your-api-url:port/api/v1';
```

## 📱 Supported Platforms

- ✅ Android
- ✅ iOS
- ✅ Web (with CORS configuration)
- ✅ macOS
- ✅ Windows
- ✅ Linux

## 🐛 Troubleshooting

### Common Issues:

1. **API Connection Failed**
   - Ensure backend server is running on localhost:8080
   - Check network connectivity
   - Verify API endpoints are accessible

2. **Build Errors**
   - Run `flutter clean` and `flutter pub get`
   - Regenerate JSON serialization: `flutter packages pub run build_runner build --delete-conflicting-outputs`

3. **Authentication Issues**
   - Clear app data or reinstall
   - Check token expiration
   - Verify credentials with demo accounts

---

**Note**: Make sure your backend API is running on localhost:8080 before using the app. The app includes comprehensive error handling and user feedback for a smooth user experience.
