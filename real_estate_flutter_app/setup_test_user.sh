#!/bin/bash

echo "🚀 Setting up test user with membership..."

# Login to get token
echo "📝 Logging in as testuser2..."
LOGIN_RESPONSE=$(curl -s -X POST "http://localhost:8080/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser2",
    "password": "password123"
  }')

TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"token":"[^"]*' | cut -d'"' -f4)

if [ -z "$TOKEN" ]; then
  echo "❌ Login failed!"
  echo "Response: $LOGIN_RESPONSE"
  exit 1
fi

echo "✅ Login successful! Token: ${TOKEN:0:20}..."

# Check current membership
echo "🔍 Checking current membership..."
MEMBERSHIP_RESPONSE=$(curl -s -X GET "http://localhost:8080/api/v1/memberships/my-membership" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json")

echo "Current membership: $MEMBERSHIP_RESPONSE"

# Get available memberships
echo "📋 Getting available memberships..."
MEMBERSHIPS_RESPONSE=$(curl -s -X GET "http://localhost:8080/api/v1/memberships" \
  -H "Content-Type: application/json")

echo "Available memberships: $MEMBERSHIPS_RESPONSE"

# Check if user already has FREE membership (auto-assigned on registration)
echo "🔍 Checking if user has FREE membership..."
if echo "$MEMBERSHIP_RESPONSE" | grep -q "FREE"; then
  echo "✅ User already has FREE membership!"
  echo "🎉 User can create up to 10 properties for free"
else
  echo "⚠️  User doesn't have FREE membership"
  echo "💡 FREE membership should be auto-assigned on registration"
  echo "📝 User can still try to create properties - backend will handle limits"
fi

echo ""
echo "🧪 Test Instructions:"
echo "1. Open Flutter app"
echo "2. Login with testuser2/password123"
echo "3. Check Dashboard - should show FREE membership"
echo "4. Try to create properties (up to 10 free)"
echo "5. When limit reached, app will suggest upgrading"
echo "6. Use Membership screen to upgrade if needed"
echo ""
echo "🔗 Useful endpoints:"
echo "- Dashboard: GET /api/v1/dashboard"
echo "- My Membership: GET /api/v1/memberships/my-membership"
echo "- Create Property: POST /api/v1/properties"
